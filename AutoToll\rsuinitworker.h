#ifndef RSUINITWORKER_H
#define RSUINITWORKER_H

#include <QObject>
#include <QString>

class CRsuDev;

/**
 * @brief 天线重新初始化工作类
 * @note 用于异步执行天线的重新初始化操作，避免阻塞主线程
 * <AUTHOR>
 * @date 2024
 */
class RsuInitWorker : public QObject
{
    Q_OBJECT
    
public:
    /**
     * @brief 构造函数
     * @param pRsuDev 天线设备指针
     * @param nRsuIndex 天线索引（0-前天线，1-后天线）
     * @param parent 父对象
     */
    explicit RsuInitWorker(CRsuDev *pRsuDev, int nRsuIndex, QObject *parent = 0);
    
    ~RsuInitWorker();
    
public slots:
    /**
     * @brief 执行天线重新初始化
     * @note 该函数在工作线程中执行
     *       执行步骤：关闭天线 -> 等待 -> 打开天线 -> 等待 -> 重新初始化
     */
    void doInit();
    
signals:
    /**
     * @brief 初始化完成信号
     * @param nRsuIndex 天线索引
     * @param bSuccess 是否成功
     * @param sError 错误信息（成功时为空）
     */
    void initFinished(int nRsuIndex, bool bSuccess, const QString &sError);
    
private:
    CRsuDev *m_pRsuDev;        // 天线设备指针
    int m_nRsuIndex;           // 天线索引
};

#endif // RSUINITWORKER_H