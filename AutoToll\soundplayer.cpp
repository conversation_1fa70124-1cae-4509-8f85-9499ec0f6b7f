#include "soundplayer.h"
#include "ilogmsg.h"
#include <QApplication>
#include <QStringList>
#include <QFile>
#include <QtEndian>

//小节位，对32位正数表达的最大整数来说，最大节权万亿就够了(Z代表万亿）
const char *chnUnitSetion[] = {"", "W", "Y", "Z"};
//每个小节里面的独立计数
const char *chnUnitChar[] = {"", "S", "B", "Q"};

//wav文件结构体
#pragma pack(push, 1)
typedef struct _default_wave_head_{
    char ChunkID[4]; // RIFF
    qint32 ChunkSize; // size=filelen-8B
    char ChunkFormat[4]; // WAVE
    // subchunk-'fmt'
    char SubChunk1[4]; // 'fmt'
    qint32 SubChunk1Size; //16
    // PCMWAVEFORMAT   最简单的固定格式
    qint16 AudioFormat; //01
    qint16 NumChannels; //02
    int SampleRate;    //44100
    int ByteRate;      //176400
    qint16 BlockAlign;  //02
    qint16 BitsperSample;  //01
    char DataID[4];     //"data";
    qint32 DataSize;       //data区域大小
}DEFWAVHEAD;
#pragma pack(pop)

//每个小节内单独处理
void SectionToChinese(quint32 section, QString &chnStr)
{
    QString strIns="";
    //当前小节内的当前个数的独立计数的权位
    qint32 unitPos = 0;
    //先设置zero为true，为了测试规则二，两个相连的0只留一个
    bool zero = true;
    while (section > 0) {
        int v = section % 10;
        if (v == 0) {
            //当不是两个0相连的时候或者 添加0在数字中
            if (!zero) {
                //当出现一个0的时候就设置zero为true，当下一个还是0的时候就不添加0了
                zero = true;
                chnStr.insert(0, QString::number(v));
            }
        } else {
            //当出现一个不是0的数字的时候就设置当前的zero标志为false表示下次遇到0的时候还是要添加
            zero = false;
            strIns = QString::number(v);
            strIns += chnUnitChar[unitPos];
            //将这个strIns插入到总的字符串的开始的位置
            chnStr.insert(0, strIns);
        }
        //权位增加
        unitPos++;
        //小节值除以10
        section /= 10;
    }
}

//转换为汉字数字（带位权）
void NumberToChinese(quint32 num, QString &chnStr)
{
    int unitPos = 0;   //小节的位置
    bool needZero = false;  //初始默认规则3不需要0
    while (num > 0) {
        QString strIns="";
        unsigned int section = num % 10000;
        if (needZero) {  //满足规则3需要添零，根据后面的语句是否修改了needZero来检测是否添加0
            chnStr.insert(0, QChar('0'));
        }
        SectionToChinese(section, strIns);
        //检测当前section的的是否是0，如果是0的话，则添加空字符串的节权位，否则添加其他的
        strIns += (section != 0) ? chnUnitSetion[unitPos] : chnUnitSetion[0];
        chnStr.insert(0, strIns);
        //当满足小节内的值小于1000且值大于0的时候表示当前小节的千位是一个0，如果前面一小节还有值的时候则添0
        needZero = (section < 1000) && (section > 0);
        num /= 10000;
        unitPos++;
    }
}



CSoundPlayer::CSoundPlayer()
{

}


////出口播放收费金额(单位：元）
//bool CSoundPlayer::Play_Money(int nMoney)
//{
//    m_MediaObject.clear();
//    m_MediaObject.setTickInterval(30);
//    m_MediaObject.setTransitionTime(0);
//    m_MediaObject.enqueue(Phonon::MediaSource(QString(QCoreApplication::applicationDirPath()+"/wav/"+"1.wav")));
//    m_MediaObject.enqueue(Phonon::MediaSource(QString(QCoreApplication::applicationDirPath()+"/wav/"+"qian.wav")));
//    m_MediaObject.enqueue(Phonon::MediaSource(QString(QCoreApplication::applicationDirPath()+"/wav/"+"3.wav")));
//    m_MediaObject.enqueue(Phonon::MediaSource(QString(QCoreApplication::applicationDirPath()+"/wav/"+"bai.wav")));

//    m_MediaObject.enqueue(Phonon::MediaSource(QString(QCoreApplication::applicationDirPath()+"/wav/"+"7.wav")));
//    m_MediaObject.enqueue(Phonon::MediaSource(QString(QCoreApplication::applicationDirPath()+"/wav/"+"shi.wav")));
//    m_MediaObject.enqueue(Phonon::MediaSource(QString(QCoreApplication::applicationDirPath()+"/wav/"+"9.wav")));
//    m_MediaObject.enqueue(Phonon::MediaSource(QString(QCoreApplication::applicationDirPath()+"/wav/"+"yuan.wav")));

//    m_MediaObject.play();

//    return true;
//}

//播放收费金额(单位：元）
bool CSoundPlayer::Play_TollMoney(int nMoney)
{
    //只处理1亿内数字
    if (nMoney > 100000000) return false;
    if (!QSound::isAvailable()){
        return false;
    }
    //转换成汉字表达法
    QString sNumChn = "";
    NumberToChinese(nMoney, sNumChn);
    //开始拼声音文件。
    if (sNumChn.length() == 0){
        return false;
    }
    QStringList lstFileName = QStringList()<<"txf.wav";
    for(int i=0; i<sNumChn.length(); i++){
        lstFileName << QString("%1.wav").arg(sNumChn[i]);
    }
    lstFileName<< "yuan.wav";
    //根据列表，生成单个wav文件
    QString sWaveFile("money.wav");
    if (!MakeWavFile(lstFileName, sWaveFile)){
        return false;
    }
    return Play_SndFile(sWaveFile);
}

//由文件输入流中复制字节到输出文件
int CSoundPlayer::CopyWriteToFile(QDataStream &in, QDataStream &out, int nDatasize)
{
    int nWritedSize = 0;
    char readbuf[1024];
    int nRemained = nDatasize;
    while(nWritedSize < nDatasize){
        nRemained = nDatasize - nWritedSize;
        int nNeedRead = nRemained > 1024 ? 1024 : nRemained;
        //读入缓冲数据
        int nRet = in.readRawData(readbuf, nNeedRead);
        if (nRet == 0){
            break;
        }
        //写入数据，确保全部写入
        int nBufIdx = 0;
        while(nRet > 0){
            int write_size = out.writeRawData(readbuf+nBufIdx, nRet);
            if (write_size == 0){
                break;
            }
            //移动buf指针
            nBufIdx +=  write_size;
            //计算未写入数量
            nRet -= write_size;
            //累加总数
            nWritedSize += write_size;
        }
    }
    //返回实际写入
    return nWritedSize;
}


//将多个wav文件合并成一个wav文件
bool CSoundPlayer::MakeWavFile(const QStringList &lstWaveFile, const QString &sOutFile)
{
    QString sDestFile = QString(QCoreApplication::applicationDirPath()+"/wav/"+sOutFile);
    QFile fDest(sDestFile);
    if (!fDest.open(QIODevice::WriteOnly | QIODevice::Truncate)){
        ErrorLog(QString("open file fail:%1").arg(sDestFile));
        return false;
    }
    DEFWAVHEAD waveheader;
    memcpy(waveheader.ChunkID, "RIFF", 4); // RIFF
    //先写入0
    waveheader.ChunkSize = 0;   // size=filelen-8B
    memcpy(waveheader.ChunkFormat, "WAVE", 4); // WAVE
    // subchunk-'fmt'
    memcpy(waveheader.SubChunk1, "fmt ", 4); // 'fmt'
    waveheader.SubChunk1Size = qToLittleEndian(16); //16
    // PCMWAVEFORMAT   最简单的固定格式
    waveheader.AudioFormat = qToLittleEndian(01); //01
    waveheader.NumChannels = qToLittleEndian(02); //01
    waveheader.SampleRate = qToLittleEndian(qint32(44100));    //32000
    waveheader.ByteRate = qToLittleEndian(qint32(176400));      //64000
    waveheader.BlockAlign = qToLittleEndian(04);  //02
    waveheader.BitsperSample = qToLittleEndian(16);  //01
    memcpy(waveheader.DataID, "data", 4);
    waveheader.DataSize = 0;
    //先写入文件头，移动文件指针
    QDataStream out(&fDest);
    out.writeRawData((char*)&waveheader, sizeof(waveheader));
    waveheader.ChunkSize = sizeof(waveheader) -8;
    //开始写入data结构
//    char databuf[1024];
    for(int i=0; i< lstWaveFile.size(); i++){
        QString sSrcFileName = QString(QCoreApplication::applicationDirPath()+"/wav/"+lstWaveFile[i]);
        TraceLog(QString("src: %1").arg(sSrcFileName));
        QFile fSrc(sSrcFileName);
        if (!fSrc.open(QIODevice::ReadOnly)){
            TraceLog(QString("read open fail: %1").arg(sSrcFileName));
            continue;
        }
        TraceLog(QString("read open succ: %1").arg(sSrcFileName));
        fSrc.seek(sizeof(waveheader) - 4);
        QDataStream in(&fSrc);
        int nDatasize;
        in.readRawData((char*)&nDatasize, 4);
        nDatasize = qFromLittleEndian(nDatasize);
        CopyWriteToFile(in, out, nDatasize);
        waveheader.DataSize += nDatasize;
        waveheader.ChunkSize += nDatasize;
        fSrc.close();
    }
    //重新改写文件中某些大小
    fDest.seek(4);
    out.setByteOrder(QDataStream::LittleEndian);
    out << waveheader.ChunkSize;
    fDest.seek(sizeof(DEFWAVHEAD) - 4);
    out << waveheader.DataSize;
    fDest.close();
    return true;
}
//播放声音文件
bool CSoundPlayer::Play_SndFile(QString sFileName)
{
    if (!QSound::isAvailable()){
        return false;
    }
    QString sSoundFile = QString(QCoreApplication::applicationDirPath()+"/wav/"+sFileName);
    QSound::play(sSoundFile);
    return true;
}
