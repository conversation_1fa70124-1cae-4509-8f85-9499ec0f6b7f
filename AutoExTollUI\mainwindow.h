#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QDialog>
#include <QListWidget>

#include "forminsertcard.h"
#include "formoutofservice.h"
#include "formshowinvoice.h"
#include "formshowprompt.h"
#include "formshowtollmoney.h"
#include "formwaitvehicle.h"
#include "uiserver.h"

namespace Ui {
class MainWindow;
}

class MainWindow : public QDialog
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = 0);
    ~MainWindow();

public:
    enum FormType
    {
        FORM_OutOfService,
        FORM_WaitVehicle,
        FORM_InsertCard,
        FORM_ShowInvoice,

        FORM_ShowMoney,
        FORM_ShowPrompt,
        FORM_END
    };

private:
    Ui::MainWindow *ui;
    UIServer *m_pServer;
    //时钟ID
    //秒表，更新界面时间
    int m_tidSecond;
    //等待车辆
    FormWaitVehicle *m_pFrmWaitVeh;
    //提示插卡
    FormInsertCard *m_pFrmInsertCard;
    //进行提示
    FormShowPrompt *m_pFrmShowPrompt;
    //显示金额，提示扫码
    FormShowTollMoney *m_pFrmShowMoney;
    //提示票据
    FormShowInvoice *m_pFrmShowInvoice;
    //暂停服务
    FormOutOfService *m_pFrmOutOfService;

    //管理状态界面服务
    BaseForm **m_pBaseForms;

    QString m_laneName, m_stationName;
    QString m_sVlp, m_sVehClassName, m_sVehTypeName, m_sEnStation;
    quint32 m_nMoney;

protected slots:

    void OnLaneInfo(const QString stationName, const QString laneName);
    void OnHelpMsg(const QString stationName, const QString laneName, quint32 nHelpType,
                   const QString helpMsg);
    void OnTollInfo(const QString vlp, const QString vehClassName, const QString vehTypeName,
                    const QString enStationName, quint32 tollMoney, bool bBeforePay,
                    quint8 channelType);
    void OnTicketInfo(const QString vlp, const QString vehClassName, const QString vehTypeName,
                      const QString enStationName, quint32 tollMoney, quint8 channelType,
                      const QString qrCode);

public:
    //初始化
    bool Init();
    //去初始化
    bool Destroy();
    //初始化UI
    void InitUI();

protected:
    // QWidget interface
    void paintEvent(QPaintEvent *);
    void mouseDoubleClickEvent(QMouseEvent *);

    // QObject interface
    void timerEvent(QTimerEvent *);

    void ShowHelpForm(FormType FormType);

    void keyPressEvent(QKeyEvent *event);
};

#endif  // MAINWINDOW_H
