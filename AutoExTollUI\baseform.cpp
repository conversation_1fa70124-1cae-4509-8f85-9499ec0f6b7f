#include "baseform.h"

#include "globalutils.h"
#include "ilogmsg.h"
#include "wavfile.h"

BaseForm::BaseForm(QWidget *parent) : QWidget(parent)
{
    m_pSoundPlay = 0;

    m_pAudioOutput = new Phonon::AudioOutput(Phonon::MusicCategory, this);
    m_pSoundPlay = new Phonon::MediaObject(this);

    m_pLoopTimer = new QTimer();

    connect(m_pLoopTimer, SIGNAL(timeout()), this, SLOT(onLoopPlay()));

    Phonon::createPath(m_pSoundPlay, m_pAudioOutput);

    //循环播放间隔时间
    m_nLoopInterval = 0;
    //循环播放次数
    m_nLoopTimes = 0;
}

BaseForm::~BaseForm()
{
    if (m_pLoopTimer) {
        m_pLoopTimer->stop();
        delete m_pLoopTimer;
    }
    if (m_pSoundPlay) {
        m_pSoundPlay->stop();
        delete m_pSoundPlay;
    }
    if (m_pAudioOutput) {
        delete m_pAudioOutput;
    }
}

QPixmap BaseForm::scaledPixmap(const QPixmap &src, int width, int height)
{
    return src.scaled(width, (height == 0 ? width : height), Qt::IgnoreAspectRatio,
                      Qt::SmoothTransformation);
}

QPixmap BaseForm::generatePixmap(const QPixmap &src, const int &radius, bool bRound)
{
    // 无效图片不处理
    if (src.isNull()) {
        return src;
    }

    // 压缩图片
    QPixmap pixmap = scaledPixmap(src, radius * 2);

    QPixmap dest(2 * radius, 2 * radius);
    dest.fill(Qt::transparent);
    QPainter painter(&dest);
    // 抗锯齿 + 平滑边缘处理
    painter.setRenderHints(QPainter::Antialiasing, true);
    painter.setRenderHints(QPainter::SmoothPixmapTransform, true);
    // 裁剪为圆角
    QPainterPath path;
    if (bRound) {
        path.addEllipse(0, 0, 2 * radius, 2 * radius);
    } else {
        path.addRect(0, 0, 2 * radius, 2 * radius);
    }
    painter.setClipPath(path);
    painter.drawPixmap(0, 0, 2 * radius, 2 * radius, pixmap);

    return dest;
}

void BaseForm::onSoundFinished()
{
    if (m_nLoopTimes > 0) {
        m_nLoopTimes--;
        if (m_pLoopTimer) {
            m_pLoopTimer->start(m_nLoopInterval);
        }
    }
}

void BaseForm::onLoopPlay()
{
    m_pLoopTimer->stop();
    m_pSoundPlay->stop();
    m_pSoundPlay->play();
}

void BaseForm::InitUI() { setGeometry(g_GlobalUI.m_RectSubForm); }

void BaseForm::OnPainter(QPainter &painter)
{
    painter.setRenderHint(QPainter::TextAntialiasing);
    painter.setPen(Qt::NoPen);
    painter.setBrush(QColor(23, 29, 36));
    painter.drawRect(rect());

    painter.setBrush(QColor(30, 35, 45));
    QRect rectBackground = rect();
    rectBackground.adjust(g_GlobalUI.sub_margin, g_GlobalUI.sub_margin, -g_GlobalUI.sub_margin,
                          -g_GlobalUI.sub_margin);
    painter.drawRoundedRect(rectBackground, 10, 10);
}

void BaseForm::ShowForm() { show(); }

void BaseForm::HideForm()
{
    {
        QMutexLocker locker(&m_playMutex);
        if (m_pLoopTimer) {
            m_pLoopTimer->stop();
        }
        disconnect(m_pSoundPlay, SIGNAL(finished()), this, SLOT(onSoundFinished()));
        m_nLoopTimes = 0;
        m_pSoundPlay->stop();
    }
    hide();
}

void BaseForm::PlaySound(const QString &fileName, int loopTimes, int interval_Second)
{
    QMutexLocker locker(&m_playMutex);
    m_pSoundPlay->stop();
    m_pSoundPlay->clear();
    m_nLoopTimes = loopTimes;
    m_nLoopInterval = interval_Second * 1000;
    if (m_nLoopTimes > 0) {
        connect(m_pSoundPlay, SIGNAL(finished()), this, SLOT(onSoundFinished()));
    } else {
        disconnect(m_pSoundPlay, SIGNAL(finished()), this, SLOT(onSoundFinished()));
    }
    m_sSoundFileName = GetCurrentPath() + "sound/" + fileName;
    DebugLog(QString("SoundFileName:%1").arg(m_sSoundFileName));
    m_pSoundPlay->enqueue(Phonon::MediaSource(m_sSoundFileName));
    m_pSoundPlay->setTransitionTime(0);
    m_pSoundPlay->play();
}

void BaseForm::PlaySound(const QList<QString> lstFileName)
{
    QMutexLocker locker(&m_playMutex);
    m_pSoundPlay->stop();
    m_pSoundPlay->clear();

    QList<QString> lstFile;

    for (int i = 0; i < lstFileName.length(); i++) {
        QString sound = QString("%1sound/%2").arg(GetCurrentPath()).arg(lstFileName.at(i));
        lstFile.append(sound);
    }
    QString targetFileName = GetCurrentPath() + "sound/tmp.wav";
    if (!MakeSingleWavFile(lstFile, targetFileName)) {
        return;
    }
    m_nLoopTimes = 0;
    m_nLoopInterval = 0;
    m_pSoundPlay->enqueue(Phonon::MediaSource(targetFileName));
    //    for (int i = 0; i < lstFileName.size(); i++) {
    //        QString sound = GetCurrentPath() + "sound/" + lstFileName.at(i);
    //        m_pSoundPlay->enqueue(Phonon::MediaSource(sound));
    //    }
    m_pSoundPlay->play();
}
