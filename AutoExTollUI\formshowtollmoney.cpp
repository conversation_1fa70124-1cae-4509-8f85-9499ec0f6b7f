#include "formshowtollmoney.h"

#include "globalutils.h"
#include "ilogmsg.h"

FormShowTollMoney::FormShowTollMoney(QWidget *parent) : BaseForm(parent)
{
    m_pQrCode = 0;
    m_nMaxQrCodeLen = 0;
    ReAllocData(1024 * 10);
}

FormShowTollMoney::~FormShowTollMoney()
{
    if (m_pQrCode) {
        delete[] m_pQrCode;
		m_nMaxQrCodeLen=0;
    }
}

//小节位,对32位正数表达的最大整数来说,最大节权万亿就够了(Z代表万亿）
const char *chnUnitSetion[] = {"", "W", "Y", "Z"};
//每个小节里面的独立计数
const char *chnUnitChar[] = {"", "S", "B", "Q"};

//每个小节内单独处理
void SectionToChinese(quint32 section, QString &chnStr)
{
    QString strIns = "";
    //当前小节内的当前个数的独立计数的权位
    qint32 unitPos = 0;
    //先设置zero为true,为了测试规则二,两个相连的0只留一个
    bool zero = true;
    while (section > 0) {
        int v = section % 10;
        if (v == 0) {
            //当不是两个0相连的时候或者 添加0在数字中
            if (!zero) {
                //当出现一个0的时候就设置zero为true,当下一个还是0的时候就不添加0了
                zero = true;
                chnStr.insert(0, QString::number(v));
            }
        } else {
            //当出现一个不是0的数字的时候就设置当前的zero标志为false表示下次遇到0的时候还是要添加
            zero = false;
            strIns = QString::number(v);
            strIns += chnUnitChar[unitPos];
            //将这个strIns插入到总的字符串的开始的位置
            chnStr.insert(0, strIns);
        }
        //权位增加
        unitPos++;
        //小节值除以10
        section /= 10;
    }
}

//转换为汉字数字（带位权）
void NumberToChinese(quint32 num, QString &chnStr)
{
    int unitPos = 0;        //小节的位置
    bool needZero = false;  //初始默认规则3不需要0
    while (num > 0) {
        QString strIns = "";
        unsigned int section = num % 10000;
        if (needZero) {  //满足规则3需要添零,根据后面的语句是否修改了needZero来检测是否添加0
            chnStr.insert(0, QChar('0'));
        }
        SectionToChinese(section, strIns);
        //检测当前section的的是否是0,如果是0的话,则添加空字符串的节权位,否则添加其他的
        strIns += (section != 0) ? chnUnitSetion[unitPos] : chnUnitSetion[0];
        chnStr.insert(0, strIns);
        //当满足小节内的值小于1000且值大于0的时候表示当前小节的千位是一个0,如果前面一小节还有值的时候则添0
        needZero = (section < 1000) && (section > 0);
        num /= 10000;
        unitPos++;
    }
}

void FormShowTollMoney::paintEvent(QPaintEvent *)
{
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    OnPainter(painter);
    QFont fontLabel(g_GlobalUI.m_FontName, g_GlobalUI.money_labelFontSize);
    QFont fontTollInfo(g_GlobalUI.m_FontName, g_GlobalUI.money_tollInfoFontSize);
    QFont fontMoney(g_GlobalUI.m_FontName, g_GlobalUI.money_moneyFontSize);
    if (m_isBeforePay) {
        fontTollInfo.setBold(true);
        fontMoney.setBold(true);
    }

    painter.setBrush(QColor(44, 54, 67));
    QRect tollInfoRect = g_GlobalUI.money_smallRoundRect;
    //计算初始
    int totalHeight = rect().height() - 2 * g_GlobalUI.sub_margin;
    int beginY = g_GlobalUI.sub_margin + g_GlobalUI.money_verticalMargin;
    //第一行，入口站
    tollInfoRect.moveTo(tollInfoRect.left(), beginY);
    painter.setPen(Qt::NoPen);
    painter.drawRoundedRect(tollInfoRect, 10, 10);
    //文字
    QRect labelRect = tollInfoRect;
    labelRect.setWidth(g_GlobalUI.money_labelWidth);
    painter.setFont(fontLabel);
    painter.setPen(Qt::white);
    painter.drawText(labelRect, Qt::AlignCenter, "入 口");
    QRect textRect = tollInfoRect;
    painter.setFont(fontTollInfo);
    painter.setPen(Qt::white);
    textRect.adjust(g_GlobalUI.money_labelWidth + 20, 0, 0, 0);
    painter.drawText(textRect, Qt::AlignVCenter | Qt::AlignLeft, m_sEnStation);

    //车牌
    int movePixel = tollInfoRect.height() + g_GlobalUI.money_verticalMargin;
    tollInfoRect.adjust(0, movePixel, 0, movePixel);
    painter.setPen(Qt::NoPen);
    painter.drawRoundedRect(tollInfoRect, 10, 10);
    labelRect = tollInfoRect;
    labelRect.setWidth(g_GlobalUI.money_labelWidth);
    painter.setFont(fontLabel);
    painter.setPen(Qt::white);
    painter.drawText(labelRect, Qt::AlignCenter, "车 牌");
    textRect = tollInfoRect;
    painter.setFont(fontTollInfo);
    painter.setPen(Qt::white);
    textRect.adjust(g_GlobalUI.money_labelWidth + 20, 0, 0, 0);
    painter.drawText(textRect, Qt::AlignVCenter | Qt::AlignLeft, m_sVlp);

    //车型
    tollInfoRect.adjust(0, movePixel, 0, movePixel);
    painter.setPen(Qt::NoPen);
    painter.drawRoundedRect(tollInfoRect, 10, 10);
    labelRect = tollInfoRect;
    labelRect.setWidth(g_GlobalUI.money_labelWidth);
    painter.setFont(fontLabel);
    painter.setPen(Qt::white);
    painter.drawText(labelRect, Qt::AlignCenter, "车 型");
    textRect = tollInfoRect;
    painter.setFont(fontTollInfo);
    painter.setPen(Qt::white);
    textRect.adjust(g_GlobalUI.money_labelWidth + 20, 0, 0, 0);
    painter.drawText(textRect, Qt::AlignVCenter | Qt::AlignLeft, m_sVehClassName);
    //车种
    tollInfoRect.adjust(0, movePixel, 0, movePixel);
    painter.setPen(Qt::NoPen);
    painter.drawRoundedRect(tollInfoRect, 10, 10);
    labelRect = tollInfoRect;
    labelRect.setWidth(g_GlobalUI.money_labelWidth);
    painter.setFont(fontLabel);
    painter.setPen(Qt::white);
    painter.drawText(labelRect, Qt::AlignCenter, "车 种");
    textRect = tollInfoRect;
    painter.setFont(fontTollInfo);
    painter.setPen(Qt::white);
    textRect.adjust(g_GlobalUI.money_labelWidth + 20, 0, 0, 0);
    painter.drawText(textRect, Qt::AlignVCenter | Qt::AlignLeft, m_sVehTypeName);
    //绘制4个后，计算剩余高度
    int leftHeight = totalHeight - 4 * tollInfoRect.height() - 6 * g_GlobalUI.money_verticalMargin;

    tollInfoRect.adjust(0, movePixel, 0,
                        movePixel + leftHeight - g_GlobalUI.money_smallRoundRect.height());
    painter.setPen(Qt::NoPen);
    painter.drawRoundedRect(tollInfoRect, 10, 10);
    painter.setFont(fontMoney);
    if (m_isBeforePay) {
        painter.setPen(QColor(238, 174, 1));
    } else {
        painter.setPen(Qt::white);
    }
    QString sMoney = QString::number(m_nMoney / 100.0, 'f', 2);
    painter.drawText(tollInfoRect, Qt::AlignCenter, sMoney);

    //右侧提示图
    QRect rectImage = g_GlobalUI.money_imageRect;
    QPixmap image;
    bool bRound = true;
    if (m_isBeforePay) {
        image.load(GetCurrentPath() + "/images/paycode.jpg");
    } else {
        if (m_sQrCode.length() > 0) {
            if (m_channelType == TransPT_AliPay) {
                image.load(GetCurrentPath() + "/images/alipayticket.png");
            } else if (m_channelType == TransPT_WeChat) {
                image.load(GetCurrentPath() + "/images/wechatticket.png");
            } else if (m_channelType == TransPT_Cash) {
                image.load(GetCurrentPath() + "/images/qrcode.bmp");
            }
            bRound = false;
        } else {
            if (m_channelType == TransPT_ETCCard) {
                //一路平安的图片
                image.load(GetCurrentPath() + "/images/pingan.jpg");
            }
        }
    }

    QPixmap roundImage = generatePixmap(image, rectImage.width() / 2, bRound);
    painter.drawPixmap(rectImage, roundImage);
    //右侧提示文字
    QFont fontTip(g_GlobalUI.m_FontName, g_GlobalUI.money_tipFontSize);
    if (!m_isBeforePay) {
        fontTip.setBold(true);
    }
    fontTip.setLetterSpacing(QFont::AbsoluteSpacing, g_GlobalUI.money_tipFontSpace);
    painter.setPen(QColor(238, 174, 1));
    painter.setFont(fontTip);
    painter.drawText(g_GlobalUI.money_tipRect, Qt::AlignCenter, m_sShowText);
    if (m_isBeforePay) {
        //支付标识
        QPixmap alipay(GetCurrentPath() + "/images/alipay.png");
        painter.drawPixmap(g_GlobalUI.money_alipaylogoRect,
                           alipay.scaled(g_GlobalUI.money_alipaylogoRect.size(),
                                         Qt::KeepAspectRatio, Qt::SmoothTransformation));
        QPixmap wechat(GetCurrentPath() + "/images/wechat.png");
        painter.drawPixmap(g_GlobalUI.money_wechatlogoRect,
                           wechat.scaled(g_GlobalUI.money_alipaylogoRect.size(),
                                         Qt::KeepAspectRatio, Qt::SmoothTransformation));
    }
    painter.end();

    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

void FormShowTollMoney::PlayMoneySound()
{
    //只处理千万
    int yuan = m_nMoney / 100;
    //只处理1亿内数字
    if (m_nMoney == 0 || yuan > 100000000) {
        return;
    }

    QList<QString> lstFile;
    int jiaofen = m_nMoney % 100;
    if (jiaofen > 0) {
        QString sJiaoFen = QString("%1").arg(jiaofen, 2, 10, QLatin1Char('0'));
        lstFile.append("dian.wav");
        for (int i = 0; i < sJiaoFen.size(); i++) {
            lstFile.append(QString("%1.wav").arg(sJiaoFen.at(i)));
        }
    }
    lstFile.push_back("yuan.wav");
    if (yuan > 0) {
        //转换成汉字表达法
        QString sNumChn = "";
        NumberToChinese(yuan, sNumChn);
        //开始拼元以上的声音文件。
        if (sNumChn.length() > 0) {
            for (int i = sNumChn.length() - 1; i >= 0; i--) {
                lstFile.push_front(QString("%1.wav").arg(sNumChn[i]));
            }
        }
    } else {
        lstFile.push_front("0.wav");
    }
    //    QString sYuan = QString("%1").arg(yuan);
    //    for (int i = 0; i < sYuan.length(); i++) {
    //        if (i == 4) {
    //            lstFile.push_front("wan.wav");
    //        }
    //        switch (i % 4) {
    //        case 0:
    //            break;
    //        case 1:
    //            lstFile.push_front("shi.wav");
    //            break;
    //        case 2:
    //            lstFile.push_front("bai.wav");
    //            break;
    //        case 3:
    //            lstFile.push_front("qian.wav");
    //            break;
    //        }
    //        lstFile.push_front(QString("%1.wav").arg(sYuan.at(sYuan.length() - i - 1)));
    //    }
    lstFile.push_front("toll.wav");
    lstFile.push_back("ma.wav");
    PlaySound(lstFile);
}

//播放支付成功领取电子发票声音
void FormShowTollMoney::PlaySuccessSound_ETicket(quint32 money, CTransPayType channelType)
{
    QList<QString> lstFile;
    switch (channelType) {
        case TransPT_WeChat:
            lstFile.append("wechat.wav");
            break;
        case TransPT_AliPay:
            lstFile.append("alipay.wav");
        default:
            break;
    }
    lstFile.append("paysucc.wav");
    if (money > 0) {
        //有发票
        lstFile.append("invoicedz.wav");
    }
    lstFile.append("bye.wav");
    PlaySound(lstFile);
}

quint8 *FormShowTollMoney::ReAllocData(int nDataLen)
{
    if (nDataLen > m_nMaxQrCodeLen) {
        DebugLog(QString("二维码内存%1超出预期%2重新分配").arg(nDataLen).arg(m_nMaxQrCodeLen));
        delete[] m_pQrCode;
        m_pQrCode = 0;
        m_nMaxQrCodeLen = nDataLen;
    }
    if (!m_pQrCode) {
        DebugLog(QString("分配内存%1").arg(m_nMaxQrCodeLen));
        m_pQrCode = new quint8[m_nMaxQrCodeLen];
        if (!m_pQrCode) {
            DebugLog("二维码内存分配失败");
            m_pQrCode = NULL;
            m_nMaxQrCodeLen = 0;
            return NULL;
        }
    }

    return m_pQrCode;
}

void FormShowTollMoney::PlaySuccessSound()
{
    QList<QString> lstFile;
    lstFile.append("paysucc.wav");
    lstFile.append("bye.wav");
    PlaySound(lstFile);
}

void FormShowTollMoney::ShowMoney(const QString &vlp, const QString &vehClassName,
                                  const QString &vehTypeName, const QString &enStation,
                                  quint32 money, bool beforePay, CTransPayType channelType)
{
    m_sVlp = vlp;
    m_sVehClassName = vehClassName;
    m_sVehTypeName = vehTypeName;
    m_sEnStation = enStation;
    m_nMoney = money;
    m_isBeforePay = beforePay;
    m_channelType = channelType;
    if (m_isBeforePay) {
        m_sShowText = "请出示付款码";
        PlayMoneySound();
    } else {
        //播放支付成功旅行
        if (channelType == TransPT_ETCCard) {
            PlaySuccessSound();
            m_sShowText = "一路平安";
        }
    }
    BaseForm::ShowForm();
}

void FormShowTollMoney::ShowETicketInfo(const QString &vlp, const QString &vehClassName,
                                        const QString &vehTypeName, const QString &enStation,
                                        quint32 money, CTransPayType channelType,
                                        const QString &qrCode)
{
    m_sVlp = vlp;
    m_sVehClassName = vehClassName;
    m_sVehTypeName = vehTypeName;
    m_sEnStation = enStation;
    m_nMoney = money;
    m_isBeforePay = false;

    PlaySuccessSound_ETicket(money, channelType);

    if (money > 0) {
        m_sShowText = "请领取电子发票";
    } else {
        m_sShowText = "一路平安";
    }
    m_sQrCode = qrCode;
    m_channelType = channelType;

    quint16 qrCodeLen = qrCode.length() / 2;
    DebugLog(QString("二维码长度%1").arg(qrCodeLen));
    quint8 *qrCodeData = ReAllocData(qrCodeLen);  // new quint8[qrCodeLen];
    if (!qrCodeData) {
        ErrorLog(QString("创建内存%1失败").arg(qrCodeLen));
        return;
    }
    memset(qrCodeData, 0, qrCodeLen);
    if (!Hex2Raw(qrCodeData, qrCode)) {
        return;
    }

    QFile ff(GetCurrentPath() + "/images/qrcode.bmp");

    ff.open(QIODevice::WriteOnly);
    QDataStream in(&ff);
    in.writeRawData((const char *)qrCodeData, qrCodeLen);
    ff.close();

    BaseForm::ShowForm();
}
