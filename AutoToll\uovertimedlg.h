#ifndef INVOICECHANGEDLG_H
#define INVOICECHANGEDLG_H

#include "baseopwidget.h"
#include "lineedit.h"

class CUOverTimeDlg : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit CUOverTimeDlg(QWidget *parent = 0);
    ~CUOverTimeDlg();

    void SetAllowCancel(bool bAllow) { m_bAllowCancel = bAllow; }

    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    void closeEvent(QCloseEvent *event);

private:
    void Init();

private:
    CNumLineEdit *m_pLineEdit;
    QLabel *m_pLabelTitle;
    QLabel *m_pLabelName;
    QLabel *m_pLabelTime;
    qint64  m_nOverTimeInFen;
    QLabel *m_pLabelTip;
    bool m_bAllowCancel;
private:
    void SetEnTimeText();
public:
    quint64 GetOverTime();
};


#endif // UOVERTIMEDLG_H
