﻿#include <QApplication>
#include <QProcess>
#include <QTextCodec>

#include "MtcKey/MtcKeyDef.h"
#include "cryptographiccommon.h"
#include "devicefactory.h"
#include "dlgmain.h"
#include "globalui.h"
#include "globalutils.h"
#include "ilogmsg.h"
#include "laneinfo.h"
#include "qtsingleapplication.h"
#include "remotemsgmgr.h"
#include "stdlog.h"
#include "ui_splashscreen.h"
#include "watchdog.h"
#include "JlCompress.h"

//程序启动时间戳
quint64 g_nAppStartTimeStamp = 0;

QString g_sLastTime = "20350101";

void ReportMessage(Ui_SplashScreen *pScreen, const QString &sMsg)
{
    pScreen->showMessage(sMsg);
    MsgLog(QString("%1").arg(sMsg));
    return;
}

int main(int argc, char *argv[])
{
    QtSingleApplication a(argc, argv);
    QString scurPath = QCoreApplication::applicationDirPath();
#ifdef Q_OS_UNIX
    QApplication::setLibraryPaths(
        QStringList(QCoreApplication::applicationDirPath() + "/plugins/"));
#endif
    RemoveFile(GetCurrentPath() + "LaneConfig.exe");
    g_nAppStartTimeStamp = (quint64)QDateTime::currentDateTime().toTime_t();
    if (a.isRunning()) return 0;
    QString sError;

    QTextCodec *utf = QTextCodec::codecForName("UTF-8");
    QTextCodec::setCodecForCStrings(utf);
    QTextCodec::setCodecForTr(utf);
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("GB18030"));

    bool bTestVer = false;
    
    // 确保Ptr_Info已正确初始化，添加空指针检查
    if (!Ptr_Info) {
        sError = QString("全局信息对象未正确初始化");
        ChMessageOnlyOk_Error(sError);
        return 0;
    }

    QCoreApplication::setOrganizationName("JXLJKIKFYXGS");
    QCoreApplication::setApplicationName("AutoToll");
    
    Ptr_Info->CreateVersionNew(bTestVer,QString("20250319100"),33);
    QString sTransVersion = Ptr_Info->GetTransVer();
    QString sAppVer = Ptr_Info->GetAppVer();
    QString sSubVer = Ptr_Info->GetSubVer();
    QString sFullVer = sAppVer + sSubVer;

    if (!log_init(sFullVer, sTransVersion, QString("ETCLog"), sError)) {
        ChMessageOnlyOk_Error(sError);
        log_fini();
        return 0;
    }

    TraceLog("Application Inited");
    Ui_SplashScreen splashcreen;
    splashcreen.resize(530, 240);
    splashcreen.setPixmap(QPixmap(":images/images/dev/etcflash.png"));
    splashcreen.show();
#if 0
    SleeperThread::msleep(2000);
#endif
    //设置当前程序版本号
    a.setApplicationVersion(sTransVersion);

    QString sMakeTime = QString::fromUtf8("车道收费软件");
    QString sMsg = QString("程序(%1)(%2)开始启动...").arg(sMakeTime).arg(sAppVer);
    // QString str = QTime::currentTime().toString("hhmmsszzz");
    // StdInfoLog(LogKey::OneKey_Other, LogKey::Other_ProgramStatus, str, QString(), sMsg);

    ReportMessage(&splashcreen, sMsg);
    TraceLog(QString("Application VerSion: %1").arg(sAppVer));

    // 添加配置文件读取的错误检查
    if (!Ptr_Info->ReadCfgFile(sError)) {
        ChMessageOnlyOk_Error(sError);
        log_fini();
        return 0;
    }

    // 验证关键配置参数
    if (Ptr_Info->GetStationID() <= 0 || Ptr_Info->GetLaneId() <= 0) {
        sError = QString("收费站或车道配置参数错误");
        ChMessageOnlyOk_Error(sError);
        log_fini();
        return 0;
    }

    RemoteMsgMgr::GetSingleInst()->SetAppVersion(sTransVersion);
    RemoteMsgMgr::GetSingleInst()->SetOrgInfo(Ptr_Info->GetStationID(), Ptr_Info->GetLaneId());

    QString sProcessName = QString("AutoTollUI.exe");
    // QString sUiName = QString("%1/%2").arg(scurPath).arg(sProcessName);

    if (Ptr_Info->bStartScreenUI()) {
        QString sPath = Ptr_Info->GetScreenUIPath();
        DebugLog(QString("UI程序目录:%1").arg(sPath));
        if (!CWatchDog::CheckProcess(sProcessName)) {
            if (!CWatchDog::StartProcess(sPath)) {
                DebugLog(QString("启动卡机屏幕软件失败"));
            } else {
                DebugLog(QString("启动卡机屏幕软件成功"));
            }
        } else {
            DebugLog(QString("卡机UI:%1已启动").arg(sProcessName));
        }
    }

    SleeperThread::msleep(2000);

    //初始化，加载界面参数
    if (!g_GlobalUI.Init(Ptr_Info->IsEntryLane(), sError)) {
        ChMessageOnlyOk_Error(sError);
        log_fini();
        return 0;
    }

    //#ifndef QT_DEBUG
    QString sCurTime = QDateTime::currentDateTime().toString("yyyyMMdd");
    // QString sSubStr = sAppVer.left(8);
    if (sCurTime > g_sLastTime || sCurTime < QString("20240901")) {
        sError = QString("车道时间异常,请检查");
        ChMessageOnlyOk_Error(sError);
        log_fini();
        return 0;
    }
    //#endif

    initKeyDef_JX2015();
    CStdLog::StdLogAppStart(true);

    QDlgMain *pMainWnd = GetMainDlg();
    if (!pMainWnd) {
        sError = QString("主窗口创建失败");
        ChMessageOnlyOk_Error(sError);
        log_fini();
        return 0;
    }
    
    //初始化主窗口
    pMainWnd->Init();
    pMainWnd->show();
    //将输入法切换到英文模式
#ifdef Q_WS_WIN
    LoadKeyboardLayout((LPCWSTR)QString("0x0409").utf16(), KLF_ACTIVATE);
#else
#endif
    a.installEventFilter(MtcKeyFilter::instance());
    splashcreen.finish(pMainWnd);
    //#ifndef QT_DEBUG
    if (!Ptr_Info->IsShowMouse()) {
        QApplication::setOverrideCursor(Qt::BlankCursor);
    }

    //#endif
    if (pMainWnd->AppStart(sError)) {
        int e = a.exec();
        CStdLog::StdLogAppStart(false);
        if (e == RETCODE_RESTART)  //重启程序
        {
            QFileInfo fileInfo(qApp->applicationFilePath());
            qApp->closeAllWindows();
            SleeperThread::msleep(1000);
            QProcess::startDetached(fileInfo.fileName(), QStringList());
            log_fini();
            return 0;
        } else if (e == RETCODE_QUIT)  //退出程序
        {
            //            qApp->closeAllWindows();
            //            SleeperThread::msleep(1000);
            log_fini();
            return 0;
        }
        log_fini();
        return e;
    } else {
        CStdLog::StdLogAppStart(false);
        QApplication::setOverrideCursor(Qt::ArrowCursor);
        ChMessageOnlyOk_Error(sError);
        log_fini();
        return 0;
    }
}
