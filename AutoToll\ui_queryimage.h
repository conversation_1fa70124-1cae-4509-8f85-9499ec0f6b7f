#ifndef UI_QUERYIMAGE_H
#define UI_QUERYIMAGE_H

#include "baseetcdialog.h"
class ui_queryimage :public CBaseEtcDialog
{
    Q_OBJECT
public:
    ui_queryimage(QWidget *parent = 0);
    void setFilePath(QPixmap &piximage);
private slots:
    void paintEvent(QPaintEvent *);
    void mousePressEvent (QMouseEvent *);
signals:
    void sigClose();
private:
    QPixmap m_sPixImage;
};

#endif // UI_QUERYIMAGE_H
