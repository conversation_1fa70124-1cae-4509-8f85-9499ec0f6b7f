#include "wtsysdev_zc.h"
#include "../../devices/globalutils.h"

CWtSysDev_ZC::CWtSysDev_ZC() : CWtSysDev()
{
    // 初始化函数指针
    m_wtSysDevFinish = NULL;
    m_wtSysManualFinishing = NULL;
    m_wtSysIsVehicleAloneUpper = NULL;
    
    // 初始化标记
    m_hasDevFinish = false;
    m_hasManualFinishing = false;
    m_isUpperCase = false;
}

CWtSysDev_ZC::~CWtSysDev_ZC()
{
    // 基类的析构函数会处理资源释放
}

bool CWtSysDev_ZC::LoadDriver()
{
    // 先调用基类的LoadDriver加载基本功能
    if (!CWtSysDev::LoadDriver()) {
        return false;
    }

    // 获取动态库路径
    QString sPath = GetCurrentPath() + m_sDriver;
    
    // 1. 加载WtSys_DevFinish函数
    m_wtSysDevFinish = (Func_WtSys_DevFinish)LoadFunc(m_hLibModule, "WtSys_DevFinish");
    if (m_wtSysDevFinish) {
        InfoLog("WtSys_DevFinish函数加载成功");
        m_hasDevFinish = true;
    } else {
        DebugLog("WtSys_DevFinish函数未找到，将使用WtSys_UploadVehicleAgain作为备选");
    }
    
    // 2. 加载WtSys_ManualFinishing函数
    m_wtSysManualFinishing = (Func_WtSys_ManualFinishing)LoadFunc(m_hLibModule, "WtSys_ManualFinishing");
    if (m_wtSysManualFinishing) {
        InfoLog("WtSys_ManualFinishing函数加载成功");
        m_hasManualFinishing = true;
    } else {
        DebugLog("WtSys_ManualFinishing函数未找到");
    }
    
    // 3. 处理WtSys_IsVehicleAlone大小写问题
    // 先尝试加载大写版本
    m_wtSysIsVehicleAloneUpper = (Func_WtSys_IsVehicleAlone_Upper)LoadFunc(m_hLibModule, "WtSys_IsVehicleAlone");
    if (m_wtSysIsVehicleAloneUpper) {
        InfoLog("WtSys_IsVehicleAlone函数加载成功");
        m_isUpperCase = true;
    }
    // 注意：小写版本(Wtsys_IsVehicleAlone)在基类中已经尝试加载

    return true;
}

bool CWtSysDev_ZC::DevFinish_HD()
{
    bool result = false;
    
    if (m_hasDevFinish && m_wtSysDevFinish) {
        // 优先使用WtSys_DevFinish接口
        InfoLog("开始执行DevFinish函数");
        int retCode = m_wtSysDevFinish();
        
        if (retCode >= 0) {
            InfoLog(QString("DevFinish函数执行成功，返回值: %1").arg(retCode));
            result = true;
        } else {
            ErrorLog(QString("DevFinish函数执行失败，错误码: %1").arg(retCode));
            // 如果WtSys_DevFinish失败，尝试使用基类的UploadVehWeightAgain方法
            InfoLog("尝试使用备选方法执行人工称重");
            result = UploadVehWeightAgain();
        }
    } else {
        // 如果没有WtSys_DevFinish接口，则使用基类的UploadVehWeightAgain方法
        InfoLog("使用UploadVehWeightAgain方法执行人工称重");
        result = UploadVehWeightAgain();
    }
    
    return result;
}

// 兼容性扩展：添加针对WtSys_GetAxisData接口参数不一致的处理函数
int CWtSysDev_ZC::GetAxisDataCompatible(int vehicleID, int axisID, int &axisType, long &weight, int &axisSpeed)
{
    // 检查基类的WtSys_GetAxisData函数是否存在
    if (!CWtSysDev::WtSys_GetAxisData) {
        ErrorLog("WtSys_GetAxisData函数未加载");
        return -1;
    }
    
    // 添加计量版本号参数，适配接口差异
    int meterVer = 0;
    return CWtSysDev::WtSys_GetAxisData(vehicleID, axisID, &axisType, &weight, &axisSpeed, &meterVer);
}

// 兼容性扩展：处理WtSys_IsVehicleAlone接口名称大小写不一致问题
bool CWtSysDev_ZC::IsVehicleAloneCompatible(int count)
{
    // 首先尝试使用大写版本
    if (m_isUpperCase && m_wtSysIsVehicleAloneUpper) {
        InfoLog("使用大写版本的WtSys_IsVehicleAlone函数");
        return m_wtSysIsVehicleAloneUpper(count);
    }
    
    // 如果大写版本不可用，尝试使用基类的小写版本
    if (CWtSysDev::WtSys_IsVehicleAlone) {
        InfoLog("使用小写版本的Wtsys_IsVehicleAlone函数");
        return CWtSysDev::WtSys_IsVehicleAlone(count);
    }
    
    // 如果两个版本都不可用，记录错误并返回默认值
    ErrorLog("IsVehicleAlone函数未加载，返回默认值false");
    return false;
}

// 兼容性扩展：处理WtSys_ManualFinishing接口
int CWtSysDev_ZC::ManualFinishingCompatible()
{
    // 检查函数是否加载
    if (m_hasManualFinishing && m_wtSysManualFinishing) {
        InfoLog("执行WtSys_ManualFinishing函数");
        int retCode = m_wtSysManualFinishing();
        
        if (retCode >= 0) {
            InfoLog(QString("WtSys_ManualFinishing函数执行成功，返回值: %1").arg(retCode));
        } else {
            ErrorLog(QString("WtSys_ManualFinishing函数执行失败，错误码: %1").arg(retCode));
        }
        
        return retCode;
    }
    
    // 如果函数未加载，记录错误并返回错误码
    ErrorLog("WtSys_ManualFinishing函数未加载");
    return -1;
} 