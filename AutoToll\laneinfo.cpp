#include "laneinfo.h"

#include <QSettings>
#include <QSysInfo>
#include <QtGlobal>
#include <QDateTime>

#include "dlgmain.h"
#include "globalutils.h"
#include "log4qt/ilogmsg.h"
#include "log4qt/log4qt.h"

//#define E_PAPER
CLaneInfo::CLaneInfo(void)
{
    // GetCurrentPath（） 返回目录最后字符为'\'
    m_sFileName = QString("%1lane.ini").arg(GetCurrentPath());

    m_sDBPath = "";
    m_sBakDBPath = "";

    m_sHexStationID = "";

    m_sDefaultPlate = "赣A";

    m_bOpenLane = false;
    m_nOtherParamInterval = 3;

    m_bLockWindowsOnTop = true;
    m_bShowMouse = false;

    m_bCheckMinFee = true;
    m_dwDetectTime = 3000;
    m_bUseOpenTime = true;

    m_bHaveFrontRsu = true;
    m_nRsuNum = 1;
    m_bhaveCardMgr = false;
    m_bCardMgrEnabled = false;
    m_bRemoveBakFile = true;
    m_sFeeUrl.clear();
    m_sEntryUrl.clear();
    m_bCheckSameVeh = true;
    m_bCheckExitLoop = true;
    m_eTick = true;
    m_nOutTime = 0;

    m_sGBLaneId.clear();
    m_sGBStationId.clear();
    m_sStationName.clear();
    m_sLocalIP.clear();
    m_bRemoteControl = false;
    m_bCheckWeight = true;
    m_bPaperCard = false;
    m_bTestVer = false;
    m_sSubVer = "";
    m_bTestPaper = false;
    m_bAutoDelay = false;
    m_nLaneTypeHT = 7; // 初始化 LaneTypeHT 默认值为 7
    m_nCardMgrType = 1; // 初始化卡机类型默认值为 1 (顺一)
    m_sProDebPaymentUrl = "";
    m_nCardTimerInterval = 1800;
    
    // 新增：初始化禁用车型设备队列获取车牌信息的配置，默认为false（不禁用）
    m_bDisableVcrQueuePlate = false;
}

CLaneInfo::~CLaneInfo(void) {}

bool CLaneInfo::ReadCfgFile(QString &sError)
{
    // GetMainDlg()->ShowLog(QString("读取配置文件"));
    m_sSysTemVer = GetSysVersion();

    QSettings readLaneConfig(m_sFileName, QSettings::IniFormat);
    //车道基本配置参数
    QString sSection = "laneinfo";

    m_bStartScreenUI = readLaneConfig.value(sSection + "/startScreenUI", false).toBool();
    m_sScreenUIPath = readLaneConfig.value(sSection + "/ScreenUIPath", "").toString();

    m_bManualCapture = readLaneConfig.value(sSection + "/ManualCapture", false).toBool();
    m_bFareShow = readLaneConfig.value(sSection + "/FareShow", true).toBool();
    m_bReverDownBar = readLaneConfig.value(sSection + "/ReverDownBar", false).toBool();
    m_nLanelen = readLaneConfig.value(sSection + "/Lanelen", 20).toUInt();
    m_isClear = readLaneConfig.value(sSection + "/isclear", false).toBool();
    m_nUTimes = readLaneConfig.value(sSection + "/utimes", 30).toInt();
    m_LoopNum = readLaneConfig.value(sSection + "/loopnum", 6).toInt();
    m_dwDetectTime = readLaneConfig.value(sSection + "/detecttimes", 3).toInt();
    m_dwDetectTime *= 1000;

    m_nLaneType = readLaneConfig.value(sSection + "/lanetype").toInt();

    // 读取配置文件中的 LaneTypeHT 配置项，默认值为 7
    m_nLaneTypeHT = readLaneConfig.value(sSection + "/LaneTypeHT", 7).toUInt();

    // 读取CardMgr/nType配置，默认值为1(顺一)
    sSection = "CardMgr";
    m_nCardMgrType = readLaneConfig.value(sSection + "/DevType", 1).toInt();
    m_nCardTimerInterval = readLaneConfig.value(sSection + "/Interval", 1800).toInt();
    sSection = "laneinfo";
    m_capture = readLaneConfig.value(sSection + "/capture", false).toBool();
    m_bCheckConVeh = readLaneConfig.value(sSection + "/checkconveh", true).toBool();
    m_bCheckVehInQue = readLaneConfig.value(sSection + "/checkvehque", false).toBool();
    m_bDeleteReservVeh = readLaneConfig.value(sSection + "/delreserveveh", false).toBool();

    m_bUseOpenTime = readLaneConfig.value(sSection + "/opentime", true).toBool();
    m_bRemoveBakFile = readLaneConfig.value(sSection + "/removebakfile", true).toBool();

    m_nRsuNum = readLaneConfig.value(sSection + "/rsunum", 1).toInt();

    m_bhaveCardMgr = readLaneConfig.value(sSection + "/havecardmgr", false).toBool();
    //是否有卡机，决定是否使用卡机的初始值
    m_bCardMgrEnabled = m_bhaveCardMgr;

    m_bNoWeightDev = readLaneConfig.value(sSection + "/NoWeight", false).toBool();
    m_bCheckWeight = readLaneConfig.value(sSection + "/CheckWeight", true).toBool();

    m_nOutTime = readLaneConfig.value(sSection + "/maxouttime", 24 * 60).toInt();
    m_nMaxDays = readLaneConfig.value(sSection + "/MaxDays", 20).toInt();
    m_nMaxTimeInterval = readLaneConfig.value(sSection + "/MaxInterval", 15).toInt();
    m_nMaxTimeInterval = m_nMaxTimeInterval * 60;  //单位秒

    if (m_nLaneType == LaneType_EtcEntry || LaneType_MtcEntry == m_nLaneType ||
        LaneType_AutoLane == m_nLaneType) {
        m_nLaneSign = 1;
    } else if (m_nLaneType == LaneType_EtcExit || LaneType_MtcExit == m_nLaneType) {
        m_nLaneSign = 2;
    } else {
        sError = QString("车道类型[%1],配置错误!").arg(m_nLaneType);
        return false;
    }

    m_eTick = readLaneConfig.value(sSection + "/eTicket", true).toBool();
    m_bUseVcrResult = readLaneConfig.value(sSection + "/usevcrresult", false).toBool();
    m_nVehDetectTime = readLaneConfig.value(sSection + "/vehdetecttime", 300).toInt();

#ifdef E_PAPER
    m_bPaperCard = readLaneConfig.value(sSection + "/papercard", false).toBool();
    m_bTestPaper = readLaneConfig.value(sSection + "/testpaper", false).toBool();
#endif
    m_bStopUJ = readLaneConfig.value(sSection + "/StopUJ", false).toBool();

    m_bNewUJ = readLaneConfig.value(sSection + "/NewUJ", false).toBool();
    m_nFlagCnt = readLaneConfig.value(sSection + "/minflagcnt", 2).toBool();

    m_bRelocatedCard = readLaneConfig.value(sSection + "/MoveCard", true).toBool();
    m_bETCStopTruck = readLaneConfig.value(sSection + "/etcstoptruck", false).toBool();
    if (IsEntryLane()) {  //一定在lanetype后面
        m_bFuzzyMatch = readLaneConfig.value(sSection + "/fuzzymatch", false).toBool();
    } else {
        m_bFuzzyMatch = readLaneConfig.value(sSection + "/fuzzymatch", true).toBool();
    }
    m_bReplaceByVcr = readLaneConfig.value(sSection + "/replacebyvcr", false).toBool();

    // 新增：读取是否禁用从车型设备队列获取车牌信息的配置
    m_bDisableVcrQueuePlate = readLaneConfig.value(sSection + "/DisableVcrQueuePlate", false).toBool();

    bool bOk = false;
    m_nLaneId = readLaneConfig.value(sSection + "/laneid", 0).toInt(&bOk);
    if ((!bOk) || (m_nLaneId <= 0)) {
        sError = QString("车道代码[%1],配置错误!").arg(m_nLaneId);
    }
    m_sFeeDllCurPath = readLaneConfig.value(sSection + "/feeDllCurPath").toString();

    m_bUseStdVPR = readLaneConfig.value(sSection + "/stdvpr", true).toBool();
    m_bFrontBar = readLaneConfig.value(sSection + "/frontbar", false).toBool();

    m_bCheckVehInfo = readLaneConfig.value(sSection + "/checkvehinfo", true).toBool();

    m_bCheckMinFee = readLaneConfig.value(sSection + "/checkMinfee", true).toBool();

    //检验etc卡是否过期
    m_bCheckOutTime = readLaneConfig.value(sSection + "/checkouttime", true).toBool();
    //检验etc卡区域
    m_bCheckLocal = readLaneConfig.value(sSection + "/checklocal", true).toBool();
    //检验卡黑名单
    m_bCheckCardBList = readLaneConfig.value(sSection + "/checkcardblist", true).toBool();
    //是否允许拆卸obu
    m_bAllowDisOBU = readLaneConfig.value(sSection + "/disobu", false).toBool();
    //校验obu超时
    m_bCheckOBUExpired = readLaneConfig.value(sSection + "/CheckOBUExpired", true).toBool();
    //允许同一obu连续交易
    m_bCheckSameVeh = readLaneConfig.value(sSection + "/checksameveh", true).toBool();
    //后天线交易时是否判断线圈状态
    m_bCheckExitLoop = readLaneConfig.value(sSection + "/checkexistloop", true).toBool();

    m_bLockWindowsOnTop = readLaneConfig.value(sSection + "/lockwindowsontop", true).toBool();
    m_bShowMouse = readLaneConfig.value(sSection + "/showmouse", true).toBool();

    sSection = "PSAMUrl";
    m_sURLParams.ip = readLaneConfig.value(sSection + "/ip", "http://127.0.0.1").toString();
    m_sURLParams.port = readLaneConfig.value(sSection + "/port", "8950").toString();
    m_sURLParams.psam_sign =
        readLaneConfig.value(sSection + "/psamSign", "/psam-auth-platform/v1/service/api/psam/sign")
            .toString();
    m_sURLParams.psam_auth =
        readLaneConfig.value(sSection + "/psamAuth", "/psam-auth-platform/v1/service/api/psam/auth")
            .toString();
    m_sURLParams.psam_result =
        readLaneConfig
            .value(sSection + "/psamResult", "/psam-auth-platform/v1/service/api/psam/result")
            .toString();

    m_sURLParams.sAuUrl_bak = readLaneConfig.value(sSection + "/AuthUrlBak", "").toString();
    m_sURLParams.sSignUrl_bak = readLaneConfig.value(sSection + "/SignUrlBak", "").toString();
    m_sURLParams.sResultUrl_bak = readLaneConfig.value(sSection + "/resultUrlBak", "").toString();

    m_sURLParams.sAuList_Url = readLaneConfig.value(sSection + "/authListUrl", "").toString();

    //收费站配置参数
    sSection = "stationinfo";
    m_sNTPHost = readLaneConfig.value(sSection + "/ntphost", "**********").toString();
    m_nNTPPort = readLaneConfig.value(sSection + "/ntpport", 123).toUInt();
    m_nNTPTime = readLaneConfig.value(sSection + "/ntptime", 60).toUInt();

    m_sParaUrl = readLaneConfig.value(sSection + "/paraUrl", "").toString();
    m_sDataUrl = readLaneConfig.value(sSection + "/dataUrl", "").toString();
    m_sDataUrlBak = readLaneConfig.value(sSection + "/dataUrlBak", "")
                        .toString();  // QString("https://************:8890/api/v1/upload"))
                                      //.toString();

    m_sUpLoadLogUrl =
        readLaneConfig
            .value(sSection + "/logUrl", QString("http://*************:8890/api/v2/upload"))
            .toString();

    m_sFeeUrl =
        readLaneConfig
            .value(sSection + "/feeurl", QString("http://************:8011/fee-api/api/v1/fee"))
            .toString();
    m_sEntryUrl =
        readLaneConfig
            .value(sSection + "/entryUrl", QString("http://************:8011/fee-api/api/v1/fee"))
            .toString();

    m_sFlagUrl =
        readLaneConfig
            .value(sSection + "/flagurl", QString("http://************:8011/fee-api/api/v1/fee"))
            .toString();

    m_sBinFileAuth = readLaneConfig.value(sSection + "/binFileAuth", "").toString();
    // m_sGBLaneId = readLaneConfig.value(sSection + "/GBLaneId", "").toString();
    m_nStationID = readLaneConfig.value(sSection + "/StationId", 0).toUInt();

    if (0 == m_nStationID) {
        sError = QString("未配置本站代码");
        return false;
    }

    this->SetStationID(m_nStationID);

    sSection = "localdb";
    QString defPath = GetCurrentPath();
    m_sPicPath = readLaneConfig.value(sSection + "/picpath", defPath + QString("pic")).toString();
    CreatePath(m_sPicPath);

    m_sDBPath = readLaneConfig.value(sSection + "/dbpath", defPath).toString();
    m_sBakDBPath =
        readLaneConfig.value(sSection + "/dbbakpath", defPath + QString("bak")).toString();

    m_nTmpFileMaxTime = readLaneConfig.value(sSection + "/tmpfiletime", 1).toInt();

    sSection = QString("rsu1");
    m_sRSUComm = readLaneConfig.value(sSection + "/connstr1", "COM1").toString();
    m_sRSUPower = readLaneConfig.value(sSection + "/power", "28").toString();
    m_sRSUChannelID = readLaneConfig.value(sSection + "/channelid", "0").toString();

    sSection = "vlprsum";
    m_sVlprBatchNo = readLaneConfig.value(sSection + "/batchno").toString();
    m_nDataCnt = readLaneConfig.value(sSection + "/datacnt").toUInt();
    m_nPicCnt = readLaneConfig.value(sSection + "/piccnt").toUInt();

    QString sOpenSection = QString("OpenLane");
    m_bOpenLane = readLaneConfig.value(sOpenSection + "/OpenLane", false).toBool();

    // IP:port
    m_sOpenLaneIPPort = readLaneConfig.value(sOpenSection + "/IPPort", "").toString();
    //代写门架hex码
    m_sReplaceGatryHex = readLaneConfig.value(sOpenSection + "/gantryhex", "").toString();
    m_sReplaceGatryID = readLaneConfig.value(sOpenSection + "/gantryid", "").toString();
    m_sReplaceTime = readLaneConfig.value(sOpenSection + "/opentime", "20220701").toString();

    QString sExSection = QString("RemoteControl");

    m_bRemoteControl = readLaneConfig.value(sExSection + "/remotecontrol", false).toBool();

    m_sLocalIP = readLaneConfig.value(sExSection + "/localIP", "").toString();
    m_nLocalPort = readLaneConfig.value(sExSection + "/localPort", 10002).toInt();
    m_sRtmpIP = readLaneConfig.value(sExSection + "/rtmpIP", "").toString();
    m_sRemoteServer = readLaneConfig.value(sExSection + "/serverIP", "").toString();
    m_nRemotePort = readLaneConfig.value(sExSection + "/serverport", 10001).toInt();
    m_sRemoteControlToken = readLaneConfig.value(sExSection + "/token", "").toString();
    m_bRemoteCloseLaneVideo = readLaneConfig.value(sExSection + "/closeLaneVideo", false).toBool();

    QString sClassSection = QString("VehPassTime");
    m_sVehClass = readLaneConfig.value(sClassSection + "/vehclass", QString("1100")).toString();
    if (m_sVehClass.length() < 4) {
        m_sVehClass = QString("1100");
    }
    QString sStopTime =
        readLaneConfig.value(sClassSection + "/stoptime", QString("02000500")).toString();
    if (sStopTime.length() >= 8) {
        m_sBeginTime = sStopTime.left(4);
        m_sEndTime = sStopTime.mid(4, 4);
    } else {
        m_sBeginTime = QString("0200");
        m_sEndTime = QString("0500");
    }
    DebugLog(QString("Permission VehClass:%1,beginTime:%2,endTime:%3")
                 .arg(m_sVehClass)
                 .arg(m_sBeginTime)
                 .arg(m_sEndTime));

    QString sVehLibSection = QString("vehlib");
    m_bUseVehLib = readLaneConfig.value(sVehLibSection + "/useVehlib", false).toBool();
    m_sVehLibUrl = readLaneConfig.value(sVehLibSection + "/vehlibUrl", "").toString();
    m_nScore = readLaneConfig.value(sVehLibSection + "/score", 80).toInt();
    m_nScoreEN = readLaneConfig.value(sVehLibSection + "/score", 100).toInt();
    m_nScoreEX = readLaneConfig.value(sVehLibSection + "/score", 80).toInt();

    // http://172.30.1.174:9998/auth/oauth/token?grant_type=password
    m_sQryTokenUrl = readLaneConfig.value(sVehLibSection + "/qryTokenUrl", "").toString();
    // http://172.30.1.174:9998/auth/oauth/token?grant_type=refresh_token
    m_sRefreshTokenUrl = readLaneConfig.value(sVehLibSection + "/refreshTokenUrl", "").toString();
    // http://172.30.1.174:9998/admin/vehicletype/saveVehicleType
    m_sSaveUrl = readLaneConfig.value(sVehLibSection + "/saveUrl", "").toString();

    QString sIOSection = QString("IOCard");
    m_nMaxTimeOnLoop = readLaneConfig.value(sIOSection + "/maxtimeonloop", 0).toInt();
    m_nMaxTimeOffLoop = readLaneConfig.value(sIOSection + "/maxtimeoffloop", 0).toInt();
    m_nMaxTimeOnBackLoop = readLaneConfig.value(sIOSection + "/maxtimeonbackloop", 600).toInt();
    GetUploadLogDate();

    sSection = QString("ETCDelay");
    m_sETCDelayUrl = readLaneConfig.value(sSection + QString("/url"), "").toString();
    m_bAutoDelay = readLaneConfig.value(sSection + QString("/ETCDelay"), false).toBool();

    // 读取ProDebPayment URL配置
    sSection = "ProDebPayment";
    m_sProDebPaymentUrl = readLaneConfig.value(sSection + "/url", "http://127.0.0.1:8080/ps").toString();

    GetMainDlg()->ShowLog(QString("读取配置文件完成"));
    return true;
}

bool CLaneInfo::SaveCfgItem(const QString &sKey, bool bValue)
{
    QSettings writeCfg(m_sFileName, QSettings::IniFormat);
    int nValue = bValue ? 1 : 0;
    writeCfg.setValue(sKey, nValue);
    return true;
}

bool CLaneInfo::SaveCfgItem(const QString sKey, const QString &sValue)
{
    QSettings writeCfg(m_sFileName, QSettings::IniFormat);
    writeCfg.setValue(sKey, sValue);
    DebugLog(QString("修改参数配置Key:[%1] Value:[%2]").arg(sKey).arg(sValue));
    return true;
}

quint8 CLaneInfo::ConverLaneTypeToTrans(int nType)
{
    if (LaneType_EtcExit == m_nLaneType || LaneType_EtcEntry == m_nLaneType) {
        return 1;
    } else if (LaneType_MtcEntry == m_nLaneType || LaneType_MtcExit == m_nLaneType) {
        if (nType!=3)
            return m_nLaneTypeHT;//原来返回3，20250401根据联网中心需求调整
        else
            return 3;
    } else
        return 3;
}

QString CLaneInfo::GetLaneName()
{
    if (IsEntryLane()) {
        return QString("入口%1").arg(m_nLaneId, 2, 10, QLatin1Char('0'));
    } else {
        return QString("出口%1").arg(m_nLaneId, 2, 10, QLatin1Char('0'));
    }
}

QString CLaneInfo::GetAppVer() { return m_sAppVer; }

QString CLaneInfo::GetSubVer()
{
    return m_sSubVer;
    /*
    if (m_bTestVer)
        return m_sSubVer;
    else
        return QString("");*/
}

//
//  20250319100
//               09 在抓拍数据处理完成后，停止机械臂的定时器
//
void CLaneInfo::CreateVersion(bool bTest)
{
    m_bTestVer = bTest;
    QSettings readLaneConfig(m_sFileName, QSettings::IniFormat);
    //车道基本配置参数
    QString sSection = "laneinfo";
    int nLaneType = readLaneConfig.value(sSection + "/lanetype").toInt();

    QString sTransVersion = QString("20250319100L");//QString("20241226100L");  // QString("20240903100L");
    QString sTransVersionForTest = QString("20250319100L");
    QString sVersion = QString("20250319100L");  // QString("20240903100L");
    QString sVersionForTest = QString("20250319100L");

    QString sSubVersion = QString("10");
    QString sSubVersionForTest = QString("10");
#ifdef E_PAPER
    sVersion = QString("20250319100L");
    sTransVersion = QString("20250319100L");
    sVersionForTest = QString("20250319100L");
    sTransVersionForTest = QString("20250319100L");
    sSubVersion = QString("10");  //增加国密扣费密钥初始值 0x41
    sSubVersionForTest = QString("10");
#endif

    if (bTest) {
        m_sSubVer = sSubVersionForTest;
        m_sAppVer = sVersionForTest;
        m_sTransVer = sTransVersionForTest;
    } else {
        m_sSubVer = sSubVersion;
        m_sAppVer = sVersion;
        m_sTransVer = sTransVersion;
    }

    if (3 == nLaneType || 1 == nLaneType) {
        m_sAppVer += QString("0");
        m_sTransVer += QString("0");
    } else {
        m_sAppVer += QString("1");
        m_sTransVer += QString("1");
    }

    if (m_bTestVer) {
        m_sAppVer += QString("T");
        m_sTransVer += QString("T");
    }
    return;  // sVersion;
}

/**
 * @brief 创建新的版本信息（带大版本号和小版本号参数）
 * @param bTest 是否为测试版本
 * @param nMajorVersion 大版本号，例如：20250319100L
 * @param nMinorVersion 小版本号，例如：10
 */
void CLaneInfo::CreateVersionNew(bool bTest, QString sMajorVersion, int nMinorVersion)
{
    m_bTestVer = bTest;
    QSettings readLaneConfig(m_sFileName, QSettings::IniFormat);
    //车道基本配置参数
    QString sSection = "laneinfo";
    int nLaneType = readLaneConfig.value(sSection + "/lanetype").toInt();

    // 处理大版本号，如果为空则使用当前日期时间生成
//    QString sMajorVersion = ;
//    if (nMajorVersion == 0) { // 假设0代表未提供或无效
//        // 使用当前日期和时间（精确到分钟）作为大版本号，格式如 YYYYMMDDHHmmL
//        sMajorVersion = QDateTime::currentDateTime().toString("yyyyMMddhhmm") + "L";
//    } else {
//        sMajorVersion = QString::number(nMajorVersion);
//        if (!sMajorVersion.endsWith("L", Qt::CaseInsensitive)) { // 确保末尾有L
//             sMajorVersion += "L";
//        }
//    }

    // 处理小版本号，如果为空则默认为1
    QString sSubVersionValue;
    if (nMinorVersion == 0) { // 假设0代表未提供或无效
        sSubVersionValue = "01";
    } else {
        sSubVersionValue = QString::number(nMinorVersion);
    }

    // 根据是否为测试版本，可能需要不同的版本号字符串，这里暂时保持一致
    QString sTransVersion = sMajorVersion;
    QString sTransVersionForTest = sMajorVersion;
    QString sVersion = sMajorVersion;
    QString sVersionForTest = sMajorVersion;

    QString sSubVersion = sSubVersionValue;
    QString sSubVersionForTest = sSubVersionValue;

#ifdef E_PAPER
    // 如果定义了 E_PAPER 宏，可能需要特殊的版本处理，此处保持和原函数逻辑类似
    // 但请注意，原函数中 E_PAPER 部分的版本号是硬编码的，
    // 如果需要根据传入参数动态调整，则需要修改此部分逻辑
    sVersion = sMajorVersion; //QString("20250319100L");
    sTransVersion = sMajorVersion; //QString("20250319100L");
    sVersionForTest = sMajorVersion; //QString("20250319100L");
    sTransVersionForTest = sMajorVersion; //QString("20250319100L");
    sSubVersion = sSubVersionValue; //QString("10");  //增加国密扣费密钥初始值 0x41
    sSubVersionForTest = sSubVersionValue; //QString("10");
#endif

    if (bTest) {
        m_sSubVer = sSubVersionForTest;
        m_sAppVer = sVersionForTest;
        m_sTransVer = sTransVersionForTest;
    } else {
        m_sSubVer = sSubVersion;
        m_sAppVer = sVersion;
        m_sTransVer = sTransVersion;
    }

    // 根据车道类型调整版本号后缀
    if (3 == nLaneType || 1 == nLaneType) { // MTC出口 或 MTC入口
        m_sAppVer += QString("0");
        m_sTransVer += QString("0");
    } else { // ETC出口 或 ETC入口或其他
        m_sAppVer += QString("1");
        m_sTransVer += QString("1");
    }

    // 如果是测试版本，增加 'T' 后缀
    if (m_bTestVer) {
        m_sAppVer += QString("T");
        m_sTransVer += QString("T");
    }
    return;
}

QString CLaneInfo::GetSysVersion()
{
#ifdef Q_OS_WIN32
    QSysInfo::WinVersion ver = QSysInfo::windowsVersion();
    if (ver == 0x0030)
        return QString("windows XP");
    else if (ver == 0x0080)
        return QString("windows VISTA");
    else if (ver == 0x0090)
        return QString("windows7");
    else if (ver == 0x00a0)
        return QString("windows10");
    else
        return QString("windows XP");
#else
    return QString("Linux");
#endif
}

bool CLaneInfo::bHaveBackDev() { return m_nRsuNum >= 1 || IsMTCLane(); }

bool CLaneInfo::bHaveFrontDev() { return m_nRsuNum > 1 || IsETCLane(); }

void CLaneInfo::SetStationID(int nStationID)
{
    m_nStationID = nStationID;

    QString sHex = QString("3601%1").arg(quint16(m_nStationID % 3600000), 4, 16, QLatin1Char('0'));
    m_sHexStationID = sHex.toUpper();
    m_sHexLaneId =
        QString("%1%2").arg(m_sHexStationID).arg(quint8(m_nLaneId % 1000), 2, 16, QLatin1Char('0'));
    m_sHexLaneId = m_sHexLaneId.toUpper();
}

void CLaneInfo::SetGBStationInfo(const QString &sGBStationId, const QString &sGBLaneId)
{
    m_sGBLaneId = sGBLaneId;
    m_sGBStationId = sGBStationId;
}

void CLaneInfo::SetStationName(const QString &sStationName)
{
    m_sStationName = sStationName;
    m_OrgCode.sOrgSName = sStationName;
}

void CLaneInfo::SetFeeDllCurPath(const QString &feeDllCurPath)
{
    this->m_sFeeDllCurPath = feeDllCurPath;
    QSettings writeLaneConfig(m_sFileName, QSettings::IniFormat);
    writeLaneConfig.setValue(QString("laneinfo/feeDllCurPath"), QVariant(feeDllCurPath));
}
void CLaneInfo::GetParaUrl(QString &sUrl, QString &sBinFileAuth)
{
    sUrl = m_sParaUrl;
    sBinFileAuth = m_sBinFileAuth;
}

void CLaneInfo::GetDataUrl(QString &sDataUrl, QString &sBinFileAuth, QString &sBakUrl)
{
    sDataUrl = m_sDataUrl;
    sBinFileAuth = m_sBinFileAuth;
    sBakUrl = m_sDataUrlBak;
}

qint32 CLaneInfo::GetVehClassDef(int nPos)
{
    if (1 == nPos)
        return VC_Truck5;
    else
        return VC_None;
}

void CLaneInfo::SetOrgCode(const COrgCode &OrgCode, const QString &sRoadName)
{
    m_OrgCode = OrgCode;
    m_sStationName = OrgCode.sOrgSName;
    m_sRoadName = sRoadName;
}

int CLaneInfo::GetBL_SubCenter() { return m_OrgCode.sBL_Road.toInt(); }

void CLaneInfo::GetVlprSmInfo(QString &sBatchNo, int &vehicleDataCount, int &vehiclePicCount)
{
    sBatchNo = m_sVlprBatchNo;
    vehicleDataCount = m_nDataCnt;
    vehiclePicCount = m_nPicCnt;
}

void CLaneInfo::SetVlprSmInfo(const QString &sBatchNo, const int &vehicleDataCount,
                              const int &vehiclePicCount)
{
    m_sVlprBatchNo = sBatchNo;
    m_nDataCnt = vehicleDataCount;
    m_nPicCnt = vehiclePicCount;
    QSettings writeLaneConfig(m_sFileName, QSettings::IniFormat);
    //车道基本配置参数
    QString sSection = "vlprsum";
    writeLaneConfig.setValue(sSection + "/batchno", m_sVlprBatchNo);
    writeLaneConfig.setValue(sSection + "/datacnt", m_nDataCnt);
    writeLaneConfig.setValue(sSection + "/piccnt", m_nPicCnt);
}

QDate CLaneInfo::GetUploadLogDate()
{
    // QDate curDate = QDate::currentDate();
    // return curDate;
    QString sSection = "uploadLogDate";
    QSettings readLaneConfig(m_sFileName, QSettings::IniFormat);
    QString str = readLaneConfig.value(sSection + "/uploadDate", QString("")).toString();
    QDate uploadDate = QDate::fromString(str, "yyyy-MM-dd");
    if (!uploadDate.isValid()) {
        uploadDate = QDate::currentDate();
        uploadDate = uploadDate.addDays(-1);
        SetUploadLogDate(uploadDate);
        /*
        m_UploadLogDate = uploadDate;
        QSettings writeLaneConfig(m_sFileName, QSettings::IniFormat);
        //车道基本配置参数
        QString sSection = "uploadLogDate";
        writeLaneConfig.setValue(sSection + "/uploadDate", m_UploadLogDate.toString("yyyy-MM-dd"));
        */
    } else {
        m_UploadLogDate = uploadDate;
    }
    return m_UploadLogDate;
}

void CLaneInfo::SetUploadLogDate(QDate date)
{
    if (!date.isValid()) return;
    m_UploadLogDate = date;
    QSettings writeLaneConfig(m_sFileName, QSettings::IniFormat);
    //车道基本配置参数
    QString sSection = "uploadLogDate";
    writeLaneConfig.setValue(sSection + "/uploadDate", m_UploadLogDate.toString("yyyy-MM-dd"));
}

void CLaneInfo::AddVirStationInfo(const CVirGantryInfo &virStaInfo)
{
    m_VirStationList.push_back(virStaInfo);
}

void CLaneInfo::AddOpenStationInfo(const QList<CVirGantryInfo> gantryList)
{
    QMutexLocker locker(&m_virStationMt);
    if (m_VirStationList.size() > 1) {
        CVirGantryInfo gantryInfo = m_VirStationList.front();
        m_VirStationList.clear();
        m_VirStationList.push_back(gantryInfo);
        m_VirStationList.append(gantryList);
    } else {
        m_VirStationList.append(gantryList);
    }
}

bool CLaneInfo::IsUVeh(const QString &sHex)
{
    QMutexLocker locker(&m_virStationMt);
    if (m_VirStationList.isEmpty()) return false;
    QList<CVirGantryInfo>::iterator it = m_VirStationList.begin();
    for (; it != m_VirStationList.end(); ++it) {
        if (it->sStationHex == sHex) return true;
    }
    return false;
}

bool CLaneInfo::CheckReplaceWriteCard()
{
    QString curTime = QDateTime::currentDateTime().toString("yyyyMMdd");
    if (m_bOpenLane && (curTime >= m_sReplaceTime)) {
        return true;
    } else
        return false;
}

bool CLaneInfo::bNoWeightDev() { return IsExitLane() && m_bNoWeightDev; }

int CLaneInfo::GetMaxDriveDays() { return m_nMaxDays; }

bool CLaneInfo::bCheckWeight()
{
    if (IsExitLane()) {
        if (m_bNoWeightDev) return false;
        return m_bCheckWeight;
    } else
        return true;
}

bool CLaneInfo::CheckVehPassPermit(int nVehClass, const QDateTime &dateTime)
{
    if (nVehClass >= 1 && nVehClass <= 4) {
        QString sCurTime = dateTime.toString("HHmm");
        bool bPermit = QChar('0') != m_sVehClass.at(nVehClass - 1);
        if (bPermit) return true;
        if (m_sEndTime > m_sBeginTime) {
            if (sCurTime >= m_sBeginTime && sCurTime < m_sEndTime) {
                return bPermit;
            }
        } else {
            if (sCurTime >= m_sBeginTime || sCurTime < m_sEndTime) {
                return bPermit;
            }
        }
    }
    return true;
}

void CLaneInfo::GetTokenUrl(QString &sQryUrl, QString &sRefreshUrl, QString &sSaveUrl)
{
    sQryUrl = m_sQryTokenUrl;
    sRefreshUrl = m_sRefreshTokenUrl;
    sSaveUrl = m_sSaveUrl;
    return;
}

void CLaneInfo::GetMaxLoopTime(int &nMaxOn, int nMaxOff)
{
    nMaxOn = m_nMaxTimeOnLoop;
    nMaxOff = m_nMaxTimeOffLoop;
}

bool CLaneInfo::bETCStopTruck() { return m_bETCStopTruck; }

bool CLaneInfo::bFuzztMatch() { return m_bFuzzyMatch; }

bool CLaneInfo::bAutoDelay()
{
    if (IsEntryLane())
        return m_bAutoDelay;
    else
        return false;
}

void CLaneInfo::LoadLogFileInfo()
{
    QString sLogPath;
    qint32 nLogLevel;

    QString sCfgFileName = m_sFileName;
    QSettings LaneConfig(sCfgFileName, QSettings::IniFormat);
    QString sSection = "log";
    bool bOk;
    nLogLevel = LaneConfig.value(sSection + "/loglevel", 4).toInt(&bOk);
    if (!bOk) nLogLevel = 4;

    QString sDefaultLogPath = QString("%1/log").arg(GetCurrentPath());
    sLogPath = LaneConfig.value(sSection + "/logpath", sDefaultLogPath).toString();

    m_nLogLevel = nLogLevel;
    m_sLogPath = sLogPath;
    return;
}
