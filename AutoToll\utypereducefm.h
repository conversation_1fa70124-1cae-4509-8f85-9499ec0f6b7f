#ifndef UCARREDUCE_H
#define UCARREDUCE_H

#include "baseopwidget.h"



#include "baseopwidget.h"
#include "lineedit.h"

class CUTypeReduceFm : public CBaseOpWidget
{
    Q_OBJECT

public:
    enum {
        InputMoney_UFree,
        InputMoney_Owe
    };
public:
    explicit CUTypeReduceFm(QWidget *parent = 0);
    ~CUTypeReduceFm();
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);

private:
     QLabel *m_pQryLabel;
     CNumLineEdit *m_pLineEdit;
     quint32 m_nActualMoney;

     int m_nCurCashMoney;
     int m_nCurCashCardMoney;
     int m_nType;
private:

private:
    void Init();
    bool VerifyActualMoney();

public:
    void SetCurTollMoney(int nCurCashMoney, int nCurCashCardMoney);
    bool GetActualMoney(quint32 &nActualMoney);
    bool <PERSON><PERSON><PERSON><PERSON>(int nType,int nCashMoney,int nCardMoney);
};


#endif // UCARREDUCE_H
