#ifndef FORMSHOWPROMPT_H
#define FORMSHOWPROMPT_H

#include <QTimer>

#include "baseform.h"

class FormShowPrompt : public BaseForm {
    Q_OBJECT
public:
    explicit FormShowPrompt(QWidget *parent = 0);

private:
    QString m_promptMsg;

    //显示次数
    int m_nWarnCount;

    QTimer m_timer;

protected:
    void paintEvent(QPaintEvent *);
protected slots:
    void OnSplash();
public:
    void HideForm();
    void ShowPrompt(const QString &sText, bool bPlaySound);
    void ShowPaying(const QString &sText);
    void ShowChecking(const QString &sText);//正在核对信息，请稍候
    void ShowChardError(const QString &sText);//读卡失败，请重新插入有效的卡
    void ShowCheckError(const QString &sText);//核对信息失败，
    void PayFail(const QString &sText);
    void Password(const QString &sText);
    // BaseForm interface

};

#endif  // FORMSHOWPROMPT_H
