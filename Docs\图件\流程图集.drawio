<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="28.0.9">
  <diagram name="第 1 页" id="ppr2lVGI6lqeU4_qDw4P">
    <mxGraphModel dx="1701" dy="932" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="PIpVFty-j-eXB5_ukM9O-10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-1" target="PIpVFty-j-eXB5_ukM9O-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-1" value="开始计费" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="220" y="220" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-2" target="PIpVFty-j-eXB5_ukM9O-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-2" value="计算累计金额-OBU内金额累加出口承载门架金额" style="rounded=0;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="210" y="300" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-3" target="PIpVFty-j-eXB5_ukM9O-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-14" value="大于" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-13">
          <mxGeometry x="-0.0545" y="1" relative="1" as="geometry">
            <mxPoint x="-1" y="14" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-3" target="PIpVFty-j-eXB5_ukM9O-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-17" value="等于" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-16">
          <mxGeometry x="0.0756" y="-1" relative="1" as="geometry">
            <mxPoint x="1" y="9" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-3" value="通行省份个数与1比较" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="190" y="400" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-4" target="PIpVFty-j-eXB5_ukM9O-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-23" value="大于上限" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-20">
          <mxGeometry x="0.3099" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-4" target="PIpVFty-j-eXB5_ukM9O-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-25" value="范围内" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-24">
          <mxGeometry x="-0.5685" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-4" target="PIpVFty-j-eXB5_ukM9O-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-27" value="低于下限" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-26">
          <mxGeometry x="-0.521" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-4" value="多省最小费额比较" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="50" y="515" width="170" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-5" target="PIpVFty-j-eXB5_ukM9O-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-19" value="低于下限" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-18">
          <mxGeometry x="-0.2638" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-5" target="PIpVFty-j-eXB5_ukM9O-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-5" target="PIpVFty-j-eXB5_ukM9O-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-31" value="范围内" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-30">
          <mxGeometry x="-0.3945" relative="1" as="geometry">
            <mxPoint y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-5" value="本省最小费额比较" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="334" y="515" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-6" value="最小费额计费" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="236" y="790" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-7" value="通行介质计费" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="236" y="720" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-8" value="人工处理" style="rounded=1;whiteSpace=wrap;html=1;fontSize=14;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="236" y="660" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-9" target="PIpVFty-j-eXB5_ukM9O-7">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="530" y="735" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-33" value="U行且小于等于半小时" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-32">
          <mxGeometry x="-0.6311" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;fontSize=14;" edge="1" parent="1" source="PIpVFty-j-eXB5_ukM9O-9" target="PIpVFty-j-eXB5_ukM9O-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-35" value="U形且大于半小时" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" vertex="1" connectable="0" parent="PIpVFty-j-eXB5_ukM9O-34">
          <mxGeometry x="-0.6802" y="-2" relative="1" as="geometry">
            <mxPoint y="37" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PIpVFty-j-eXB5_ukM9O-9" value="U行车判断" style="rhombus;whiteSpace=wrap;html=1;fontFamily=Times New Roman;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="530" y="580" width="100" height="80" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
