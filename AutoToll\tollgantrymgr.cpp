#include "tollgantrymgr.h"

#include "etclanectrl.h"
#include "laneinfo.h"
#include "paramfilemgr.h"
//#include "globalconfig.h"

CTollGantryMgr::CTollGantryMgr(QObject *parent) : QObject(parent)
{
    memset(m_GantrySumInfos, 0, sizeof m_GantrySumInfos);
}

void CTollGantryMgr::SetBaseInfo(const QString &sTollGantry, const QString &sTollGantryHex,
                                 const QString &sTollIntervals, const QString &sGantryName,
                                 int nGantryType)
{
    m_tollGantryId = sTollGantry;

    m_sHex = sTollGantryHex;
    quint8 bRawId[3];
    memset(bRawId, 0, sizeof bRawId);
    while (m_sHex.length() < 6) {
        m_sHex = "0" + m_sHex;
    }
    Hex2Raw(bRawId, m_sHex.toAscii().data(), 6);

    m_bDirection = bRawId[0] & 0x01;
    m_nGantrySn = m_tollGantryId.right(3).left(2).toInt();  // Byte2Long(bRawId+1,2,false);

    m_tollIntervals = sTollIntervals;
    m_sCantryName = sGantryName;
    m_nGantryType = nGantryType;
}

/**
 * @brief
 * @param
 * @return
 */

quint32 CTollGantryMgr::GetGantryDirectId() { return m_bDirection * 100 + m_nGantrySn % 100; }

bool CTollGantryMgr::GetCurGantryInfo(const QString &sGantryHex, CVirGantryInfo &gantryInfo,
                                      bool &bOpenGantry)
{
    QMutexLocker locker(&m_Mutex);
    if (m_GantryList.isEmpty()) {
        DebugLog("gantrylist empty");
        return false;
    }

    bOpenGantry = false;

    if (sGantryHex.isEmpty()) {
        gantryInfo = m_GantryList.front();
        return true;
    }

    QList<CVirGantryInfo>::iterator it = m_GantryList.begin();
    for (; it != m_GantryList.end(); ++it) {
        if (it->sGantryHex == sGantryHex) {
            gantryInfo = *it;
            bOpenGantry = true;
            return true;
        }
    }
    DebugLog(QString("取开放式门架%1信息失败,改为当前门架").arg(sGantryHex));
    gantryInfo = m_GantryList.front();
    return true;
}

void CTollGantryMgr::CheckBatchChanged(QString sNewBatch)
{
    QMutexLocker locker(&m_Mutex);
    int nIndex = 0;
    CGantrySumInfo_H gantrySumInfos[MAX_GANTRYNUM];
    memset(gantrySumInfos, 0, sizeof gantrySumInfos);

    QList<CVirGantryInfo>::iterator it = m_GantryList.begin();
    for (; it != m_GantryList.end(); ++it) {
        QString sLastBatch = QString::fromAscii(m_GantrySumInfos[nIndex].szCollectHourBatch);
        if (sLastBatch.isEmpty() || sNewBatch > sLastBatch) {
            if (sLastBatch.length() >= 10) {
                if (!Ptr_ETCCtrl->SendGantrySumInfo(m_GantrySumInfos[nIndex], it->sGBLaneId)) {
                    ErrorLog("门架小时汇总数据发送错误");
                }
                SleeperThread::msleep(1500);
            }
            qsnprintf(gantrySumInfos[nIndex].szGantryId, sizeof gantrySumInfos[nIndex].szGantryId,
                      it->sGantryId.toAscii().constData());
            qsnprintf(gantrySumInfos[nIndex].szCollectHourBatch,
                      sizeof gantrySumInfos[nIndex].szCollectHourBatch,
                      sNewBatch.toAscii().constData());
            memcpy(gantrySumInfos[nIndex].szCollectDate, gantrySumInfos[nIndex].szCollectHourBatch,
                   8);
            memcpy(&m_GantrySumInfos[nIndex], &gantrySumInfos[nIndex], sizeof(CGantrySumInfo_H));
        }
        nIndex++;
    }
    //    memcpy(m_GantrySumInfos,gantrySumInfos,sizeof gantrySumInfos);
    QByteArray sumData;
    sumData.append((char *)&m_GantrySumInfos[0], sizeof(CGantrySumInfo_H) * m_GantryList.size());
    Ptr_ETCCtrl->SaveGanteyHourSumData(sumData);
    return;
}

void CTollGantryMgr::CheckBatchChanged_New(QString sNewBatch)
{
    QMutexLocker locker(&m_Mutex);

    bool bSave = false;
    QList<CVirGantryInfo>::iterator it = m_GantryList.begin();
    for (; it != m_GantryList.end(); ++it) {
        QMap<QString, CGantrySumInfo_H>::iterator sumIt = m_GantrySumInfoMap.find(it->sGantryId);
        QString sLastBatch;
        if (sumIt != m_GantrySumInfoMap.end()) {
            sLastBatch = QString::fromAscii(sumIt->szCollectHourBatch);
        }
        CGantrySumInfo_H gantrySumInfo;
        memset(&gantrySumInfo, 0, sizeof gantrySumInfo);
        if (sLastBatch.isEmpty() || sNewBatch > sLastBatch) {
            if (sLastBatch.length() >= 10) {
                if (!Ptr_ETCCtrl->SendGantrySumInfo(sumIt.value(), it->sGBLaneId)) {
                    ErrorLog("门架小时汇总数据发送错误");
                }
                SleeperThread::msleep(1500);
            }
            qsnprintf(gantrySumInfo.szGantryId, sizeof gantrySumInfo.szGantryId,
                      it->sGantryId.toAscii().constData());
            qsnprintf(gantrySumInfo.szCollectHourBatch, sizeof gantrySumInfo.szCollectHourBatch,
                      sNewBatch.toAscii().constData());
            memcpy(gantrySumInfo.szCollectDate, gantrySumInfo.szCollectHourBatch, 8);
            if (sumIt != m_GantrySumInfoMap.end()) {
                *sumIt = gantrySumInfo;
            } else {
                m_GantrySumInfoMap.insert(it->sGantryId, gantrySumInfo);
            }
            bSave = true;
        }
    }

    if (bSave) SaveGantrySumData();
    return;
}

//启动时
void CTollGantryMgr::SetGantrySumInfo(const QByteArray &SumInfo)  // const CGantrySumInfo_H
                                                                  // &SumInfo)
{
    CGantrySumInfo_H sumInfos[10];
    memset(sumInfos, 0, sizeof sumInfos);

    quint32 nSize = (quint32)SumInfo.size();
    if ((nSize > sizeof sumInfos) || (nSize < sizeof(CGantrySumInfo_H))) return;

    const CGantrySumInfo_H *pSumInfo = (CGantrySumInfo_H *)SumInfo.constData();
    QString sGantryId = QString::fromAscii(pSumInfo->szGantryId);
    if (sGantryId != m_tollGantryId) {
        return;
    } else {
        QMutexLocker locker(&m_Mutex);
        memcpy(&m_GantrySumInfos[0], SumInfo.constData(), SumInfo.size());
    }

    return;
}

void CTollGantryMgr::SetGantrySumInfo_New(const QByteArray &SumInfo)
{
    int nSumSize = sizeof(CGantrySumInfo_H);
    int nGantryNum = SumInfo.size() / nSumSize;
    if (0 == nGantryNum) return;
    const CGantrySumInfo_H *pSumInfo = (CGantrySumInfo_H *)SumInfo.constData();

    m_GantrySumInfoMap.clear();
    for (int i = 0; i < nGantryNum; ++i) {
        const CGantrySumInfo_H *pTmp = pSumInfo + i;
        QString sGantryId = QString::fromLocal8Bit(pTmp->szGantryId);
        if (sGantryId.length() > 0) {
            m_GantrySumInfoMap.insert(sGantryId, *pTmp);
        }
    }
    return;
}

void CTollGantryMgr::GantrySum(const CDoorWaste_ETCTU &etctu, int nIndex)
{
    QMutexLocker locker(&m_Mutex);
    if (nIndex >= MAX_GANTRYNUM) return;
    m_GantrySumInfos[nIndex].batchCount++;

    QString sVehClassName = GetVehClassName((CVehClass)etctu.vehicleType);
    if (sVehClassName.length() > 0) {
        if (MediaType_OBU == etctu.mediaType) {
            if (etctu.vehicleType < 30) {
                m_GantrySumInfos[nIndex].TypeCount[etctu.vehicleType].etcCount += 1;
            }
        } else if (MediaType_CPC == etctu.mediaType) {
            if (etctu.vehicleType < 30)
                m_GantrySumInfos[nIndex].TypeCount[etctu.vehicleType].cpcCount += 1;
        }
    }
    QString sVehTypeName = GetUnionVehTypeName((CUnionVehType)etctu.vehicleClass);
    if (sVehTypeName.length() > 0) {
        if (MediaType_OBU == etctu.mediaType) {
            if (etctu.vehicleClass < 30)
                m_GantrySumInfos[nIndex].ClassCount[etctu.vehicleClass].etcCount += 1;
        } else if (MediaType_CPC == etctu.mediaType) {
            if (etctu.vehicleClass < 30)
                m_GantrySumInfos[nIndex].ClassCount[etctu.vehicleClass].cpcCount += 1;
        }
    }

    if (0 == etctu.tradeResult) {
        if (etctu.mediaType == MediaType_OBU) {
            if (nIndex < 10) {
                m_GantrySumInfos[nIndex].etcSucessCount += 1;
                m_GantrySumInfos[nIndex].etcSuccessFee += etctu.fee;
            }
        } else if (MediaType_CPC == etctu.mediaType) {
            if (nIndex < 10) {
                m_GantrySumInfos[nIndex].cpcSuccessCount += 1;
                m_GantrySumInfos[nIndex].cpcSuccessFee += etctu.fee;
            }
        }
    } else {
        if (MediaType_OBU == etctu.mediaType)
            m_GantrySumInfos[nIndex].etcFailCount += 1;
        else if (MediaType_CPC == etctu.mediaType)
            m_GantrySumInfos[nIndex].cpcFailCount += 1;
    }
    return;
}

void CTollGantryMgr::GantrySum_New(const CDoorWaste_ETCTU &etctu, const QString &sGantryId)
{
    QMutexLocker locker(&m_Mutex);

    QMap<QString, CGantrySumInfo_H>::iterator it = m_GantrySumInfoMap.find(sGantryId);
    if (it == m_GantrySumInfoMap.end()) return;

    CGantrySumInfo_H sumInfo = it.value();
    GantrySum_Single(etctu, sumInfo);
    *it = sumInfo;
    return;
}

void CTollGantryMgr::GetGantrySumInfo(QByteArray &SumInfo)
{
    QMutexLocker locker(&m_Mutex);
    SumInfo.append((char *)&m_GantrySumInfos[0], m_GantryList.size() * sizeof(CGantrySumInfo_H));
    return;
}

void CTollGantryMgr::GetGantrySumInfo_New(QByteArray &sumData)
{
    QMutexLocker locker(&m_Mutex);
    QList<CVirGantryInfo>::iterator it = m_GantryList.begin();
    for (; it != m_GantryList.end(); ++it) {
        QString sKey = it->sGantryId;
        if (m_GantrySumInfoMap.contains(sKey)) {
            CGantrySumInfo_H gantrySum = m_GantrySumInfoMap.value(sKey);
            sumData.append((char *)&gantrySum, sizeof gantrySum);
        }
    }
    return;
}

bool CTollGantryMgr::CheckNeedGrantryCalcFee()
{
    if (m_tollGantryId.isEmpty()) {
        return false;
    }
    CalcFee *pCalcFee = (CalcFee *)CParamFileMgr::GetParamFile(cfFareDll);
    if (!pCalcFee) return false;
    return true;
}

void CTollGantryMgr::AddGantryInfo(const CVirGantryInfo &gantryInfo)
{
    QMutexLocker locker(&m_Mutex);
    m_GantryList.push_back(gantryInfo);
}

void CTollGantryMgr::AddOpenGantryInfo(const QList<CVirGantryInfo> &gantryList)
{
    QMutexLocker locker(&m_Mutex);
    if (m_GantryList.size() > 1) {
        CVirGantryInfo curGantry = m_GantryList.front();
        m_GantryList.clear();
        m_GantryList.push_back(curGantry);
        m_GantryList.append(gantryList);
    } else {
        m_GantryList.append(gantryList);
    }
    return;
}

void CTollGantryMgr::GantrySum_Single(const CDoorWaste_ETCTU &etctu, CGantrySumInfo_H &gantrySum)
{
    gantrySum.batchCount++;

    QString sVehClassName = GetVehClassName((CVehClass)etctu.vehicleType);
    if (sVehClassName.length() > 0) {
        if (MediaType_OBU == etctu.mediaType) {
            if (etctu.vehicleType < 30) {
                gantrySum.TypeCount[etctu.vehicleType].etcCount += 1;
            }
        } else if (MediaType_CPC == etctu.mediaType) {
            if (etctu.vehicleType < 30) gantrySum.TypeCount[etctu.vehicleType].cpcCount += 1;
        }
    }
    QString sVehTypeName = GetUnionVehTypeName((CUnionVehType)etctu.vehicleClass);
    if (sVehTypeName.length() > 0) {
        if (MediaType_OBU == etctu.mediaType) {
            if (etctu.vehicleClass < 30) gantrySum.ClassCount[etctu.vehicleClass].etcCount += 1;
        } else if (MediaType_CPC == etctu.mediaType) {
            if (etctu.vehicleClass < 30) gantrySum.ClassCount[etctu.vehicleClass].cpcCount += 1;
        }
    }

    if (0 == etctu.tradeResult) {
        if (etctu.mediaType == MediaType_OBU) {
            gantrySum.etcSucessCount += 1;
            gantrySum.etcSuccessFee += etctu.fee;
        } else if (MediaType_CPC == etctu.mediaType) {
            gantrySum.cpcSuccessCount += 1;
            gantrySum.cpcSuccessFee += etctu.fee;
        }
    } else {
        if (MediaType_OBU == etctu.mediaType)
            gantrySum.etcFailCount += 1;
        else if (MediaType_CPC == etctu.mediaType)
            gantrySum.cpcFailCount += 1;
    }
    return;
}

bool CTollGantryMgr::SaveGantrySumData()
{
    QByteArray sumData;
    QList<CVirGantryInfo>::iterator it = m_GantryList.begin();
    for (; it != m_GantryList.end(); ++it) {
        QString sKey = it->sGantryId;
        if (m_GantrySumInfoMap.contains(sKey)) {
            CGantrySumInfo_H gantrySum = m_GantrySumInfoMap.value(sKey);
            sumData.append((char *)&gantrySum, sizeof gantrySum);
        }
    }
    // sumData.append((char *)&m_GantrySumInfos[0], sizeof(CGantrySumInfo_H) * m_GantryList.size());
    return Ptr_ETCCtrl->SaveGanteyHourSumData(sumData);
}
