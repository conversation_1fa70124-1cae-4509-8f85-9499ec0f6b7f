#ifndef UISERVER_H
#define UISERVER_H
#include <QTcpServer>
#include <QTcpSocket>
#include <qobject>

#include "uimessage.h"

class UIServer : public QObject {
    Q_OBJECT
public:
    UIServer();
    ~UIServer();

public:
    enum HELP_TYPE
    {
        HT_OutOfService,
        HT_WaitVehicleArrive,
        HT_InsertCard,
        HT_Waiting,  //正在处理，请耐心等待。
        HT_Login,    //上班
        HT_Prompt,   //提示
        HT_Password,    //提示输入密码
        HT_PayFail,   //支付失败
        HT_Paying,   //正在支付，请稍候
        HT_Checking, //正在核对信息，请稍候
        HT_CardError,   //读卡失败，请重新插入有效的卡
        HT_CheckError  //核对信息失败，
    };

protected slots:
    void OnNewConnection();
    void OnClientReadReady();
    void OnClientDisconnect();
signals:
    void NotifyRemoteCmd(quint8 bCmdType);
    void NotifyNewConnection(const QString &sIP);
    void NotifyDisConnected(const QString &sIP);

    void NotifyLaneInfo(const QString stationName, const QString laneName);
    void NotifyHelpMsg(const QString stationName, const QString laneName, quint32 nHelpType, const QString helpMsg);
    void NotifyTollInfo(const QString vlp, const QString vehClassName, const QString vehTypeName, const QString enStationName, quint32 tollMoney,
                        bool bBeforePay, quint8 channelType);
    void NotifyTicketInfo(const QString vlp, const QString vehClassName, const QString vehTypeName,
                          const QString enStationName, quint32 tollMoney, quint8 channelType, const QString qrCode);

protected:
    void ProcessData(QTcpSocket *pSocket);
    //处理车道信息
    int handleMsgLaneInfo(QTcpSocket *pSocket, const struct UIM_MsgHead *pMsgHead, QByteArray msg);
    //处理提示界面
    int handleMsgHelp(QTcpSocket *pSocket, const struct UIM_MsgHead *pMsgHead, QByteArray msg);
    //处理收费信息
    int handleMsgTollInfo(QTcpSocket *pSocket, const struct UIM_MsgHead *pMsgHead, QByteArray msg);
    //处理发票信息
    int handleMsgTicket(QTcpSocket *pSocket, const struct UIM_MsgHead *pMsgHead, QByteArray msg);

public:
    //初始化服务
    bool StartServer();
    //关闭服务
    bool StopServer();

protected:
    QTcpServer *m_pServer;
    QList<QTcpSocket *> m_lstClients;
};

#endif  // UISERVER_H
