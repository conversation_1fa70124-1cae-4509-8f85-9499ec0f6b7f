#include "utypereducefm.h"

CUTypeReduceFm::CUTypeReduceFm(QWidget *parent) :
    CBaseOpWidget(parent)
{
    m_pQryLabel = NULL;
    m_pLineEdit = NULL;
    m_nActualMoney = 0;
    m_nCurCashMoney = 0;
    m_nCurCashCardMoney = 0;
    m_nType = InputMoney_UFree;
    Init();
    filterChildrenKeyEvent();
}

CUTypeReduceFm::~CUTypeReduceFm()
{
}

void CUTypeReduceFm::Init()
{
    CBaseOpWidget::InitUI();
    QString sTitle =InputMoney_Owe==m_nType? QString("欠费"):QString("U型车减免");
    CBaseOpWidget::SetTitle(sTitle);

  //  QPalette pa;
  //  pa.setColor(QPalette::WindowText, QColor(0,0,255));
    QFont ftText;
    ftText.setFamily(QString::fromUtf8("微软雅黑"));
    ftText.setPixelSize(20);
    ftText.setStyleStrategy(QFont::PreferAntialias);

    if(NULL == m_pQryLabel)
        m_pQryLabel = new QLabel(this);
    m_pQryLabel->setGeometry(30,130, 100, 40);
    m_pQryLabel->setAlignment(Qt::AlignLeft);
    m_pQryLabel->setFont(ftText);
    m_pQryLabel->setText("实收金额");

    if(NULL == m_pLineEdit)
        m_pLineEdit = new CNumLineEdit(Type_Num, 20, this);
    m_pLineEdit->setGeometry(140,130, this->width()-220, 40);
    m_pLineEdit->setAlignment(Qt::AlignLeft);
    m_pLineEdit->setFont(ftText);
    m_pLineEdit->setFocus();
    m_pLineEdit->setMaxLength(5);

    if(InputMoney_UFree== m_nType)
        CBaseOpWidget::SetMessage("请输入实收金额，并按【确定】键");
    else if(InputMoney_Owe==m_nType)
        SetMessage("请输入实收金额后,按【现金】键");

}

void CUTypeReduceFm::SetCurTollMoney(int nCurCashMoney, int nCurCashCardMoney)
{
    m_nCurCashMoney = nCurCashMoney/100;
    m_nCurCashCardMoney = nCurCashCardMoney/100;
}

// 获取减免后金额
bool CUTypeReduceFm::GetActualMoney(quint32 &nActualMoney)
{
    nActualMoney = 0;
//    if(m_nActualMoney >= 0)
        nActualMoney = m_nActualMoney;
//    else
//        return false;

    return true;
}

bool CUTypeReduceFm::DoMoneyFree(int nType,int nCashMoney, int nCardMoney)
{
    SetCurTollMoney(nCashMoney,nCardMoney);
    m_nType = nType;
    Init();
    return Rlt_OK== doModalShow();
}

// 当前输入的实际费用与原来费用对比
bool CUTypeReduceFm::VerifyActualMoney()
{
    // 实际费用应大于等于卡成本，小于等于总费用
    if(m_nActualMoney < uint(m_nCurCashCardMoney)) {
         CBaseOpWidget::ShowErrorMsg("实收金额应大于卡成本,请重新输入");
         return false;
    }

    if(m_nActualMoney >= uint(m_nCurCashMoney+m_nCurCashCardMoney)) {
        CBaseOpWidget::ShowErrorMsg("实收金额应小于应收金额");
        return false;
    }

    if(0 != m_nActualMoney%5) {
        CBaseOpWidget::ShowErrorMsg("实收金额应为5的倍数,请重新输入");
        return false;
    }
    return true;
}

int CUTypeReduceFm::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if(mtcKeyEvent->isNumKey()) {      // 数字键
        if(!m_pLineEdit)
            return 0;
        mtcKeyEvent->setKeyType(KC_Number);
        m_pLineEdit->SetLineEditText(mtcKeyEvent);
        return 1;
    }

    if(!mtcKeyEvent->isFuncKey())
        return 0;

    int nKey = mtcKeyEvent->func();
    switch(nKey){
    case KeyDel:
        m_pLineEdit->DeleteLineEditText();
        break;
    case KeyConfirm:{
        if(InputMoney_UFree==m_nType){
            m_nActualMoney = m_pLineEdit->text().toInt();
            if(!VerifyActualMoney()){
                m_pLineEdit->clear();
            }else
                this->OnOk();
        }
        break;
    }
    case KeyCash:{
        if(InputMoney_Owe==m_nType){
            m_nActualMoney = m_pLineEdit->text().toInt();
            if(!VerifyActualMoney()){
                m_pLineEdit->clear();
            }else
                this->OnOk();
        }
        break;
    }
    case KeyEsc:{
        this->OnCancel();;
        break;
    }
    default:
        return 0;
    }
    mtcKeyEvent->setKeyType(KC_Func);
    return 1;
}
