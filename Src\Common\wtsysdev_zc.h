#ifndef WTSYSDEV_ZC_H
#define WTSYSDEV_ZC_H

#include "../../devices/wtsysdev.h"

/**
 * @brief 中创称重设备扩展类
 * 继承自CWtSysDev，添加扩展接口，兼容不同版本的API
 */
class CWtSysDev_ZC : public CWtSysDev
{
    Q_OBJECT
public:
    explicit CWtSysDev_ZC();
    virtual ~CWtSysDev_ZC();

    /**
     * @brief 加载动态库
     * @return bool 加载结果
     */
    virtual bool LoadDriver();

    /**
     * @brief 手动称重扩展接口
     * @return bool 执行结果
     */
    bool DevFinish_HD();
    
    /**
     * @brief 兼容性获取轴组数据接口
     * @param vehicleID 车辆序号
     * @param axisID 轴组号
     * @param axisType 轴型
     * @param weight 轴重
     * @param axisSpeed 轴速
     * @return int 获取结果
     */
    int GetAxisDataCompatible(int vehicleID, int axisID, int &axisType, long &weight, int &axisSpeed);
    
    /**
     * @brief 兼容性检测多车是否在秤台接口
     * @param count 最低报警车辆数
     * @return bool 是否需要报警
     */
    bool IsVehicleAloneCompatible(int count);
    
    /**
     * @brief 兼容性超长车手动收尾接口
     * @return int 执行结果
     */
    int ManualFinishingCompatible();

protected:
    // DevFinish接口函数指针定义
    typedef int(WINAPI *Func_WtSys_DevFinish)();
    
    // ManualFinishing接口函数指针定义
    typedef int(WINAPI *Func_WtSys_ManualFinishing)();
    
    // IsVehicleAlone接口函数指针定义(大写版本)
    typedef bool(WINAPI *Func_WtSys_IsVehicleAlone_Upper)();
    
    // DevFinish函数指针
    Func_WtSys_DevFinish m_wtSysDevFinish;
    
    // ManualFinishing函数指针
    Func_WtSys_ManualFinishing m_wtSysManualFinishing;
    
    // IsVehicleAlone大写版本函数指针
    Func_WtSys_IsVehicleAlone_Upper m_wtSysIsVehicleAloneUpper;
    
    // 标记是否存在DevFinish接口
    bool m_hasDevFinish;
    
    // 标记是否存在ManualFinishing接口
    bool m_hasManualFinishing;
    
    // 标记IsVehicleAlone接口大小写版本
    bool m_isUpperCase;
};

#endif // WTSYSDEV_ZC_H 