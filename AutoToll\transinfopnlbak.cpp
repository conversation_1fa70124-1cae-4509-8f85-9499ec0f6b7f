#include "transinfopnl.h"
#include "ui_transinfopnl.h"
#include "ilogmsg.h"
#include "etclanectrl.h"
#include "dlgmain.h"
#include "laneconfigdlg.h"

#ifdef Q_OS_WIN32
#define FONT_NAME "微软雅黑"
#else
#define FONT_NAME "微软雅黑"
#endif

CTransInfoPnl::CTransInfoPnl(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::CTransInfoPnl)
{
    ui->setupUi(this);
    m_bMotor = false;
}

CTransInfoPnl::~CTransInfoPnl()
{
    delete ui;
}

void CTransInfoPnl::InitUI(int nWidth, int nHeight)
{
    wnd_w=nWidth;
    wnd_h=nHeight;//220;//252;
    this->resize(wnd_w, wnd_h);
    setFixedSize(wnd_w, wnd_h);

    ui->groupBox->resize(wnd_w-5,60);
    ui->groupBox->move(3,2);
    ui->groupBox->setTitle("系统信息");

    QPalette paWhite;
    paWhite.setColor(QPalette::WindowText, QColor(255, 255, 255));
    QPalette paBlack;
    paBlack.setColor(QPalette::WindowText, QColor(0, 0, 0));
    QFont ftLaneInfo;
    ftLaneInfo.setFamily(FONT_NAME);
    ftLaneInfo.setPixelSize(15);


    QLabel *label;
    label = new QLabel(QString("内存使用率"), this);
    label->setFont(ftLaneInfo);
    label->setPalette(paBlack);
    label->resize(100,20);
    label->move(60,15);

    m_pMemUsge = new QLabel(this);
    m_pMemUsge->setFont(ftLaneInfo);
    m_pMemUsge->setPalette(paBlack);
    m_pMemUsge->resize(100, 20);
    m_pMemUsge->move(90, 40);
    m_pMemUsge->setText(QString("100%"));


    label = new QLabel(QString("硬盘剩余大小"),this);
    label->setFont(ftLaneInfo);
    label->setPalette(paBlack);
    label->resize(110,20);
    label->move(180,15);

    m_pDiskSpace = new QLabel(this);
    m_pDiskSpace->setFont(ftLaneInfo);
    m_pDiskSpace->setPalette(paBlack);
    m_pDiskSpace->resize(100, 20);
    m_pDiskSpace->move(190, 40);
    m_pDiskSpace->setText(QString("50G"));

    label = new QLabel(QString("网络状态"), this);
    label->setFont(ftLaneInfo);
    label->setPalette(paBlack);
    label->resize(150,20);
    label->move(320,15);

    m_pNetState = new QLabel(this);
    m_pNetState->setFont(ftLaneInfo);
    m_pNetState->setPalette(paBlack);
    m_pNetState->resize(100, 20);
    m_pNetState->move(320, 40);
    m_pNetState->setText(QString("正常"));

    InitButton();

}

void CTransInfoPnl::InitButton()
{
    QPushButton* pVehLeadOut = new QPushButton(QString("紧急车"), this);
    connect(pVehLeadOut, SIGNAL(clicked()), this, SLOT(barrierup_clicked()));
    pVehLeadOut->resize(100,30);
    quint32 nLineTop=80;
    //m_pVehLeadOut->move(30, 5);
    int nLeft = 15;
    int nSubWidth = 135;
    pVehLeadOut->move(nLeft, nLineTop);
    QPushButton * barrierdown = new QPushButton(QString("模拟过车"), this);
    connect(barrierdown, SIGNAL(clicked()), this, SLOT(barrierdown_clicked()));
    barrierdown->resize(100,30);
    nLeft+=nSubWidth;
    barrierdown->move(nLeft, nLineTop);
    QPushButton * exitprogram = new QPushButton(QString("退出程序"), this);
    connect(exitprogram, SIGNAL(clicked()), this, SLOT(exitprogram_clicked()));
    exitprogram->resize(100,30);
    nLeft+= nSubWidth;
    exitprogram->move(nLeft, nLineTop);

    QPushButton *btnSetCfg = new QPushButton(QString("配置参数"),this);
    //connect(btnSetCfg,SIGNAL(clicked()),this,SLOT(on_setcfg_clicked()));
    nLeft +=nSubWidth;
    btnSetCfg->resize(100,30);
    btnSetCfg->move(nLeft,nLineTop);
    btnSetCfg->hide();


    m_pMotorBtn = new QPushButton(QString("车队开始"), this);
    connect(m_pMotorBtn, SIGNAL(clicked()), this, SLOT(motor_clicked()));
    m_pMotorBtn->resize(100,30);

    nLeft = 15;
    nLineTop +=50;
    m_pMotorBtn->move(nLeft, nLineTop);
    QPushButton * deletecar = new QPushButton(QString("删除异常车"), this);
    connect(deletecar, SIGNAL(clicked()), this, SLOT(deletecar_clicked()));
    deletecar->resize(100,30);
    nLeft += nSubWidth;
    deletecar->move(nLeft, nLineTop);
    QPushButton * showparainfo = new QPushButton(QString("参数版本"), this);
    connect(showparainfo, SIGNAL(clicked()), this, SLOT(downloadpara_clicked()));
    showparainfo->resize(100,30);
    nLeft +=nSubWidth;
    showparainfo->move(nLeft, nLineTop);
}

void CTransInfoPnl::SetMotor(bool bMotor)
{
    m_bMotor = bMotor;
    if(bMotor){
        m_pMotorBtn->setText("车队结束");
    }else
        m_pMotorBtn->setText("车队开始");
    return ;
}

void CTransInfoPnl::SetSysInfo(int nSize, const QString &sDisSpace)
{
    m_pMemUsge->setText(QString("%1%").arg(nSize));
    m_pDiskSpace->setText(sDisSpace);
    return ;
}

void CTransInfoPnl::SetNetState(int nStatus)
{
    QString str;
    if(0==nStatus){
        str = QString("网络正常");
    }else{
        str = QString("网络异常");
    }
    if(m_pNetState)
        m_pNetState->setText(str);
}


void CTransInfoPnl::barrierup_clicked()
{
    DebugLog("操作紧急车业务");
    QString str = QTime::currentTime().toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_Other, LogKey::Other_Oper, str, QString(), QString("操作紧急车业务"));
    Ptr_ETCCtrl->SetVehLeadOut();
}

void CTransInfoPnl::barrierdown_clicked()
{
    DebugLog("手动模拟过车");
    QString str = QTime::currentTime().toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_Other, LogKey::Other_Oper, str, QString(), QString("手动模拟过车"));
    Ptr_ETCCtrl->SimulateDownBar();
    GetMainDlg()->RefreshPassVehQueue();
    GetMainDlg()->RefreshTradeVehQueue();
}

void CTransInfoPnl::exitprogram_clicked()
{
    CLocalDataMgr *pLocalDataMgr = Ptr_ETCCtrl->GetDataMgr();
    CDeviceFactory::StopAlarm(DevIndex_ALL);
    int cnt = pLocalDataMgr->getUnSendCnt();
    if(cnt>0)
    {
        QPushButton *okbtn = new QPushButton(QString("确定"));
        QPushButton *cancelbtn = new QPushButton(QString("取消"));
        QMessageBox mymsgbox;

        mymsgbox.setIcon(QMessageBox::Warning);
        mymsgbox.setWindowTitle(QString("警告"));
        mymsgbox.setText(QString("还有%1条未上传流水,是否继续退出程序").arg(cnt));
        mymsgbox.addButton(okbtn, QMessageBox::AcceptRole);
        mymsgbox.addButton(cancelbtn, QMessageBox::RejectRole);
        mymsgbox.show();
        mymsgbox.exec();
        if (mymsgbox.clickedButton()==cancelbtn)//点击了取消按钮
            return;
    }
    if(Ptr_ETCCtrl->bAllowllContinuePass(false)){
        CAbstractState::GetCurState()->ShowErrorMessage("车道内有未离开车辆,无法下班");
        return;
    }

    Ptr_ETCCtrl->UnLogin();
    SleeperThread::msleep(150);
    qApp->closeAllWindows();
}

void CTransInfoPnl::motor_clicked()
{
    if(!m_bMotor){
        if(Ptr_ETCCtrl->GetVehCount_FrontQue()>0){
            GetMainDlg()->ShowLog(QString("车道内有未放行车辆"));
            return ;
        }
        Ptr_ETCCtrl->ChangeToMotorcade();
    }
    else{
        Ptr_ETCCtrl->ChangeToVehInputState();
    }
}

void CTransInfoPnl::deletecar_clicked()
{
    DebugLog("手动删除异常车辆");
    QString str = QTime::currentTime().toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_Other, LogKey::Other_Oper, str, QString(), QString("手动删除异常车辆"));
    CTransInfo transInfo;
    if(Ptr_ETCCtrl->bAbnormalVehOfTheFirstVehInQueue(transInfo)){
        CTransInfo *pTransInfo =Ptr_ETCCtrl->RemoveTransInfoFromQue(true);
        if(pTransInfo){
            delete pTransInfo;
            if(Ptr_ETCCtrl->bAllowllContinuePass(false)){
                Ptr_ETCCtrl->SetAllowPass(true);
            }
        }
    }
}

void CTransInfoPnl::downloadpara_clicked()
{
    GetMainDlg()->CheckAndDownLoadParam();
}

void CTransInfoPnl::setcfg_clicked()
{
    CLaneConfigDlg cfgDlg;
    cfgDlg.InitUI();
    cfgDlg.LoadCfg();
    return ;
}
