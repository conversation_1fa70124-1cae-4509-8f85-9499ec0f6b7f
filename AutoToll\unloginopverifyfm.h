#ifndef UNLOGINOPVERIFY_H
#define UNLOGINOPVERIFY_H

#include <QWidget>
#include "baseopwidget.h"
#include "paramfilemgr.h"

namespace Ui {
class CUnloginOpVerify;
}

class CUnloginOpVerifyFm : public CBaseOpWidget
{
    Q_OBJECT
public:
    explicit CUnloginOpVerifyFm(QWidget *parent = 0);
    ~CUnloginOpVerifyFm();

protected:
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    virtual bool OnOpenCardEvent(int nReadId,int nCardType);
    void closeEvent(QCloseEvent *event);
private:
    Ui::CUnloginOpVerify *ui;
private:
    COperInfo m_LoginOperInfo;
    int m_nPswErrorNum;
private:
    void ToLoginDlg();
    bool OperVerifyPassword(QString strPassword, QString &sError);
};

#endif // UNLOGINOPVERIFY_H
