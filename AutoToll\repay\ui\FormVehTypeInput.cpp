#include "FormVehTypeInput.h"
#include <QPainter>
#include <QFont>
#include "globalui.h"
#include "dlgmain.h"
#include "mtckey/MtcKeyDef.h"

// 车型映射表：字母键到车型的映射
const FormVehTypeInput::VehTypeMapping FormVehTypeInput::s_vehTypeMappings[] = {
    {'A', VC_Car1, "客一"},
    {'B', VC_Car2, "客二"},
    {'C', VC_Car3, "客三"},
    {'D', VC_Car4, "客四"},
    {'E', VC_Truck1, "货一"},
    {'F', VC_Truck2, "货二"},
    {'G', VC_Truck3, "货三"},
    {'H', VC_Truck4, "货四"},
    {'J', VC_Truck5, "货五"},
    {'K', VC_Truck6, "货六"},
    {'L', VC_YJ1, "专一"},
    {'M', VC_YJ2, "专二"},
    {'N', VC_YJ3, "专三"},
    {'P', VC_YJ4, "专四"},
    {'Q', VC_YJ5, "专五"},
    {'R', VC_YJ6, "专六"},
    {0, VC_None, nullptr}  // 结束标记
};

FormVehTypeInput::FormVehTypeInput(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblVehTypeTitle(nullptr)
    , m_pLblVehTypeName(nullptr)
    , m_selectedVehType(VC_None)
{
    // 创建控件
    m_pLblVehTypeTitle = new QLabel(this);
    m_pLblVehTypeName = new QLabel(this);
    
    // 设置对象名称
    setObjectName(QString("FormVehTypeInput"));

}

FormVehTypeInput::~FormVehTypeInput()
{
    delete m_pLblVehTypeTitle;
    delete m_pLblVehTypeName;
}

bool FormVehTypeInput::InputVehType(CVehClass defaultVehType)
{
    // 初始化界面
    InitUI();
    
    // 设置默认车型
    m_selectedVehType = defaultVehType;
    UpdateVehTypeDisplay();
    
    // 显示模态对话框
    return (CBaseOpWidget::Rlt_OK == doModalShow());
}

QString FormVehTypeInput::GetSelectedVehTypeName() const
{
    return GetVehClassName(m_selectedVehType);
}

void FormVehTypeInput::InitUI()
{
    CBaseOpWidget::InitUI();
    
    // 设置字体
    QFont fontTitle = QFont(g_GlobalUI.m_FontName, 24);  // "车型"标题字体大小24
    QFont fontLabel = QFont(g_GlobalUI.m_FontName, g_GlobalUI.plate_EditFontSize + 4);
    
    m_pLblVehTypeTitle->setFont(fontTitle);
    m_pLblVehTypeName->setFont(fontLabel);
    
    // 设置"车型"标题标签属性
    m_pLblVehTypeTitle->setText("车型");
    m_pLblVehTypeTitle->setAlignment(Qt::AlignCenter);
    m_pLblVehTypeTitle->setStyleSheet("QLabel { color: black; font-weight: bold; }");
    
    // 设置车型名称标签属性 - 初始为白色背景
    m_pLblVehTypeName->setAlignment(Qt::AlignCenter);
    m_pLblVehTypeName->setStyleSheet("QLabel { background-color: white; border: 2px solid black; font-weight: bold; }");
    
    // 计算控件位置
    int centerX = rect().width() / 2;
    int startY = g_GlobalUI.optw_TitleHeight + 30;
    
    // "车型"标题标签位置
    int titleWidth = 80;
    int titleHeight = 40;
    int titleX = centerX - 100;  // 位于车型名称显示框左边
    QRect titleRect(titleX, startY, titleWidth, titleHeight);
    m_pLblVehTypeTitle->setGeometry(titleRect);
    
    // 车型名称显示框（显示对应的车型名称）- 加宽输入框
    int labelWidth = 220;  // 从160加宽到220
    int labelHeight = 60;
    int labelX = centerX - 10;  // 调整X位置以适应加宽后的尺寸
    QRect labelRect(labelX, startY - 10, labelWidth, labelHeight);
    m_pLblVehTypeName->setGeometry(labelRect);

    // 过滤子控件的键盘事件
    filterChildrenKeyEvent();
}

int FormVehTypeInput::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) {
        return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
    }
    
    int keyCode = mtcKeyEvent->key();
    int keyFunc = mtcKeyEvent->func();
    
    // 处理确认键
    if (keyFunc == KeyConfirm) {
        if (m_selectedVehType != VC_None) {
            OnOk();
            return 1;
        }
        return 1;
    }
    
    // 处理取消键
    if (keyFunc == KeyEsc) {
        OnCancel();
        return 1;
    }
    
    // 处理字母键
    if (keyCode >= 'A' && keyCode <= 'Z') {
        OnLetterInput(static_cast<char>(keyCode));
        return 1;
    }
    
    // 处理小写字母键
    if (keyCode >= 'a' && keyCode <= 'z') {
        OnLetterInput(static_cast<char>(keyCode - 'a' + 'A'));
        return 1;
    }
    
    return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
}

void FormVehTypeInput::OnLetterInput(char letter)
{
    // 查找对应的车型
    CVehClass vehType = GetVehTypeByLetter(letter);
    
    if (vehType != VC_None) {
        m_selectedVehType = vehType;
        m_inputBuffer = QString(letter);
        UpdateVehTypeDisplay();
        
        InfoLog(QString("车型输入：%1 -> %2(%3)")
                .arg(letter)
                .arg(GetVehClassName(vehType))
                .arg(static_cast<int>(vehType)));
                
        InfoLog(QString("当前选择车型：%1(%2)").arg(GetVehClassName(m_selectedVehType)).arg(static_cast<int>(m_selectedVehType)));
    }
}

void FormVehTypeInput::UpdateVehTypeDisplay()
{
    if (m_selectedVehType != VC_None) {
        // 显示车型名称 - 绿色背景以匹配设计图
        QString vehTypeName = GetVehClassName(m_selectedVehType);
        m_pLblVehTypeName->setText(vehTypeName);
        m_pLblVehTypeName->setStyleSheet("QLabel { background-color: #90EE90; border: 2px solid black; font-weight: bold; color: black; }");
    } else {
        m_pLblVehTypeName->clear();
        m_pLblVehTypeName->setStyleSheet("QLabel { background-color: white; border: 2px solid black; font-weight: bold; }");
    }
}

CVehClass FormVehTypeInput::GetVehTypeByLetter(char letter)
{
    for (int i = 0; s_vehTypeMappings[i].letter != 0; i++) {
        if (s_vehTypeMappings[i].letter == letter) {
            return s_vehTypeMappings[i].vehType;
        }
    }
    return VC_None;
}

char FormVehTypeInput::GetLetterByVehType(CVehClass vehType)
{
    for (int i = 0; s_vehTypeMappings[i].letter != 0; i++) {
        if (s_vehTypeMappings[i].vehType == vehType) {
            return s_vehTypeMappings[i].letter;
        }
    }
    return 0;
}

void FormVehTypeInput::paintEvent(QPaintEvent *)
{
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    
    // 绘制背景色
    painter.setBrush(g_GlobalUI.m_ColorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);
    
    // 绘制标题
    QFont fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    QRect rctTitle(0, 0, rect().width(), g_GlobalUI.optw_TitleHeight);
    painter.setFont(fontTitle);
    painter.drawText(rctTitle, Qt::AlignCenter, "补费车型");
    
    // 绘制车型选项说明
    QFont fontTip = QFont(g_GlobalUI.m_FontName, g_GlobalUI.plate_TipFontSize);
    painter.setFont(fontTip);
    
    int startY = m_pLblVehTypeName->geometry().bottom() + 20;
    int lineHeight = QFontMetrics(fontTip).height() + 2;
    
    // 第一行：客车车型 - 居中显示
    QString line1 = "A:客一, B:客二, C:客三, D:客四";
    QRect rect1(10, startY, rect().width() - 20, lineHeight);
    painter.drawText(rect1, Qt::AlignCenter, line1);
    
    // 第二行：货车车型 - 居中显示
    QString line2 = "E:货一, F:货二, G:货三, H:货四, J:货五, K:货六";
    QRect rect2(10, startY + lineHeight, rect().width() - 20, lineHeight);
    painter.drawText(rect2, Qt::AlignCenter, line2);
    
    // 第三行：专项车型 - 居中显示
    QString line3 = "L:专一, M:专二, N:专三, P:专四, Q:专五, R:专六";
    QRect rect3(10, startY + lineHeight * 2, rect().width() - 20, lineHeight);
    painter.drawText(rect3, Qt::AlignCenter, line3);
    
    // 绘制操作提示 - 居中显示
    int helpY = startY + lineHeight * 4;
    QRect rctHelp(10, helpY, rect().width() - 20, rect().bottom() - helpY - 10);
    QString sHelp = "按字母键选择车型\n【确认】键确认，【ESC】键取消";
    painter.drawText(rctHelp, Qt::AlignCenter, sHelp);
    
    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}
