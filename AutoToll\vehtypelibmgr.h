#ifndef CVEHTYPELIBMGR_H
#define CVEHTYPELIBMGR_H

#include <QObject>
#include "pausablethread.h"
#include "lanetype.h"
#include "ilogmsg.h"

struct CVehInfo_VehLib
{
    QString sVehicleId;
    int nColor;
    CVehClass vehClass;  //车型
    int seatingNum;
    int axleNum;
    quint32 ratedWeight;
    int vehType;  //车种名称
    QString imgs;
    int score;  //可信度 0~100 百分比
    QString message;
    QString vehicleTypeDbInfo;  //车型库信息，格式：车型|车种|轴数|轴型，每个字段用"|"分隔，没有的项用" "标识

public:
    void Clear()
    {
        sVehicleId.clear();
        nColor = -1;
        vehClass = VC_None;
        seatingNum = 0;
        axleNum = 0;
        ratedWeight = 0;
        vehType = UVT_Normal;
        imgs.clear();
        score = 0;
        message.clear();
        vehicleTypeDbInfo.clear();
    }
    CVehInfo_VehLib() { Clear(); }
};

struct CVehTypeQryCondition
{
    int nColor;
    QString sVehicle;
    int nState;  //状态0-等待查询 1-正在查询
};

struct User_Info
{
    QString id;
    QString username;
    QString authorities;
};

struct CTokenInfo
{
    QString username;
    QString password;
    QString access_token;
    QString token_type;
    QString refresh_token;
    int expires_in;  //失效时长
    User_Info userInfo;
};

class CVehTypeLibMgr;
class CTokenThread : public CPausableThread
{
    Q_OBJECT
public:
    explicit CTokenThread(QObject *parent = 0);
    virtual bool RunOnce();
    void SetUrl(const QString &sTokenUrl, const QString &sRefreshUrl, const QString &sSaveUrl)
    {
        m_sTokenUrl = sTokenUrl;
        m_sRefreshTokenUrl = sRefreshUrl;
        m_sSaveVehUrl = sSaveUrl;
        return;
    }

    static QString urlEncode(const QMap<QString, QString> &params);

    static QString AesPassWord(QString Password, QString &key);

    void AddVehInfo(const CVehInfo_VehLib &vehInfo);

protected:
    bool HttpPost_urlencoded(const QString &sUrl, const QString &sContent, QString &sRespons);
    bool HttpPost_ByToken(const QString &sUrl, const QString &sContent, const QString stoken,
                          QString &sRespons);

    bool QueryToken(CTokenInfo &tokenInfo);
    bool RefreshToken(const QString &sRefreshToken, CTokenInfo &tokenInfo);
    bool SaveVehType(const QString &sToken, const CVehInfo_VehLib &vehInfo);

protected:
    CTokenInfo m_tokenInfo;
    QString m_sRefreshTokenUrl;
    QString m_sTokenUrl;
    QString m_sSaveVehUrl;

    QMutex m_listMt;
    QList<CVehInfo_VehLib> m_vehList;
    QString m_sKey;
};

class CVehTypeLibMgr : public CPausableThread
{
    Q_OBJECT
public:
    explicit CVehTypeLibMgr(QObject *parent = 0);

    static CVehTypeLibMgr *GetVehTypeLibMgr()
    {
        static CVehTypeLibMgr libMgr;
        return &libMgr;
    }

    virtual bool RunOnce();

    void InitVehTypeLib(const QString &sUrl, const QString &sQryToken, const QString &sRefreshToken,
                        const QString &sSaveToken);
    bool GetVehResult(const QString &sVlp, int nColor, CVehInfo_VehLib &vehInfo);
    void AddQuryTask(const QString &sVlp, int nColor);
    
    void AsyncGetVehResult(const QString &sVlp, int nColor);

    // 新增：直接查询缓存中的车型信息
    bool QueryVehFromCache(const QString &sVlp, int nColor, CVehInfo_VehLib &vehInfo);

    static QString ConvertVlpColor(int nColor);
    void ReleaseVehTypeLibMgr();
    void DebugLog_VehMsg(const QString &sMsg);
    void DebugLogVehInfo(const CVehInfo &vehInfo);

signals:
    void VehResultReady(const QString &sVlp, int nColor, bool bSuccess);

protected:
    bool QueryVehType(int nVlpColor, const QString &sVehPlate, CVehInfo_VehLib &vehInfo);

    bool ParseQueryResult(const QString &sResponse, CVehInfo_VehLib &vehInfo);

private:
    QString m_sUrl;
    QMutex m_qryMt;
    QList<CVehTypeQryCondition> m_Tasks;
    QMutex m_vehMt;
    QList<CVehInfo_VehLib> m_vehList;
    QWaitCondition m_waitCondition;
    CLog4Qt *m_pLog;
    CTokenThread *m_pTokenThread;
};

extern bool HttpPost(const QString &sUrl, const QString &sContent, QString &sRespons);

#endif  // CVEHTYPELIBMGR_H
