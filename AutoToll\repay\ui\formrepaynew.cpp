#include "formrepaynew.h"
#include "formauthorization.h"
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"
#include "JlCompress.h"
#include <QApplication>
#include <QKeyEvent>
#include <QMessageBox>
#include <QRegExp>
#include <QTextCodec>
#include <QFile>
#include "forminterceptselect.h"
#include "formdebtdetail.h"
#include "../../common/paramfilemgr.h"
#include "../../common/paramfile.h"

FormRepayNew::FormRepayNew(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_repayType(RepayType_None)
    , m_currentStage(RepayStage_None)
    , m_vehPlateColor(1)
    , m_vehType(1)
    , m_amount(0)

    , m_bProcessing(false)
    , m_bInputEnabled(true)
    , m_bPaymentEnabled(false)
    , m_paymentInProgress(false)
    , m_pRepayManager(0)
    , m_pQueryTimer(0)
    , m_pErrorDisplayTimer(0)
    , m_bConnectionsInited(false)
{
    // 获取RepayManager实例
    m_pRepayManager = RepayManager::GetInstance();
    
    // 初始化定时器
    m_pQueryTimer = new QTimer(this);
    m_pErrorDisplayTimer = 0; // 错误显示定时器按需创建
    
    // 初始化界面配置
    InitUIConfig();
    
    setObjectName(QString("FormRepayNew"));
}

FormRepayNew::~FormRepayNew()
{
    InfoLog("销毁新版补费界面");

    // 无状态设计：不需要清理RepayManager状态
    // 每次补费都是独立的操作，界面关闭不影响后续补费

    // 车牌输入界面已改为局部变量，无需清理

    // 停止所有定时器
    if (m_pQueryTimer) {
        m_pQueryTimer->stop();
    }

    // 断开RepayManager信号连接
    if (m_pRepayManager) {
        disconnect(m_pRepayManager, 0, this, 0);
    }

    DebugLog("新版补费界面资源清理完成");
}

void FormRepayNew::InitUIConfig()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize, QFont::Bold);
    m_fontText = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    m_fontEdit = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_EditFontSize);
    m_fontButton = QFont(g_GlobalUI.m_FontName, 14);
    
    // 设置颜色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    m_colorTitle = QColor(0, 0, 0);
    m_colorText = QColor(50, 50, 50);
    m_colorHighlight = QColor(0, 120, 215);
    m_colorError = QColor(220, 50, 50);
    m_colorSuccess = QColor(50, 150, 50);
    m_colorWarning = QColor(200, 120, 0);
    
    // 设置高度配置
    m_nTitleHeight = g_GlobalUI.optw_TitleHeight;
    m_nStageHeight = STAGE_PROMPT_HEIGHT;
    m_nInputAreaHeight = 200;
    m_nPaymentAreaHeight = 120;
    m_nButtonAreaHeight = 60;
    m_nInfoAreaHeight = 100;
}

void FormRepayNew::InitUI(int iFlag)
{
    // 轻量化：不再初始化自身界面控件，仅建立业务信号连接即可
    CBaseOpWidget::InitUI(iFlag);
    InitConnections();
    DebugLog("补费界面初始化（无UI）完成");
}

void FormRepayNew::InitControls()
{
    // 创建控件（简化为车牌输入界面）
//    m_pLblTitle = new QLabel(this);
//    m_pLblStage = new QLabel(this);
    
//    // 车牌输入控件已改为使用FormInputPlate类
    
//    // 金额输入区域（仅当趟补费使用）
//    m_pLblAmountLabel = new QLabel("补费金额:", this);
//    m_pEditAmount = new QLineEdit(this);
//    m_pLblAmountUnit = new QLabel("元", this);
    
//    // 功能按钮已移除，直接进入车牌输入
    
//    // 信息显示区域
//    m_pLblDebtInfo = new QLabel(this);
//    m_pLblProgressInfo = new QLabel(this);
//    m_pLblHelpInfo = new QLabel(this);
    
    // 设置控件属性
    //SetupControlProperties();
}

void FormRepayNew::SetupControlProperties()
{
    // 设置标题
//    m_pLblTitle->setFont(m_fontTitle);
//    m_pLblTitle->setAlignment(Qt::AlignCenter);
//    m_pLblTitle->setStyleSheet("color: rgb(0, 0, 0);");
    
//    // 设置阶段提示
//    m_pLblStage->setFont(m_fontText);
//    m_pLblStage->setAlignment(Qt::AlignCenter);
//    m_pLblStage->setStyleSheet("color: rgb(0, 120, 215); background-color: rgb(240, 240, 240);");
    
//    // 设置标签字体
//    if (m_pLblAmountLabel) m_pLblAmountLabel->setFont(m_fontText);
//    if (m_pLblAmountUnit) m_pLblAmountUnit->setFont(m_fontText);
    
//    // 车牌输入已改为使用FormInputPlate类
    
//    // 设置金额输入框（仅当趟补费用）
//    if (m_pEditAmount) {
//        m_pEditAmount->setFont(m_fontEdit);
//        m_pEditAmount->setPlaceholderText("0.00");
//        m_pEditAmount->setValidator(new QRegExpValidator(QRegExp("^[0-9]+(\\.[0-9]{1,2})?$"), this));
//    }
    
//    // 设置按钮样式
//    SetupButtonStyles();
    
//    // 设置信息显示区域
//    m_pLblDebtInfo->setFont(m_fontText);
//    m_pLblDebtInfo->setWordWrap(true);
//    m_pLblDebtInfo->setAlignment(Qt::AlignTop | Qt::AlignLeft);
    
//    m_pLblProgressInfo->setFont(m_fontText);
//    m_pLblProgressInfo->setAlignment(Qt::AlignCenter);
    
//    m_pLblHelpInfo->setFont(m_fontText);
//    m_pLblHelpInfo->setWordWrap(true);
//    m_pLblHelpInfo->setAlignment(Qt::AlignTop | Qt::AlignLeft);
    
//    // 默认隐藏一些控件
//    m_pLblDebtInfo->setVisible(false);
}

void FormRepayNew::SetupButtonStyles()
{
    QString buttonStyle = QString(
        "QPushButton {"
        "    background-color: rgb(240, 240, 240);"
        "    border: 1px solid rgb(180, 180, 180);"
        "    border-radius: 4px;"
        "    padding: 5px;"
        "}"
        "QPushButton:hover {"
        "    background-color: rgb(220, 220, 220);"
        "}"
        "QPushButton:pressed {"
        "    background-color: rgb(200, 200, 200);"
        "}"
        "QPushButton:disabled {"
        "    background-color: rgb(250, 250, 250);"
        "    color: rgb(150, 150, 150);"
        "}"
    );
    
    QString primaryButtonStyle = QString(
        "QPushButton {"
        "    background-color: rgb(0, 120, 215);"
        "    border: 1px solid rgb(0, 100, 180);"
        "    border-radius: 4px;"
        "    padding: 5px;"
        "    color: white;"
        "}"
        "QPushButton:hover {"
        "    background-color: rgb(0, 100, 180);"
        "}"
        "QPushButton:pressed {"
        "    background-color: rgb(0, 80, 150);"
        "}"
    );
    
    // 功能按钮已移除
}

void FormRepayNew::InitLayout()
{
    CBaseOpWidget::InitUI();

    // 金额输入框位置（当趟补费用）
//    if (m_pEditAmount && m_repayType == RepayType_Current) {
//        int amountY = 120; // 固定位置
//        int amountLeft = (rect().width() - 200) / 2;
//        m_pLblAmountLabel->setGeometry(amountLeft - 80, amountY, 80, 30);
//        m_pEditAmount->setGeometry(amountLeft, amountY, 120, 30);
//        m_pLblAmountUnit->setGeometry(amountLeft + 125, amountY, 30, 30);
//    }
    
    // 按钮布局已移除，直接进入车牌输入
}







void FormRepayNew::InitConnections()
{
    // RepayManager信号连接
    if (m_pRepayManager && !m_bConnectionsInited) {
        connect(m_pRepayManager, SIGNAL(RepayStarted(RepayType)), this, SLOT(OnRepayStarted(RepayType)));
        connect(m_pRepayManager, SIGNAL(RepayStageChanged(RepayStage)), this, SLOT(OnRepayStageChanged(RepayStage)));
        connect(m_pRepayManager, SIGNAL(RepayCompleted(bool,QString)), this, SLOT(OnRepayCompleted(bool,QString)));
        connect(m_pRepayManager, SIGNAL(PaymentProcessing()), this, SLOT(OnPaymentProcessing()));
        connect(m_pRepayManager, SIGNAL(PaymentCompleted(bool,QString)), this, SLOT(OnPaymentCompleted(bool,QString)));
        connect(m_pRepayManager, SIGNAL(DebtQueryCompleted(bool,RepayDebtQueryResult)), this, SLOT(OnDebtQueryCompleted(bool,RepayDebtQueryResult)));
        connect(m_pRepayManager, SIGNAL(ErrorOccurred(RepayErrorCode,QString)), this, SLOT(OnRepayError(RepayErrorCode,QString)));
        m_bConnectionsInited = true;
    }
    
    // 界面控件信号连接（简化后只保留存在的控件）
//    if (m_pEditAmount) {
//        connect(m_pEditAmount, SIGNAL(textChanged(QString)), this, SLOT(OnAmountChanged()));
//    }
    
    // 功能按钮已移除
    
    // 定时器连接
    connect(m_pQueryTimer, SIGNAL(timeout()), this, SLOT(OnQueryTimeout()));
}

void FormRepayNew::SetRepayType(RepayType type)
{
    m_repayType = type;
    UpdateUI();

    QString typeName = GetRepayTypeName(type);
    InfoLog(QString("设置补费类型：%1 (值:%2)").arg(typeName).arg((int)type));
}

void FormRepayNew::SetVehicleInfo(const QString &vehPlate, int vehPlateColor, int vehType)
{
    m_vehPlate = vehPlate;
    m_vehPlateColor = vehPlateColor;
    m_vehType = vehType;

    // 更新界面显示
    SetVehPlateColor(vehPlateColor);

    InfoLog(QString("设置车辆信息 - 车牌:%1, 颜色:%2, 车型:%3")
            .arg(vehPlate).arg(vehPlateColor).arg(vehType));
}

void FormRepayNew::SetVehPlateColor(int color)
{
    m_vehPlateColor = color;

    DebugLog(QString("选择车牌颜色：%1").arg(GetVehPlateColorName(color)));
}

bool FormRepayNew::StartRepayProcess()
{
    if (!m_pRepayManager) {
        ShowErrorMessage("补费管理器未初始化");
        return false;
    }
    
    // 防止外部重复调用导致信号未连接
    if (!m_bConnectionsInited) {
        InitConnections();
    }

    if (!m_pRepayManager->Initialize()) {
        ShowErrorMessage("补费管理器初始化失败");
        return false;
    }
    
    // 准备车辆信息
    CVehInfo vehInfo;
    vehInfo.Clear();
    // 设置车牌号（转换为本地字符编码）
    QByteArray plateBytes = m_vehPlate.toLocal8Bit();
    qstrncpy(vehInfo.szVehPlate, plateBytes.data(), sizeof(vehInfo.szVehPlate));
    vehInfo.nVehPlateColor = (quint8)m_vehPlateColor;
    vehInfo.VehClass = (CVehClass)m_vehType;
    
    // 如果是省内名单补费，先显示拦截方式页面
    if (m_repayType == RepayType_Province) {
        // 先隐藏当前界面，符合项目规范
        this->hide();
        
        FormInterceptSelect interceptForm(GetMainDlg());
        interceptForm.InitUI();
        
        // 使用简化的接口，默认选择出口拦截
//        if (!interceptForm.SelectInterceptType(Intercept_Entry)) {
//            InfoLog("用户取消拦截方式选择");
//            return false;
//        }
        interceptForm.SelectInterceptType(Intercept_Entry);
        // 获取选择结果并写入业务管理器，避免后续重复弹窗
        InterceptType selectedType = interceptForm.GetSelectedType();
        m_pRepayManager->SetPreselectedInterceptType(selectedType);
        
        InfoLog(QString("拦截方式选择完成 - 类型:%1").arg((int)selectedType));

        // 重新显示当前流程控制界面，继续后续流程
//        this->show();
//        this->raise();
//        this->activateWindow();
    }

    // 开始补费流程
    bool result = m_pRepayManager->StartRepay(m_repayType, vehInfo);
    
    if (result) {
        m_bProcessing = true;
        m_startTime = QDateTime::currentDateTime();
        InfoLog(QString("开始补费流程：%1").arg(GetRepayTypeName(m_repayType)));
    } else {
        ShowErrorMessage("启动补费流程失败");
    }
    
    return result;
}

void FormRepayNew::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);

    // FormRepayNew现在作为流程控制器，不需要复杂的界面绘制
    // 只绘制简单的背景，实际的输入界面由专门的页面处理

    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);

    // 绘制背景色
    painter.setBrush(g_GlobalUI.m_ColorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);

    // 绘制标题
    QFont fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    QRect rctTitle(0, 0, rect().width(), 60);
    painter.setFont(fontTitle);

    QString title = (m_repayType == RepayType_Current) ? "当趟补费流程控制" : "省内名单补费流程控制";
    painter.drawText(rctTitle, Qt::AlignCenter, title);

    // 绘制流程状态信息
    QFont fontInfo = QFont(g_GlobalUI.m_FontName, 14);
    painter.setFont(fontInfo);
    QRect rctInfo(20, 80, rect().width() - 40, rect().height() - 100);

    QString info = "补费流程控制器\n";
    info += QString("车牌: %1\n").arg(m_vehPlate.isEmpty() ? "未输入" : m_vehPlate);
    if (m_repayType == RepayType_Current) {
        info += QString("金额: %1元\n").arg(m_amount / 100.0, 0, 'f', 2);
    }
    info += "各步骤将自动显示专门的输入界面";

    painter.drawText(rctInfo, Qt::AlignLeft, info);

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

void FormRepayNew::DrawBackground(QPainter &painter)
{
    QRect rectClient = this->rect();
    
    // 绘制背景色
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);
}

void FormRepayNew::DrawTitle(QPainter &painter)
{
//    if (!m_pLblTitle) return;
    
//    QRect titleRect(0, 0, rect().width(), m_nTitleHeight);
//    painter.setFont(m_fontTitle);
//    painter.setPen(m_colorTitle);
    
//    QString title = (m_repayType == RepayType_Current) ? "当趟补费" : "省内名单补费";
//    painter.drawText(titleRect, Qt::AlignCenter, title);
}

void FormRepayNew::DrawStagePrompt(QPainter &painter)
{
//    if (!m_pLblStage) return;
    
//    QRect stageRect(0, m_nTitleHeight, rect().width(), m_nStageHeight);
//    painter.setFont(m_fontText);
//    painter.setPen(m_colorHighlight);
    
//    QString stageText = GetRepayStageName(m_currentStage);
//    painter.drawText(stageRect, Qt::AlignCenter, stageText);
}

void FormRepayNew::DrawHelpMessage(QPainter &painter)
{
//    if (!m_pLblHelpInfo) return;
    
//    QRect helpRect = m_pLblHelpInfo->geometry();
//    painter.setFont(m_fontText);
//    painter.setPen(m_colorText);
    
//    QString helpText = GetHelpMessageForCurrentStage();
//    painter.drawText(helpRect, Qt::AlignTop | Qt::AlignLeft | Qt::TextWordWrap, helpText);
}

void FormRepayNew::DrawProgressIndicator(QPainter &painter)
{
//    if (!m_bProcessing) return;
    
//    QRect progressRect(rect().width() - 60, 10, 50, 20);
//    painter.setFont(m_fontText);
//    painter.setPen(m_colorHighlight);
    
//    // 简单的进度指示（显示处理时间）
//    int elapsedSecs = m_startTime.secsTo(QDateTime::currentDateTime());
//    QString progressText = QString("处理中... %1s").arg(elapsedSecs);
//    painter.drawText(progressRect, Qt::AlignCenter, progressText);
}

QString FormRepayNew::GetHelpMessageForCurrentStage()
{
    switch (m_currentStage) {
        case RepayStage_Authorization:
            return "请进行班长授权验证";
        case RepayStage_VehicleInput:
            return "请输入车牌号并选择车牌颜色";
        case RepayStage_DebtQuery:
            return "正在查询车辆欠费信息...";
        case RepayStage_AmountConfirm:
            if (m_repayType == RepayType_Current) {
                return "请输入补费金额，然后选择支付方式";
            } else {
                return "确认欠费金额，然后选择支付方式";
            }
        case RepayStage_Payment:
            return "正在处理支付，请稍候...";
        case RepayStage_Complete:
            return "补费完成";
        default:
            return "请按照提示进行操作";
    }
}

int FormRepayNew::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) return 0;

    // 处理键盘输入
    int keyCode = mtcKeyEvent->func();

    switch (keyCode) {
        case KeyEsc:
            OnCancel();
            break;
//        case KeyCar1:
//            if (m_repayType == RepayType_Province) {
//                StartDebtQuery();
//            }
//            break;
        default:
            return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
    }
    return 1;
}

// 实现车牌输入对话框
bool FormRepayNew::ShowPlateInputDialog()
{
    InfoLog("准备显示车牌输入对话框");

    // 先隐藏当前FormRepayNew页面
    InfoLog("隐藏FormRepayNew界面");
    this->hide();

    // 按照CLaneState_VehInput::InputPlate()的方式创建FormInputPlate
    // 使用GetMainDlg()作为父窗口，而不是this
    FormInputPlate dlgInput(GetMainDlg());

    InfoLog(QString("创建FormInputPlate对象，初始车牌:%1, 颜色:%2").arg(m_vehPlate).arg(m_vehPlateColor));

    // 调用EditPlate方法显示车牌输入对话框
    bool result = dlgInput.EditPlate(m_vehPlate, static_cast<VP_COLOR>(m_vehPlateColor));

    InfoLog(QString("车牌输入对话框返回结果：%1").arg(result ? "成功" : "取消/失败"));

    if (result) {
        // 获取输入结果
        QString plate;
        VP_COLOR color;
        dlgInput.GetInputResult(plate, color);

        // 保存结果
        m_vehPlate = plate;
        m_vehPlateColor = static_cast<int>(color);

        InfoLog(QString("车牌输入完成 - 车牌:%1, 颜色:%2").arg(plate).arg(static_cast<int>(color)));
    } else {
        InfoLog("车牌输入被取消或失败");
    }

    // 不在这里重新显示界面，让调用方处理
    InfoLog("车牌输入对话框处理完成，返回结果");
    return result;
}

bool FormRepayNew::ShowAmountInputDialog()
{
    InfoLog("准备显示金额输入对话框");

    // 先隐藏当前界面
    this->hide();

    // 创建金额输入对话框
    FormInputAmount dlgAmount(GetMainDlg());

    InfoLog(QString("显示金额输入对话框 - 车牌:%1, 当前金额:%2分").arg(m_vehPlate).arg(m_amount));

    // 显示金额输入对话框
    bool result = dlgAmount.InputAmount(m_amount, m_vehPlate, m_vehPlateColor, m_vehType);

    InfoLog(QString("金额输入对话框返回结果：%1").arg(result ? "成功" : "取消/失败"));

    if (result) {
        // 获取输入的金额
        m_amount = dlgAmount.GetInputAmount();
        InfoLog(QString("金额输入完成 - 金额:%1分").arg(m_amount));
    } else {
        InfoLog("金额输入被取消或失败");
    }

    return result;
}

bool FormRepayNew::ShowVehTypeInputDialog()
{
    InfoLog("准备显示车型输入对话框");

    // 先隐藏当前界面
    this->hide();

    // 创建车型输入对话框
    FormVehTypeInput dlgVehType(GetMainDlg());

    InfoLog(QString("显示车型输入对话框 - 当前车型:%1").arg(m_vehType));

    // 显示车型输入对话框
    bool result = dlgVehType.InputVehType(static_cast<CVehClass>(m_vehType));

    InfoLog(QString("车型输入对话框返回结果：%1").arg(result ? "成功" : "取消/失败"));

    if (result) {
        // 获取选择的车型
        CVehClass selectedVehType = dlgVehType.GetSelectedVehType();
        QString vehTypeName = dlgVehType.GetSelectedVehTypeName();
        
        // 保存结果
        m_vehType = static_cast<int>(selectedVehType);
        
        InfoLog(QString("车型输入完成 - 车型:%1(%2)").arg(vehTypeName).arg(static_cast<int>(selectedVehType)));
    } else {
        InfoLog("车型输入被取消或失败");
    }

    return result;
}

bool FormRepayNew::ShowRepaySuccessDialog(CTransPayType payType)
{
    InfoLog("准备显示补费成功确认界面");

    // 先隐藏当前界面
    this->hide();

    // 创建补费成功界面
    FormRepaySuccess dlgSuccess(GetMainDlg());

    // 构建补费方式名称
    QString repayTypeName;
    if (m_repayType == RepayType_Current) {
        repayTypeName = "当趟补费";
    } else if (m_repayType == RepayType_Province) {
        repayTypeName = "省内名单补费";
    } else {
        repayTypeName = "补费";
    }

    // 构建支付方式名称
    QString payTypeName;
    switch (payType) {
        case TransPT_Cash:
            payTypeName = "现金";
            break;
        case TransPT_AliPay:
            payTypeName = "支付宝";
            break;
        case TransPT_WeChat:
            payTypeName = "微信";
            break;
        case TransPT_Union:
            payTypeName = "银联";
            break;
        case TransPT_ETCCard:
            payTypeName = "ETC卡";
            break;
        default:
            payTypeName = "其他支付";
            break;
    }

    InfoLog(QString("显示补费成功界面 - 方式:%1, 车牌:%2, 支付:%3, 金额:%4分")
            .arg(repayTypeName).arg(m_vehPlate).arg(payTypeName).arg(m_amount));

    // 显示成功界面（自动关闭30秒）
    bool result = dlgSuccess.ShowRepaySuccess(repayTypeName, m_vehPlate, m_vehPlateColor, 
                                             payTypeName, m_amount, 30);

    InfoLog(QString("补费成功界面返回结果：%1").arg(result ? "确认" : "取消"));

    return result;
}

bool FormRepayNew::ShowDebtDetailDialog(const RepayDebtQueryResult &result)
{
    InfoLog("准备显示债务详情界面");

    // 检查债务数据有效性
    if (result.debtItems.isEmpty()) {
        ShowWarningMessage("未查询到有效的欠费记录");
        return false;
    }

    // 先隐藏当前界面，符合项目规范
    this->hide();

    // 使用原有的省内追缴名单明细界面
    FormDebtDetail dlgDebtDetail(GetMainDlg());
    dlgDebtDetail.InitUI();

    InfoLog(QString("显示债务详情界面 - 车牌:%1, 记录数:%2, 总金额:%3分")
            .arg(result.vehiclePlate.isEmpty() ? m_vehPlate : result.vehiclePlate)
            .arg(result.debtItems.size())
            .arg(result.totalAmount));

    // 显示债务详情界面
    bool dialogResult = dlgDebtDetail.ShowDebtDetail(result);

    InfoLog(QString("债务详情界面返回结果：%1").arg(dialogResult ? "成功" : "取消"));

    if (dialogResult) {
        // 用户确认债务详情，使用总金额进行补费
        int totalAmount = dlgDebtDetail.GetTotalRepayAmount();
        
        if (totalAmount <= 0) {
            InfoLog("债务总金额无效");
            ShowWarningMessage("债务总金额无效");
            return false;
        }

        // 更新当前补费信息为总金额
        m_amount = totalAmount;
        
        // 保持原始债务结果不变（包含所有债务项目）
        m_debtResult = result;
        
        InfoLog(QString("用户确认债务补费 - 总金额:%1分, 债务项目数:%2")
                .arg(totalAmount)
                .arg(result.debtItems.size()));
        
        // 更新界面显示的债务信息
        UpdateDebtInfo(m_debtResult);
        
        return true;
    } else {
        InfoLog("用户取消债务详情选择");
        return false;
    }
}

void FormRepayNew::OnModalShowed()
{
    CBaseOpWidget::OnModalShowed();

    InfoLog("补费界面显示完成，准备启动车辆输入");

    // 立即进入车牌输入界面
    StartVehicleInput();
}

void FormRepayNew::OnCancel()
{
    InfoLog("补费界面取消");

    // 无状态设计：不需要清理RepayManager状态
    // 每次补费都是独立的操作，界面关闭不影响后续补费

    // 调用基类的OnCancel方法关闭界面
    CBaseOpWidget::OnCancel();
}

// 业务逻辑实现将在下一部分继续...

void FormRepayNew::UpdateUI()
{
    
    // 根据补费类型更新界面
//    bool isCurrentRepay = (m_repayType == RepayType_Current);
    
//    // 金额输入框的可见性（仅当趟补费显示）
//    if (m_pEditAmount) m_pEditAmount->setVisible(isCurrentRepay);
//    if (m_pLblAmountLabel) m_pLblAmountLabel->setVisible(isCurrentRepay);
//    if (m_pLblAmountUnit) m_pLblAmountUnit->setVisible(isCurrentRepay);
    
//    // 按钮已移除
    
//    // 更新标题
//    QString title = isCurrentRepay ? "当趟补费" : "省内名单补费";
//    if (m_pLblTitle) m_pLblTitle->setText(title);
    
    update();
}

void FormRepayNew::ResetUI()
{
    // 重置所有输入
    ClearInputs();
    
    // 重置状态
    m_currentStage = RepayStage_None;
    m_bProcessing = false;
    m_bInputEnabled = true;
    m_bPaymentEnabled = false;
    
    // 重置界面状态
    EnableInput(true);
    EnablePaymentButtons(false);
    
    // 隐藏信息区域
//    if (m_pLblDebtInfo) m_pLblDebtInfo->setVisible(false);
//    if (m_pLblProgressInfo) m_pLblProgressInfo->setText("");
//    if (m_pLblHelpInfo) m_pLblHelpInfo->setText("");
    
    // 停止定时器
    if (m_pQueryTimer) m_pQueryTimer->stop();
    
    DebugLog("界面状态已重置");
}

void FormRepayNew::ClearInputs()
{
//    if (m_pEditAmount) m_pEditAmount->clear();

    m_vehPlate.clear();
    m_vehPlateColor = 1;
    m_amount = 0;

    // 重置车牌颜色选择
    SetVehPlateColor(1);
}


void FormRepayNew::ShowErrorMessage(const QString &message)
{
    // 使用项目统一提示方式
    if (GetMainDlg()) {
        GetMainDlg()->ShowPromptMsg(message, true, true);
    }
    ErrorLog(QString("补费界面错误：%1").arg(message));
}

void FormRepayNew::ShowSuccessMessage(const QString &message)
{
    if (GetMainDlg()) {
        GetMainDlg()->ShowPromptMsg(message, false, true);
    }
    InfoLog(QString("补费界面成功：%1").arg(message));
}

void FormRepayNew::ShowWarningMessage(const QString &message)
{
    if (GetMainDlg()) {
        GetMainDlg()->ShowPromptMsg(message, true, true);
    }
    WarnLog(QString("补费界面警告：%1").arg(message));
}

QString FormRepayNew::GetEnhancedPaymentErrorMessage(const QString &originalError)
{
    // 根据原始错误信息提供更详细的用户提示
    QString enhanced = originalError;
    
    if (originalError.contains("金额") && originalError.contains("超")) {
        enhanced = QString("补费金额超出限制！\n当前车型最大补费金额为：%1元\n请重新输入合理金额")
                  .arg(GetMaxFeeForCurrentVehicle() / 100.0, 0, 'f', 2);
    }
    else if (originalError.contains("网络") || originalError.contains("连接")) {
        enhanced = "网络连接失败！\n请检查网络连接后重试\n或联系管理员处理";
    }
    else if (originalError.contains("ETC") || originalError.contains("卡")) {
        enhanced = "ETC卡支付失败！\n请检查：\n1. 卡片是否正确插入\n2. 卡片余额是否充足\n3. 卡片状态是否正常";
    }
    else if (originalError.contains("支付宝") || originalError.contains("AliPay") || originalError.contains("微信") || originalError.contains("WeChat")) {
        enhanced = "移动支付失败！\n请检查：\n1. 网络连接是否正常\n2. 支付账户是否有足够余额\n3. 重新扫码支付";
    }
    else if (originalError.contains("现金")) {
        enhanced = "现金支付处理失败！\n请联系管理员检查现金箱状态";
    }
    else if (originalError.contains("专项作业车") || (m_vehType >= 21 && m_vehType <= 26)) {
        enhanced = QString("专项作业车支付失败！\n车型：%1\n请检查车型配置或联系管理员")
                  .arg(GetVehClassName(m_vehType));
    }
    else if (originalError.contains("授权") || originalError.contains("权限")) {
        enhanced = "操作授权失败！\n请联系班长进行授权验证";
    }
    else if (originalError.isEmpty() || originalError == "支付失败") {
        enhanced = "支付处理失败！\n请检查支付条件后重试\n如问题持续存在，请联系管理员";
    }
    
    return enhanced;
}

int FormRepayNew::GetMaxFeeForCurrentVehicle()
{
    // 获取当前车型的最大补费金额
    RepayConfig *config = RepayConfig::GetInstance();
    if (config) {
        return config->GetMaxFee(m_vehType);
    }
    return 15000; // 默认150元
}

// RepayManager信号响应槽函数实现
void FormRepayNew::OnRepayStarted(RepayType type)
{
    InfoLog(QString("补费流程开始 - 类型:%1").arg(static_cast<int>(type)));
    
    m_repayType = type;
    m_currentStage = RepayStage_VehicleInput;
    
    // 更新界面状态
    UpdateStageDisplay();
    ShowStageMessage("补费流程已开始");
}

void FormRepayNew::OnRepayStageChanged(RepayStage stage)
{
    InfoLog(QString("补费阶段变更：%1").arg(static_cast<int>(stage)));
    
    m_currentStage = stage;
    
    // 更新界面显示
    UpdateStageDisplay();
    
    // 根据阶段更新界面状态
    switch (stage) {
        case RepayStage_Authorization:
            // 班长确认界面（授权）
            ShowStageMessage("正在进行授权验证...");
            SetUIEnabled(false);
            PerformAuthorization();
            break;
        case RepayStage_VehicleInput:
            // 当趟补费：车型/车牌/金额；省内名单：直接进入拦截方式选择
            if (m_repayType == RepayType_Province) {
                // 进入拦截方式选择
                this->hide();
                FormInterceptSelect interceptForm(GetMainDlg());
                interceptForm.InitUI();
                interceptForm.SelectInterceptType(Intercept_Entry);
                InterceptType selectedType = interceptForm.GetSelectedType();
                if (m_pRepayManager) {
                    m_pRepayManager->SetPreselectedInterceptType(selectedType);
                }
                this->show();
                // 拦截方式选择后，直接进入费用信息（债务明细）
                StartDebtQuery();
            } else {
                ShowStageMessage("请输入车辆信息");
                SetUIEnabled(true);
                EnableInput(true);
            }
            break;
        case RepayStage_DebtQuery:
            ShowStageMessage("正在查询欠费信息...");
            SetUIEnabled(false);
            break;
        case RepayStage_AmountConfirm:
            ShowStageMessage("请确认补费金额");
            SetUIEnabled(true);
            EnableInput(false);
            EnablePaymentButtons(true);
            break;
        case RepayStage_Payment:
            ShowStageMessage("正在处理支付...");
            SetUIEnabled(false);
            break;
        case RepayStage_Complete:
            ShowStageMessage("补费完成");
            EnablePaymentButtons(false);
            break;
        default:
            break;
    }
}

void FormRepayNew::OnRepayCompleted(bool success, const QString &message)
{
    InfoLog(QString("补费流程完成 - 成功:%1, 消息:%2").arg(success).arg(message));

    if (success) {
        // 获取当前支付方式
        CTransPayType payType = TransPT_Cash; // 默认现金
        if (m_pRepayManager) {
            payType = m_pRepayManager->GetCurrentPayType();
        }
        
        InfoLog(QString("补费成功，准备显示成功确认界面 - 支付方式:%1").arg(payType));
        
        // 显示补费成功确认界面
        ShowRepaySuccessDialog(payType);
        
        // 成功界面关闭后，自动关闭主界面
        OnOk();
    } else {
        ShowErrorMessage(message.isEmpty() ? "补费失败" : message);
        
        // 重置界面状态，允许重新操作
        SetUIEnabled(true);
        EnableInput(true);
        m_currentStage = RepayStage_VehicleInput;
        UpdateStageDisplay();
    }
}

void FormRepayNew::OnPaymentProcessing()
{
    InfoLog("开始处理支付");
    
    ShowStageMessage("正在处理支付，请稍候...");
    SetUIEnabled(false);
    
    // 显示支付进度
    m_paymentInProgress = true;
    UpdateProgressDisplay();
}

void FormRepayNew::OnPaymentCompleted(bool success, const QString &message)
{
    InfoLog(QString("支付处理完成 - 成功:%1, 消息:%2").arg(success).arg(message));
    
    m_paymentInProgress = false;
    
    if (success) {
        ShowSuccessMessage(message.isEmpty() ? "支付成功" : message);
        
        // 支付成功后，设置为完成阶段
        m_currentStage = RepayStage_Complete;
        UpdateStageDisplay();
        
        // 支付成功后，继续后续流程
        if (m_repayType == RepayType_Province) {
            ShowStageMessage("正在通知省中心...");
        } else {
            // 当趟补费直接完成，延迟关闭界面
            ShowStageMessage("补费完成");
            QTimer::singleShot(2000, this, SLOT(OnDelayedOk()));
        }
    } else {
        // 增强的支付错误处理
        QString errorMsg = message.isEmpty() ? "支付失败" : message;
        QString enhancedErrorMsg = GetEnhancedPaymentErrorMessage(errorMsg);
        
        InfoLog(QString("支付失败详细信息 - 原始错误:%1, 增强提示:%2").arg(message).arg(enhancedErrorMsg));
        ShowErrorMessage(enhancedErrorMsg);
        
        // 支付失败，返回金额确认阶段
        m_currentStage = RepayStage_AmountConfirm;
        SetUIEnabled(true);
        EnablePaymentButtons(true);
        UpdateStageDisplay();
        
        // 显示重试提示
        ShowWarningMessage("请检查支付条件后重新选择支付方式");
    }
}

void FormRepayNew::OnDebtQueryCompleted(bool success, const RepayDebtQueryResult &result)
{
    InfoLog(QString("欠费查询完成 - 成功:%1").arg(success));
    
    // 停止查询超时定时器
    if (m_pQueryTimer) {
        m_pQueryTimer->stop();
    }
    
    if (success) {
        m_debtResult = result;
        
        // 根据查询结果更新界面
        if (!result.listno.isEmpty() && result.totalAmount > 0) {  // 查询成功
            InfoLog(QString("查询成功，共%1条欠费记录，总金额:%2元")
                             .arg(result.debtItems.size())
                             .arg(QString::number(result.totalAmount / 100.0, 'f', 2)));
            
            // 显示债务详情界面让用户选择具体补费项目
            if (ShowDebtDetailDialog(result)) {
                // 省内名单补费：债务详情确认后直接进入支付方式选择流程
                InfoLog("债务详情确认完成，直接进入支付方式选择");
                StartPaymentProcess();
            } else {
                // 用户取消了债务详情选择，返回车辆输入阶段
                InfoLog("用户取消债务详情选择");
                m_currentStage = RepayStage_VehicleInput;
                SetUIEnabled(true);
                EnableInput(true);
                UpdateStageDisplay();
            }
            
        } else {  // 不在名单中或无欠费
            ShowWarningMessage("该车辆不在省内追收名单中或无欠费记录");
            
            // 返回车辆输入阶段
            m_currentStage = RepayStage_VehicleInput;
            SetUIEnabled(true);
            EnableInput(true);
            UpdateStageDisplay();
        }
    } else {
        ShowErrorMessage("网络查询失败，请检查网络连接");
        
        // 返回车辆输入阶段
        m_currentStage = RepayStage_VehicleInput;
        SetUIEnabled(true);
        EnableInput(true);
        UpdateStageDisplay();
    }
}

void FormRepayNew::OnRepayError(RepayErrorCode errorCode, const QString &message)
{
    ErrorLog(QString("补费流程错误 - 代码:%1, 消息:%2").arg(static_cast<int>(errorCode)).arg(message));

    // 停止所有定时器
    if (m_pQueryTimer) {
        m_pQueryTimer->stop();
    }
    
    QString errorText;
    switch (errorCode) {
        case RepayError_NetworkFailed:
            errorText = "网络连接失败";
            break;
        case RepayError_AuthFailed:
            errorText = "授权验证失败";
            break;
        case RepayError_AmountExceeded:
            errorText = "补费金额超过限制";
            break;
        case RepayError_VehicleNotFound:
            errorText = "车辆信息未找到";
            break;
        case RepayError_PaymentFailed:
            errorText = "支付处理失败";
            break;
        case RepayError_SystemError:
            errorText = "系统内部错误";
            break;
        default:
            errorText = "未知错误";
            break;
    }
    
    if (!message.isEmpty()) {
        errorText += QString("：%1").arg(message);
    }
    
    ShowErrorMessage(errorText);
    
    // 重置界面状态
    SetUIEnabled(true);
    
    // 根据错误类型决定返回的阶段
    if (errorCode == RepayError_AuthFailed) {
        // 授权失败，需要重新开始
        m_currentStage = RepayStage_None;
        EnableInput(true);
    } else if (errorCode == RepayError_VehicleNotFound || errorCode == RepayError_NetworkFailed) {
        // 车辆或网络问题，返回输入阶段
        m_currentStage = RepayStage_VehicleInput;
        EnableInput(true);
    } else if (errorCode == RepayError_AmountExceeded) {
        // 金额问题，返回金额确认阶段
        m_currentStage = RepayStage_AmountConfirm;
        EnablePaymentButtons(true);
    } else {
        // 其他错误，返回输入阶段
        m_currentStage = RepayStage_VehicleInput;
        EnableInput(true);
    }
    
    UpdateStageDisplay();
}

QString FormRepayNew::GetVehPlateColorName(int color)
{
    switch (color) {
        case 1: return "蓝牌";
        case 2: return "黄牌";
        case 3: return "白牌";
        case 4: return "黑牌";
        case 5: return "绿牌";
        default: return "未知";
    }
}












// 界面控制实现
void FormRepayNew::EnableInput(bool enabled)
{
    m_bInputEnabled = enabled;

    //if (m_pEditAmount) m_pEditAmount->setEnabled(enabled);
}

void FormRepayNew::EnablePaymentButtons(bool enabled)
{
    m_bPaymentEnabled = enabled;
    // 由于使用单独的支付选择页面，这里不再需要设置支付按钮
}

void FormRepayNew::SetUIEnabled(bool enabled)
{
    EnableInput(enabled);
    EnablePaymentButtons(enabled && m_currentStage == RepayStage_AmountConfirm);
}

void FormRepayNew::SetCurrentStage(RepayStage stage)
{
    m_currentStage = stage;
    UpdateUI();
    UpdateStageDisplay();
}

void FormRepayNew::ShowStageMessage(RepayStage stage)
{
    // 统一通过提示窗口显示阶段提示
    QString stageText = GetRepayStageName(stage);
    if (GetMainDlg()) {
        GetMainDlg()->ShowPromptMsg(stageText, false, true);
    }
}

void FormRepayNew::ShowStageMessage(const QString &message)
{
    if (GetMainDlg()) {
        GetMainDlg()->ShowPromptMsg(message, false, true);
    }
}

void FormRepayNew::UpdateProgressDisplay()
{
    //if (!m_bProcessing || !m_pLblProgressInfo) return;
    
    int elapsed = m_startTime.secsTo(QDateTime::currentDateTime());
    QString progressText = QString("处理进行中... %1秒").arg(elapsed);
    
    // 添加动画效果（简单的点号动画）
    int dots = (elapsed % 4);
    for (int i = 0; i < dots; i++) {
        progressText += ".";
    }
    
    //m_pLblProgressInfo->setText(progressText);
    //m_pLblProgressInfo->setStyleSheet("color: rgb(0, 120, 215);");
}

// 输入验证实现

bool FormRepayNew::ValidateAmount()
{
    if (m_amount <= 0) {
        return false;
    }
    
    // 简单的金额范围检查
    if (m_amount > 50000) { // 最大500元
        return false;
    }
    
    return true;
}

bool FormRepayNew::ValidateInputs()
{
    if (!ValidateVehPlate()) {
        ShowErrorMessage("请输入正确的车牌号");
        return false;
    }

    if (m_repayType == RepayType_Current && !ValidateAmount()) {
        ShowErrorMessage("请输入有效的补费金额");
        //if (m_pEditAmount) m_pEditAmount->setFocus();
        return false;
    }

    if (m_repayType == RepayType_Province && (m_debtResult.listno.isEmpty() || m_debtResult.totalAmount <= 0)) {
        ShowErrorMessage("请先查询欠费信息");
        return false;
    }
    
    return true;
}

// 界面更新实现
void FormRepayNew::UpdateDebtInfo(const RepayDebtQueryResult &result)
{
    QString debtInfo = QString("查询到欠费记录：\n车牌：%1\n欠费金额：%2元\n工单数：%3个")
                      .arg(result.vehiclePlate.isEmpty() ? m_vehPlate : result.vehiclePlate)
                      .arg(QString::number(result.totalAmount / 100.0, 'f', 2))
                      .arg(result.debtItems.size());
    
//    if (m_pLblDebtInfo) {
//        m_pLblDebtInfo->setText(debtInfo);
//        m_pLblDebtInfo->setVisible(true);
//    }
    
    // 设置金额
    m_amount = result.totalAmount;
}

// 注意：车牌颜色和支付方式现在通过键盘输入和单独页面处理，不再需要按钮槽函数

// 延时操作slot实现
void FormRepayNew::OnDelayedOk()
{
    OnOk();
}

// 业务处理方法实现
void FormRepayNew::PerformAuthorization()
{
    InfoLog("开始进行班长授权验证");
    
    // 创建授权验证界面
    FormAuthorization authForm;
    
    QString operatorId, operatorName;
    bool authResult = authForm.DoAuthorization(operatorId, operatorName);
    
    if (authResult) {
        InfoLog(QString("授权验证成功 - 操作员:%1(%2)").arg(operatorName).arg(operatorId));
        
        // 授权成功，进入下一阶段
        if (m_pRepayManager) {
            // 通知RepayManager授权成功
            // 根据补费类型决定下一阶段
            if (m_repayType == RepayType_Current) {
                // 当趟补费：进入车辆输入阶段
                m_pRepayManager->SetRepayStage(RepayStage_VehicleInput);
            } else if (m_repayType == RepayType_Province) {
                // 省内名单补费：进入车辆输入阶段
                m_pRepayManager->SetRepayStage(RepayStage_VehicleInput);
            }
        }
        
        ShowSuccessMessage("授权验证成功");
    } else {
        ErrorLog("授权验证失败或用户取消");
        
        // 授权失败，结束补费流程
        if (m_pRepayManager) {
            m_pRepayManager->CancelRepay();
        }
        
        ShowErrorMessage("授权验证失败，补费流程已取消");
        
        // 延迟关闭界面
        QTimer::singleShot(2000, this, SLOT(OnCancel()));
    }
}

void FormRepayNew::StartVehicleInput()
{
    InfoLog(QString("开始车辆输入流程，当前补费类型：%1").arg((int)m_repayType));

    // 根据补费类型决定下一步
    if (m_repayType == RepayType_Current) {
        InfoLog("当趟补费模式");

        InfoLog("步骤1：车型输入");
        if (!ShowVehTypeInputDialog()) {
            InfoLog("车型输入取消，结束流程");
            OnCancel();
            return;
        }
        InfoLog("车型输入成功，继续金额输入");

        InfoLog("步骤2：车牌输入");
        if (!ShowPlateInputDialog()) {
            InfoLog("车牌输入取消，结束流程");
            OnCancel();
            return;
        }
        InfoLog("车牌输入成功，继续后续流程");

        InfoLog("步骤3：金额输入");
        if (!ShowAmountInputDialog()) {
            InfoLog("金额输入取消，结束流程");
            OnCancel();
            return;
        }
        InfoLog("金额输入成功，进入支付流程");

        InfoLog("步骤4：支付处理");
        ProcessCurrentRepay();

    } else {
        InfoLog("省内名单补费模式");

        // 步骤2：查询欠费
        InfoLog("步骤2：查询欠费");
        StartDebtQuery();
    }
}

void FormRepayNew::ProcessCurrentRepay()
{
    // 当趟补费：直接进入支付流程
    StartPaymentProcess();
}

void FormRepayNew::ProcessProvinceRepay()
{
    // 省内名单补费：查询完成后进入支付流程
    StartPaymentProcess();
}

void FormRepayNew::HandleRepayCompletion(bool success, const QString &message)
{
    if (success) {
        ShowSuccessMessage(message.isEmpty() ? "补费完成" : message);

        // 补费成功，关闭界面
        QTimer::singleShot(2000, this, SLOT(OnOk()));
    } else {
        ShowErrorMessage(message.isEmpty() ? "补费失败" : message);

        // 重置界面状态，允许重新操作
        SetUIEnabled(true);
        EnableInput(true);
        m_currentStage = RepayStage_VehicleInput;
        UpdateStageDisplay();
    }
}

void FormRepayNew::HandleRepayError(RepayErrorCode errorCode, const QString &message)
{
    QString errorMsg = message;
    if (errorMsg.isEmpty()) {
        // 根据错误代码生成默认错误信息
        switch (errorCode) {
            case RepayError_AuthFailed:
                errorMsg = "授权验证失败";
                break;
            case RepayError_VehicleNotFound:
                errorMsg = "车辆信息未找到";
                break;
            case RepayError_NetworkFailed:
                errorMsg = "网络连接失败";
                break;
            case RepayError_AmountExceeded:
                errorMsg = "金额超出限制";
                break;
            default:
                errorMsg = "补费处理出错";
                break;
        }
    }

    ShowErrorMessage(errorMsg);

    // 根据错误类型决定返回的阶段
    if (errorCode == RepayError_AuthFailed) {
        // 授权失败，需要重新开始
        m_currentStage = RepayStage_None;
        EnableInput(true);
    } else if (errorCode == RepayError_VehicleNotFound || errorCode == RepayError_NetworkFailed) {
        // 车辆或网络问题，返回输入阶段
        m_currentStage = RepayStage_VehicleInput;
        EnableInput(true);
    } else if (errorCode == RepayError_AmountExceeded) {
        // 金额问题，返回金额确认阶段
        m_currentStage = RepayStage_AmountConfirm;
        EnablePaymentButtons(true);
    } else {
        // 其他错误，返回输入阶段
        m_currentStage = RepayStage_VehicleInput;
        EnableInput(true);
    }

    UpdateStageDisplay();
}

void FormRepayNew::StartPaymentProcess()
{
    // 显示支付方式选择界面
    CTransPayType selectedPayType;
    if (ShowPaymentSelection(selectedPayType)) {
        // 用户选择了支付方式，开始支付处理
        ProcessPayment(selectedPayType);
    } else {
        // 用户取消了支付方式选择，返回金额确认阶段
        InfoLog("用户取消支付方式选择");
        ShowWarningMessage("支付方式选择已取消");

        // 返回到金额确认阶段，允许用户重新选择支付方式
        m_currentStage = RepayStage_AmountConfirm;
        SetUIEnabled(true);
        EnablePaymentButtons(true);
        UpdateStageDisplay();
    }
}

void FormRepayNew::StartDebtQuery()
{
    if (!ValidateVehPlate()) {
        ShowErrorMessage("请输入正确的车牌号");
        return;
    }

    if (!m_pRepayManager) {
        ShowErrorMessage("补费管理器未初始化");
        return;
    }

    InfoLog(QString("开始查询欠费 - 车牌：%1").arg(m_vehPlate));

    // 省内名单补费：先走本地参数（省内追收名单）快速判定
    if (m_repayType == RepayType_Province) {
        CProvinceDebtTable *pProvinceDebtTable = (CProvinceDebtTable *)CParamFileMgr::GetParamFile(cfProvinceDebtList);
        if (!pProvinceDebtTable) {
            if (GetMainDlg()) {
                GetMainDlg()->ShowPromptMsg("省内追收名单参数未加载，无法进行省内名单补费", true, true);
            } else {
                ShowErrorMessage("省内追收名单参数未加载");
            }
            OnCancel();
            return;
        }

        // 若内存映射尚未就绪，尝试准备（加载）一次
        if (pProvinceDebtTable->GetRecordCount() <= 0) {
            pProvinceDebtTable->PrePareQuery();
        }

        CProvinceDebt debtInfo;
        if (!pProvinceDebtTable->IsInDebtList(m_vehPlate, m_vehPlateColor, debtInfo)) {
            if (GetMainDlg()) {
                GetMainDlg()->ShowPromptMsg("该车辆不在省内追收名单中，无法进行省内名单补费", true, true);
            } else {
                ShowWarningMessage("该车辆不在省内追收名单中");
            }
            InfoLog("车辆未命中省内追收名单，本次补费流程结束");
            OnCancel();
            return;
        }
    }

    SetUIEnabled(false);
    ShowWarningMessage("正在查询欠费信息...");

    // 启动查询（已改为同步查询，失败时业务层会弹提示）
    RepayDebtQueryResult result;
    bool queryOk = m_pRepayManager->QueryDebtDetail(m_vehPlate, m_vehPlateColor, result);

    if (!queryOk) {
        // 在界面层进行统一提示与退出
        if (GetMainDlg()) {
            GetMainDlg()->ShowPromptMsg("省内欠费查询失败，请检查网络或省中心接口", true, true);
        } else {
            ShowErrorMessage("省内欠费查询失败");
        }
        InfoLog("省内欠费查询失败，关闭补费界面");
        OnCancel();
        return;
    }
}

void FormRepayNew::ProcessPayment(CTransPayType payType)
{
    if (!ValidateInputs()) {
        return;
    }
    
    if (!m_pRepayManager) {
        ShowErrorMessage("补费管理器未初始化");
        return;
    }
    
    InfoLog(QString("开始处理支付 - 方式：%1, 金额：%2分").arg(payType).arg(m_amount));
    
    // 根据补费类型处理业务逻辑
    bool processResult = false;
    
    if (m_repayType == RepayType_Current) {
        // 当趟补费
        processResult = m_pRepayManager->ProcessCurrentRepay(m_vehPlate, m_vehPlateColor, m_vehType, m_amount);
    } else if (m_repayType == RepayType_Province) {
        // 省内名单补费
        processResult = m_pRepayManager->ProcessProvinceRepay(m_vehPlate, m_vehPlateColor, m_debtResult);
    }
    
    if (processResult) {
        // 开始支付处理
        m_pRepayManager->ProcessPayment(payType, m_amount, m_vehPlate, m_vehPlateColor);
    } else {
        ShowErrorMessage("补费处理失败");
    }
}

bool FormRepayNew::ValidateVehPlate()
{
    // 简单验证车牌号
    if (m_vehPlate.isEmpty()) {
        return false;
    }

    if (m_vehPlate.length() < 7) {
        return false;
    }

    return true;
}

// Slot方法实现
void FormRepayNew::OnVehPlateChanged()
{
    // 车牌输入现在通过FormInputPlate处理，此方法保留为空
}

void FormRepayNew::OnAmountChanged()
{
    //if (!m_pEditAmount) return;

//    QString amountText ="";// m_pEditAmount->text();
//    bool ok;
//    double amount = amountText.toDouble(&ok);

//    if (ok && amount >= 0) {
//        m_amount = (int)(amount * 100); // 转换为分
//        m_pEditAmount->setStyleSheet("");

//        // 如果金额有效且大于0，自动进入支付流程
//        if (amount > 0 && ValidateAmount()) {
//            // 延迟一点时间让用户看到输入结果
//            QTimer::singleShot(500, this, SLOT(StartPaymentProcess()));
//        }
//    } else {
//        m_pEditAmount->setStyleSheet("border: 1px solid red;");
//    }
}

void FormRepayNew::OnQueryTimeout()
{
    ShowErrorMessage("查询超时，请重试");
    SetUIEnabled(true);
    
    if (m_pQueryTimer) m_pQueryTimer->stop();
}

void FormRepayNew::OnErrorDisplayTimeout()
{
    // 错误信息显示超时，清空错误提示
//    if (m_pLblProgressInfo) {
//        m_pLblProgressInfo->setText("");
//        m_pLblProgressInfo->setStyleSheet("");
//    }
//    InfoLog("错误提示信息已自动清除");
}

void FormRepayNew::UpdateStageDisplay()
{
    // 更新当前阶段显示
    QString stageText;
    switch (m_currentStage) {
        case RepayStage_None:
            stageText = "等待开始";
            break;
        case RepayStage_Authorization:
            stageText = "等待授权验证";
            break;
        case RepayStage_VehicleInput:
            stageText = "等待输入车辆信息";
            break;
        case RepayStage_DebtQuery:
            stageText = "正在查询欠费信息...";
            break;
        case RepayStage_AmountConfirm:
            stageText = "请确认补费信息";
            break;
        case RepayStage_Payment:
            stageText = "正在处理补费...";
            break;
        case RepayStage_Complete:
            stageText = "补费完成";
            break;
        default:
            stageText = "未知状态";
            break;
    }
    
//    if (m_pLblStage) {
//        m_pLblStage->setText(stageText);
//    }
    
    // 更新界面状态
    update();
}

void FormRepayNew::OnPaymentButtonClicked()
{
    // 由于使用单独的支付选择页面，这个方法不再需要
    InfoLog("支付按钮点击 - 使用单独的支付选择页面");
}



// 移除了所有复刻的车牌输入方法，现在使用FormInputPlate类




bool FormRepayNew::ShowPaymentSelection(CTransPayType &selectedPayType)
{
    InfoLog("显示支付方式选择界面");
    
    // 创建支付方式选择界面
    FormPaymentSelect paymentDialog;

    paymentDialog.InitUI();
    
    // 设置可用的支付方式 - 重新定义支付方式含义
    QList<CTransPayType> availablePayTypes;
    availablePayTypes << TransPT_Cash;
    availablePayTypes << TransPT_Union;     // 银联卡代表移动支付（支付宝/微信）
    availablePayTypes << TransPT_ETCCard;   // 赣通卡包含ETC
    
    paymentDialog.SetAvailablePayTypes(availablePayTypes);
    
    // 显示选择界面
    return paymentDialog.ShowPaymentSelect(selectedPayType);
}
