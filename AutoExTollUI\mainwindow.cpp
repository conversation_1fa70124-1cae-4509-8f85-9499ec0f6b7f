#include "mainwindow.h"

#include <QDateTime>
#include <QPainter>
#include <QKeyEvent>

#include "globalui.h"
#include "parammgr.h"
#include "ui_mainwindow.h"

#define TIMEID_SECOND 1

MainWindow::MainWindow(QWidget *parent) : QDialog(parent)
{
    ui->setupUi(this);
    m_pServer = new UIServer();
    connect(m_pServer, SIGNAL(NotifyLaneInfo(QString, QString)), this,
            SLOT(OnLaneInfo(QString, QString)));
    connect(m_pServer, SIGNAL(NotifyHelpMsg(QString, QString, quint32, QString)), this,
            SLOT(OnHelpMsg(QString, QString, quint32, QString)));
    connect(m_pServer,
            SIGNAL(NotifyTollInfo(QString, QString, QString, QString, quint32, bool, quint8)), this,
            SLOT(OnTollInfo(QString, QString, QString, QString, quint32, bool, quint8)));
    connect(m_pServer,
            SIGNAL(NotifyTicketInfo(QString, QString, QString, QString, quint32, quint8, QString)),
            this, SLOT(OnTicketInfo(QString, QString, QString, QString, quint32, quint8, QString)));

    //等待车辆
    m_pFrmWaitVeh = new FormWaitVehicle(this);
    //提示插卡
    m_pFrmInsertCard = new FormInsertCard(this);
    //进行提示
    m_pFrmShowPrompt = new FormShowPrompt(this);
    //显示金额，提示扫码
    m_pFrmShowMoney = new FormShowTollMoney(this);
    //提示票据
    m_pFrmShowInvoice = new FormShowInvoice(this);
    //暂停服务
    m_pFrmOutOfService = new FormOutOfService(this);

    m_pBaseForms = new BaseForm *[FORM_END];
    m_pBaseForms[FORM_OutOfService] = m_pFrmOutOfService;
    m_pBaseForms[FORM_InsertCard] = m_pFrmInsertCard;
    m_pBaseForms[FORM_ShowInvoice] = m_pFrmShowInvoice;
    m_pBaseForms[FORM_WaitVehicle] = m_pFrmWaitVeh;
    m_pBaseForms[FORM_ShowMoney] = m_pFrmShowMoney;
    m_pBaseForms[FORM_ShowPrompt] = m_pFrmShowPrompt;
}

MainWindow::~MainWindow()
{
    if (m_pServer) {
        delete m_pServer;
    }

    //等待车辆
    delete m_pFrmWaitVeh;
    //提示插卡
    delete m_pFrmInsertCard;
    //进行提示
    delete m_pFrmShowPrompt;
    //显示金额，提示扫码
    delete m_pFrmShowMoney;
    //提示票据
    delete m_pFrmShowInvoice;
    //暂停服务
    delete m_pFrmOutOfService;
}

void MainWindow::OnLaneInfo(const QString stationName, const QString laneName)
{
    //    m_plistLog->addItem(QString("%1, %2").arg(stationName).arg(laneName));
}

void MainWindow::OnHelpMsg(const QString stationName, const QString laneName, quint32 nHelpType,
                           const QString helpMsg)
{
    m_laneName = stationName;
    m_stationName = laneName;
    switch (nHelpType) {
        case UIServer::HT_Waiting:
            ShowHelpForm(FORM_END);
            m_pFrmShowPrompt->ShowPrompt(helpMsg, true);
            break;
        case UIServer::HT_Paying:
            ShowHelpForm(FORM_END);
            m_pFrmShowPrompt->ShowPaying(helpMsg);
            break;
        case UIServer::HT_Checking:
            ShowHelpForm(FORM_END);
            m_pFrmShowPrompt->ShowChecking(helpMsg);
            break;
        case UIServer::HT_CardError:
            ShowHelpForm(FORM_END);
            m_pFrmShowPrompt->ShowChardError(helpMsg);
            break;
        case UIServer::HT_CheckError:
            ShowHelpForm(FORM_END);
            m_pFrmShowPrompt->ShowCheckError(helpMsg);
            break;
        case UIServer::HT_InsertCard:
            ShowHelpForm(FORM_InsertCard);
            break;
        case UIServer::HT_OutOfService:
            ShowHelpForm(FORM_OutOfService);
            break;
        case UIServer::HT_WaitVehicleArrive:
            ShowHelpForm(FORM_WaitVehicle);
            break;
        case UIServer::HT_Prompt:
            ShowHelpForm(FORM_END);
            m_pFrmShowPrompt->ShowPrompt(helpMsg, false);
            break;
        case UIServer::HT_PayFail:
            ShowHelpForm(FORM_END);
            m_pFrmShowPrompt->PayFail(helpMsg);
            break;
        case UIServer::HT_Password:
            ShowHelpForm(FORM_END);
            m_pFrmShowPrompt->Password(helpMsg);
            break;
        case UIServer::HT_Login:
            ShowHelpForm(FORM_END);
            m_pFrmWaitVeh->Login();
            break;
        default:
            break;
    }
}

void MainWindow::OnTollInfo(const QString vlp, const QString vehClassName,
                            const QString vehTypeName, const QString enStationName,
                            quint32 tollMoney, bool bBeforePay, quint8 channelType)
{
    m_sVlp = vlp;
    m_sVehClassName = vehClassName;
    m_sVehTypeName = vehTypeName;
    m_sEnStation = enStationName;
    m_nMoney = tollMoney;
    ShowHelpForm(FORM_END);
    m_pFrmShowMoney->ShowMoney(m_sVlp, m_sVehClassName, m_sVehTypeName, m_sEnStation, m_nMoney,
                               bBeforePay, (CTransPayType)channelType);
}

void MainWindow::OnTicketInfo(const QString vlp, const QString vehClassName,
                              const QString vehTypeName, const QString enStationName,
                              quint32 tollMoney, quint8 channelType, const QString qrCode)
{
    m_sVlp = vlp;
    m_sVehClassName = vehClassName;
    m_sVehTypeName = vehTypeName;
    m_sEnStation = enStationName;
    m_nMoney = tollMoney;
    ShowHelpForm(FORM_END);
    m_pFrmShowMoney->ShowETicketInfo(m_sVlp, m_sVehClassName, m_sVehTypeName, m_sEnStation,
                                     m_nMoney, (CTransPayType)channelType, qrCode);
}

bool MainWindow::Init()
{
    if (!m_pServer->StartServer()) {
        return false;
    }

    m_tidSecond = startTimer(1000);

    return true;
}

bool MainWindow::Destroy()
{
    if (m_pServer) {
        m_pServer->StopServer();
    }
    killTimer(m_tidSecond);
    return true;
}

void MainWindow::InitUI()
{
    setWindowFlags(Qt::FramelessWindowHint);
    setGeometry(g_GlobalUI.m_RectMainWnd);

    //等待车辆
    m_pFrmWaitVeh->InitUI();
    //提示插卡
    m_pFrmInsertCard->InitUI();
    //进行提示
    m_pFrmShowPrompt->InitUI();
    //显示金额，提示扫码
    m_pFrmShowMoney->InitUI();
    //提示票据
    m_pFrmShowInvoice->InitUI();
    //暂停服务
    m_pFrmOutOfService->InitUI();
    show();

    ShowHelpForm(FORM_OutOfService);
}

void MainWindow::paintEvent(QPaintEvent *)
{
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::TextAntialiasing);
    painter.setBrush(Qt::white);
    painter.drawRect(0, 0, rectClient.width() - 1, rectClient.height() - 1);
    //绘制标题栏和状态栏背景
    QColor clrTitle(44, 54, 67);
    painter.setPen(Qt::NoPen);
    painter.setBrush(clrTitle);
    painter.drawRect(g_GlobalUI.main_RectTitle);
    painter.drawRect(g_GlobalUI.main_RectStatus);
    //绘制logo
    if (g_GlobalUI.main_RectLogo.width() > 0) {
        QPixmap pmLogo;
        pmLogo.load(":/images/logo.png");
        painter.drawPixmap(g_GlobalUI.main_RectLogo,
                           pmLogo.scaled(g_GlobalUI.main_RectLogo.size(), Qt::KeepAspectRatio,
                                         Qt::SmoothTransformation));
    }
    //绘制标题文字
    QFont fontTitle(g_GlobalUI.m_FontName, g_GlobalUI.main_TitleFontSize);
    fontTitle.setLetterSpacing(QFont::AbsoluteSpacing, g_GlobalUI.main_TitleFontSpace);
    QColor clrText(255, 255, 255);
    painter.setFont(fontTitle);
    painter.setPen(clrText);
    painter.drawText(g_GlobalUI.main_TitleTextRect, Qt::AlignLeft | Qt::AlignVCenter,
                     g_GlobalUI.main_AppTitle);
    //时间
    painter.drawText(g_GlobalUI.main_TimeTextRect, Qt::AlignRight | Qt::AlignVCenter,
                     QDateTime::currentDateTime().toString("MM/dd hh:mm:ss"));
    //状态栏
    QFont fontStatus(g_GlobalUI.m_FontName, g_GlobalUI.main_StatusFontSize);
    painter.setFont(fontStatus);
    QString strStatus = QString("%1  %2").arg(g_GlobalUI.main_CopyRight).arg(g_ParamMgr.m_sVersion);
    painter.drawText(g_GlobalUI.main_RectStatus, Qt::AlignCenter, strStatus);

    //版本
    //    //字体颜色
    //    QColor clrText(255, 255, 255);
    //    painter.setFont(fontTitle);
    //    painter.setPen(clrText);
    //    painter.drawText(g_GlobalUI.lnif_TitleTextRect, g_GlobalUI.m_sAppTitle);
    //    //收费站名称
    //    QRect stationRect;
    //    painter.setFont(fontStation);
    //    painter.drawText(g_GlobalUI.lnif_StationTextRect, Qt::AlignCenter, m_sStationName,
    //    &stationRect);

    //    painter.setFont(fontLabel);
    //    //授权信息 + 版本信息
    //    painter.drawText(g_GlobalUI.lnif_CopyRightTextRect, QString("%1
    //    程序版本：%2").arg(g_GlobalUI.m_sCopyRight, m_sAppVersion));

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

void MainWindow::mouseDoubleClickEvent(QMouseEvent *)
{
    g_GlobalUI.Init();
    InitUI();
    update();
}

void MainWindow::timerEvent(QTimerEvent *event)
{
    //秒表计时
    if (event->timerId() == m_tidSecond) {
        update();
    }
}

void MainWindow::ShowHelpForm(MainWindow::FormType helpType)
{
    for (int i = 0; i < FORM_END; i++) {
        m_pBaseForms[i]->HideForm();
        if (i == helpType) {
            m_pBaseForms[i]->ShowForm();
        }
    }
}

void MainWindow::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
        case Qt::Key_Escape:

            break;
        default:
            QDialog::keyPressEvent(event);
            break;
    }
}
