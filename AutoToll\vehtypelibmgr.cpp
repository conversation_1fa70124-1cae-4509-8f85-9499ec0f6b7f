#include "vehtypelibmgr.h"
#include "jsonbuilder.h"
#include "ccurl.h"
#include <QUrl>
#include <QVariantMap>
#include "qxtjson.h"
#include "lanetype.h"
#include "cryptographiccommon.h"

CVehTypeLibMgr::CVehTypeLibMgr(QObject *parent) : CPausableThread(parent)
{
    setObjectName(QString("VehType Qry thread"));
    m_bContinue = false;
    m_pLog = NULL;
    m_pLog = new CLog4Qt();
    m_pLog->SetMaxFileInfo(10 * 1024 * 1024, 30);
    QString sLogPath = QApplication::applicationDirPath() + "//" + QString("VehLibLog");
    m_pLog->InitLog4Qt(QString(), sLogPath, "VehLibLog");
    m_pTokenThread = new CTokenThread();
}

bool CVehTypeLibMgr::RunOnce()
{
    DebugLog(QString("启动车型库查询线程"));
    CVehTypeQryCondition condition;
    int nTasksSize = 0;
    m_qryMt.lock();
    nTasksSize = m_Tasks.size();
    m_qryMt.unlock();
    while (nTasksSize > 0) {
        m_qryMt.lock();
        QList<CVehTypeQryCondition>::iterator it = m_Tasks.begin();
        condition = *it;
        it->nState = 1;
        m_qryMt.unlock();

        CVehInfo_VehLib vehInfo;
        bool bRlt = QueryVehType(condition.nColor, condition.sVehicle, vehInfo);
        if (bRlt) {
            QMutexLocker locker(&m_vehMt);
            m_vehList.push_back(vehInfo);
            while (m_vehList.size() > 5) {
                m_vehList.pop_front();
            }
            
            // 记录查询结果的vehicleTypeDbInfo字段
            DebugLog(QString("查询成功,车牌:%1,颜色:%2,车型:%3,车种:%4,轴数:%5,可信度:%6,车型库信息:%7")
                    .arg(condition.sVehicle)
                    .arg(condition.nColor)
                    .arg(vehInfo.vehClass)
                    .arg(vehInfo.vehType)
                    .arg(vehInfo.axleNum)
                    .arg(vehInfo.score)
                    .arg(vehInfo.vehicleTypeDbInfo));
            
            // 发送查询完成信号
            emit VehResultReady(condition.sVehicle, condition.nColor, true);
        } else {
            // 查询失败也发送信号
            emit VehResultReady(condition.sVehicle, condition.nColor, false);
        }
        
        {
            QMutexLocker locker(&m_qryMt);
            m_Tasks.pop_front();
            m_waitCondition.wakeAll();
            nTasksSize = m_Tasks.size();
        }
        if (CheckExit(100)) return true;
    }
    PauseThread();
    return true;
}

void CVehTypeLibMgr::InitVehTypeLib(const QString &sUrl, const QString &sQryToken,
                                    const QString &sRefreshToken, const QString &sSaveToken)
{
    m_sUrl = sUrl;

    if (m_pTokenThread) {
        m_pTokenThread->SetUrl(sQryToken, sRefreshToken, sSaveToken);
        m_pTokenThread->ResumeThread();
    }
}

bool CVehTypeLibMgr::GetVehResult(const QString &sVlp, int nColor, CVehInfo_VehLib &vehInfo)
{
    m_qryMt.lock();
    if (m_Tasks.size() > 0) {
        CVehTypeQryCondition condition = m_Tasks.first();
        if (condition.sVehicle == sVlp && condition.nColor == nColor) {
            //正在查询
            if (condition.nState >= 0) {
                DebugLog(QString("等待车型库查询返回结果..."));
                if (!m_waitCondition.wait(&m_qryMt, 1000)) {
                    DebugLog("等待车型库查询返回超时");
                } else {
                    DebugLog("等待车型库查询返回成功");
                }
            }
        }
    }
    m_qryMt.unlock();
    //查询结果
    QMutexLocker locker(&m_vehMt);
    foreach (CVehInfo_VehLib tmpInfo, m_vehList) {
        if (tmpInfo.sVehicleId == sVlp && tmpInfo.nColor == nColor) {
            vehInfo = tmpInfo;
            DebugLog(QString("匹配到车型库结果,车牌:%1,颜色:%2,车型:%3,车种:%4,轴数:%5,可信度:%6,车型库信息:%7")
                         .arg(sVlp)
                         .arg(nColor)
                         .arg(vehInfo.vehClass)
                         .arg(vehInfo.vehType)
                         .arg(vehInfo.axleNum)
                         .arg(vehInfo.score)
                         .arg(vehInfo.vehicleTypeDbInfo));
            return true;
        }
    }
    return false;
}

void CVehTypeLibMgr::AddQuryTask(const QString &sVlp, int nColor)
{
    CVehTypeQryCondition condition;
    condition.sVehicle = sVlp;
    condition.nColor = nColor;
    condition.nState = 0;
    {
        QMutexLocker locker(&m_qryMt);
        bool bSameVeh = false;
        foreach (CVehTypeQryCondition tmp, m_Tasks) {
            if (condition.nColor == tmp.nColor && condition.sVehicle == tmp.sVehicle) {
                bSameVeh = true;
                break;
            }
        }
        if (!bSameVeh) {
            /*
            while (m_Tasks.size() > 2) {
                DebugLog(QString("车型库查询队列任务数%1,清除多余任务").arg(m_Tasks.size()));
                m_Tasks.pop_back();
            }*/
            m_Tasks.push_back(condition);
            DebugLog(QString("添加车型库查询任务,vlp:%1,color:%2").arg(sVlp).arg(nColor));
        } else {
            DebugLog(QString("已存在相同查询任务%1_%2").arg(sVlp).arg(nColor));
            return;
        }
    }
    this->ResumeThread();
}

void CVehTypeLibMgr::AsyncGetVehResult(const QString &sVlp, int nColor)
{
    // 检查队列中是否已存在相同的查询任务
    {
        QMutexLocker locker(&m_qryMt);
        foreach (CVehTypeQryCondition tmp, m_Tasks) {
            if (tmp.nColor == nColor && tmp.sVehicle == sVlp) {
                DebugLog(QString("已存在相同查询任务%1_%2，不再重复添加").arg(sVlp).arg(nColor));
                return;
            }
        }
    }
    
    // 先检查缓存中是否已有结果
    {
        QMutexLocker locker(&m_vehMt);
        foreach (CVehInfo_VehLib tmpInfo, m_vehList) {
            if (tmpInfo.sVehicleId == sVlp && tmpInfo.nColor == nColor) {
                DebugLog(QString("缓存中已有结果,vehtype:%1,score:%2")
                             .arg(tmpInfo.vehClass)
                             .arg(tmpInfo.score));
                // 发送信号通知结果已准备好
                emit VehResultReady(sVlp, nColor, true);
                return;
            }
        }
    }
    
    // 添加到查询队列
    CVehTypeQryCondition condition;
    condition.sVehicle = sVlp;
    condition.nColor = nColor;
    condition.nState = 0;
    
    {
        QMutexLocker locker(&m_qryMt);
        m_Tasks.push_back(condition);
        DebugLog(QString("添加异步车型库查询任务,vlp:%1,color:%2").arg(sVlp).arg(nColor));
    }
    
    // 恢复线程处理查询任务
    this->ResumeThread();
}

bool CVehTypeLibMgr::QueryVehFromCache(const QString &sVlp, int nColor, CVehInfo_VehLib &vehInfo)
{
    // 直接从缓存中查询，不触发新的查询任务
    QMutexLocker locker(&m_vehMt);
    foreach (CVehInfo_VehLib tmpInfo, m_vehList) {
        if (tmpInfo.sVehicleId == sVlp && tmpInfo.nColor == nColor) {
            vehInfo = tmpInfo;
            DebugLog(QString("从缓存中查询到车型信息,车牌:%1,颜色:%2,车型:%3,车种:%4,轴数:%5,可信度:%6,车型库信息:%7")
                     .arg(sVlp)
                     .arg(nColor)
                     .arg(vehInfo.vehClass)
                     .arg(vehInfo.vehType)
                     .arg(vehInfo.axleNum)
                     .arg(vehInfo.score)
                     .arg(vehInfo.vehicleTypeDbInfo));
            return true;
        }
    }
    DebugLog(QString("缓存中未找到车型信息,车牌:%1,颜色:%2").arg(sVlp).arg(nColor));
    return false;
}

QString CVehTypeLibMgr::ConvertVlpColor(int nColor)
{
    switch (nColor) {
        case 0:
            return QString("蓝色");
            break;
        case 1:
            return QString("黄色");
            break;
        case 2:
            return QString("黑色");
            break;
        case 3:
            return QString("白色");
            break;
        case 4:
            return QString("渐变绿色");
            break;
        case 5:
            return QString("黄绿双拼色");
            break;
        case 6:
            return QString("蓝白渐变色");
            break;
        case 7:
            return QString("临时牌照");
            break;
        case 9:
            return QString("未确定");
            break;
        case 11:
            return QString("绿色");
            break;
        case 12:
            return QString("红色");
        default:
            return QString("");
            break;
    }
}

void CVehTypeLibMgr::ReleaseVehTypeLibMgr()
{
    if (m_pLog) {
        delete m_pLog;
        m_pLog = NULL;
    }

    if (m_pTokenThread) {
        m_pTokenThread->StopThread();
        delete m_pTokenThread;
        m_pTokenThread = NULL;
    }
    this->StopThread();
    return;
}

void CVehTypeLibMgr::DebugLog_VehMsg(const QString &sMsg)
{
    if (!m_pLog) return;
    m_pLog->LogMsg(LEVEL_DEBUG, sMsg);
}

void CVehTypeLibMgr::DebugLogVehInfo(const CVehInfo &vehInfo)
{
    QString sVehLog;

    QString sVlp = GB2312toUnicode(vehInfo.szVehPlate);

    int nIndex = sVlp.indexOf(QString("临"));
    if (vehInfo.nVehClassWay == VehClassway_Input && nIndex < 0) {
        if (m_pTokenThread) {
            CVehInfo_VehLib vehlibInfo;
            vehlibInfo.Clear();
            vehlibInfo.vehClass = vehInfo.VehClass;
            vehlibInfo.sVehicleId = sVlp;
            vehlibInfo.nColor = vehInfo.nVehPlateColor;
            vehlibInfo.vehType = vehInfo.GBVehType;
            m_pTokenThread->AddVehInfo(vehlibInfo);
        }
    }

    sVehLog = QString("%1,%2,%3,%4,%5,%6,%7(1-设备,2-车型库,3-人工4-标签")
                  .arg(sVlp)
                  .arg(vehInfo.nVehPlateColor)
                  .arg(vehInfo.VehClass)
                  .arg(vehInfo.AutoVehClass)
                  .arg(vehInfo.qryVehClass)
                  .arg(vehInfo.nScore)
                  .arg(vehInfo.nVehClassWay);
    DebugLog_VehMsg(sVehLog);
}

bool CTokenThread::QueryToken(CTokenInfo &tokenInfo)
{
    if (m_sTokenUrl.isEmpty()) return false;
    QMap<QString, QString> map;

    QString sPassword = AesPassWord(tokenInfo.password, m_sKey);
    map.insert(QString("username"), QString("admin"));
    map.insert(QString("password"), sPassword);
    QString sContent = urlEncode(map);

    // DebugLog(QString("ask token:%1").arg(sContent));
    QString sResponse;
    bool bRlt = HttpPost_urlencoded(m_sTokenUrl, sContent, sResponse);
    if (!bRlt) return false;

    DebugLog(QString("sResponse:%1").arg(sResponse));

    QxtJSON parser;
    QVariantMap retMap = parser.parse(sResponse).toMap();
    if (retMap.isEmpty()) {
        DebugLog(QString("token查询返回结果为空"));
        return false;
    }
    if (!retMap.contains(QString("access_token"))) {
        ErrorLog("车型库查询返回结果不包含 code");
        return false;
    }

    QString sToken = parser.stringify(retMap.value(QString("access_token")));
    tokenInfo.access_token = sToken;

    if (retMap.contains(QString("token_type"))) {
        tokenInfo.token_type = parser.stringify(retMap.value(QString("token_type")));
    } else {
        DebugLog(QString("token_type"));
    }

    if (retMap.contains(QString("refresh_token"))) {
        tokenInfo.refresh_token = parser.stringify(retMap.value(QString("refresh_token")));
    } else {
        DebugLog(QString("refresh_token"));
    }

    if (retMap.contains(QString("expires_in"))) {
        tokenInfo.expires_in = parser.stringify(retMap.value(QString("expires_in"))).toInt();
    } else {
        DebugLog(QString("no expires_in"));
    }

    if (!retMap.contains(QString("user_info"))) {
        DebugLog(QString("user_info"));
        return false;
    }

    QVariant varUserInfo = retMap.value(QString("user_info"));
    QVariantMap userInfoMap = varUserInfo.toMap();
    if (userInfoMap.isEmpty()) {
        DebugLog(QString("userInfo is empty"));
        return true;
    }

    if (userInfoMap.contains(QString("id"))) {
        tokenInfo.userInfo.id = parser.stringify(userInfoMap.value(QString("id")));
    } else {
        DebugLog(QString("no userinfo id"));
    }

    if (userInfoMap.contains(QString("username"))) {
        tokenInfo.userInfo.username = parser.stringify(userInfoMap.value(QString("username")));
    } else {
        DebugLog(QString("no userinfo username"));
    }

    if (userInfoMap.contains(QString("authorities"))) {
        tokenInfo.userInfo.authorities =
            parser.stringify(userInfoMap.value(QString("authorities")));
    } else {
        DebugLog(QString("no userinfo authorities"));
    }
    return true;
}

bool CTokenThread::RefreshToken(const QString &sRefreshToken, CTokenInfo &tokenInfo)
{
    QMap<QString, QString> map;
    map.insert(QString("refresh_token"), sRefreshToken);
    QString sContent = urlEncode(map);
    QString sResponse;
    bool bRlt = HttpPost_urlencoded(m_sRefreshTokenUrl, sContent, sResponse);
    if (!bRlt) return false;

    QxtJSON parser;

    DebugLog(QString("sResponse:%1").arg(sResponse));
    QVariantMap retMap = parser.parse(sResponse).toMap();
    if (retMap.isEmpty()) {
        DebugLog(QString("token查询返回结果为空"));
        return false;
    }
    if (!retMap.contains(QString("access_token"))) {
        ErrorLog("车型库查询返回结果不包含 code");
        return false;
    }

    QString sToken = parser.stringify(retMap.value(QString("access_token")));
    tokenInfo.access_token = sToken;

    if (retMap.contains(QString("token_type"))) {
        tokenInfo.token_type = parser.stringify(retMap.value(QString("token_type")));
    } else {
        DebugLog(QString("token_type"));
    }

    if (retMap.contains(QString("refresh_token"))) {
        tokenInfo.refresh_token = parser.stringify(retMap.value(QString("refresh_token")));
    } else {
        DebugLog(QString("refresh_token"));
    }

    if (retMap.contains(QString("expires_in"))) {
        tokenInfo.expires_in = parser.stringify(retMap.value(QString("expires_in"))).toInt();
    } else {
        DebugLog(QString("no expires_in"));
        tokenInfo.expires_in = 0;
    }

    if (!retMap.contains(QString("user_info"))) {
        DebugLog(QString("user_info"));
        return false;
    }

    QVariant varUserInfo = retMap.value(QString("user_Info"));
    QVariantMap userInfoMap = varUserInfo.toMap();
    if (userInfoMap.isEmpty()) {
        DebugLog(QString("userInfo is empty"));
        return false;
    }

    if (userInfoMap.contains(QString("id"))) {
        tokenInfo.userInfo.id = parser.stringify(userInfoMap.value(QString("id")));
    } else {
        DebugLog(QString("no userinfo id"));
    }

    if (userInfoMap.contains(QString("username"))) {
        tokenInfo.userInfo.username = parser.stringify(userInfoMap.value(QString("username")));
    } else {
        DebugLog(QString("no userinfo username"));
    }

    if (userInfoMap.contains(QString("authorities"))) {
        tokenInfo.userInfo.authorities =
            parser.stringify(userInfoMap.value(QString("authorities")));
    } else {
        DebugLog(QString("no userinfo authorities"));
    }
    return true;
}

bool CTokenThread::SaveVehType(const QString &sToken, const CVehInfo_VehLib &vehInfo)
{
    if (m_sSaveVehUrl.isEmpty()) return false;
    CJsonBuilder jBuilder;
    QString sVehId = QString("%1_%2").arg(vehInfo.sVehicleId).arg(vehInfo.nColor);
    jBuilder.AddKeyValue(QString("vehicleId"), sVehId);
    jBuilder.AddKeyValue(QString("vehicleType"), QString("%1").arg(vehInfo.vehClass));
    jBuilder.AddKeyValue_Int(QString("axleNum"), vehInfo.axleNum);
    jBuilder.AddKeyValue_Int(QString("seatingNum"), vehInfo.seatingNum);
    jBuilder.AddKeyValue_Int(QString("wheelbase"), 0);
    jBuilder.AddKeyValue_Uint(QString("rateWeight"), vehInfo.ratedWeight);
    jBuilder.AddKeyValue_Int(QString("vehicleClass"), vehInfo.vehType);
    QString sJson = jBuilder.CreateJsonStr();
    if (sJson.length() > 0) sJson.remove(0, 1);

    sJson = QString("{%1}").arg(sJson);
    DebugLog(QString("%1:%2,%3").arg(sJson).arg(sToken).arg(m_sSaveVehUrl));
    QString sResponse;
    bool bRlt = HttpPost_ByToken(m_sSaveVehUrl, sJson, sToken, sResponse);
    if (!bRlt) {
        DebugLog(QString("save vehtype innfo post failed"));
        return false;
    }

    QxtJSON parser;

    DebugLog(QString("savevehtype:%1").arg(sResponse));
    QVariantMap retMap = parser.parse(sResponse).toMap();
    if (retMap.isEmpty()) {
        DebugLog(QString("保存车型信息返回结果为空"));
        return false;
    }
    if (!retMap.contains(QString("code"))) {
        ErrorLog("车型库查询返回结果不包含 code");
        return false;
    }

    int nCode = parser.stringify(retMap.value(QString("code"))).toInt();
    if (200 != nCode) {
        DebugLog(QString("车型库查询返回code:%1,查询失败").arg(nCode));
        return false;
    }

    if (retMap.contains(QString("message"))) {
        QString sMessage = parser.stringify(retMap.value(QString("message")));
        DebugLog(QString("save vehtype:%1").arg(sMessage));
    }
    return true;
}

bool CVehTypeLibMgr::QueryVehType(int nVlpColor, const QString &sVehPlate, CVehInfo_VehLib &vehInfo)
{
    QString sVehicleId = QString("%1_%2").arg(sVehPlate).arg(nVlpColor);
    QString sJson = QString("{\"vehicleId\":\"%1\"}").arg(sVehicleId);
    DebugLog(QString("query vehtype,url:%1,sJson:%2").arg(m_sUrl).arg(sJson));
    QString sResponse;
    bool bRlt = HttpPost(m_sUrl, sJson, sResponse);
    if (!bRlt) {
        DebugLog(QString("车型库通讯处理失败"));
        return false;
    }
    bRlt = ParseQueryResult(sResponse, vehInfo);
    if (!bRlt) {
        DebugLog("车型库返回数据解析失败");
        return false;
    }
    vehInfo.sVehicleId = sVehPlate;
    vehInfo.nColor = nVlpColor;
    DebugLog(QString("车型库查询返回成功."));
    return bRlt;
}

bool HttpPost(const QString &sUrl, const QString &sContent, QString &sRespons)
{
    Curl curl;
    curl.addRequestHeaders(QString("Content-Type:application/json;charset=utf-8"));
    QByteArray content = sContent.toUtf8();
    curl.addRequestBody(content);
    quint32 nResponCode = curl.postRequest_NoForm(sUrl);
    sRespons = QString::fromUtf8(curl.getResponseBody());
    DebugLog(QString("车型库查询返回:%1,%2").arg(nResponCode).arg(sRespons));
    return nResponCode == 200;
}

QString CTokenThread::urlEncode(const QMap<QString, QString> &params)
{
    QStringList encodedItems;
    for (QMap<QString, QString>::const_iterator it = params.begin(); it != params.end(); ++it) {
        QString key = QUrl::toPercentEncoding(it.key());
        QString value = QUrl::toPercentEncoding(it.value());
        encodedItems.append(key + "=" + value);
    }
    return encodedItems.join("&");
}

QString CTokenThread::AesPassWord(QString Password, QString &key)
{
    char sourceStringTemp[AES_MSG_LEN];
    char dstStringTemp[AES_MSG_LEN];
    memset((char *)sourceStringTemp, 0, AES_MSG_LEN);
    memset((char *)dstStringTemp, 0, AES_MSG_LEN);

    strcpy((char *)sourceStringTemp, Password.toLatin1().data());
    int len = strlen(sourceStringTemp);
    int padding = AES_BLOCK_SIZE - len % AES_BLOCK_SIZE;
    memset((char *)sourceStringTemp + len, 0, padding);
    QByteArray bKey = key.toLocal8Bit().data();
    CryptographicCommon::aes_encrypt_cbc(sourceStringTemp, (char *)bKey.data(), (char *)bKey.data(),
                                         dstStringTemp);

    return QByteArray(dstStringTemp).toBase64();
}

void CTokenThread::AddVehInfo(const CVehInfo_VehLib &vehInfo)
{
    QMutexLocker locker(&m_listMt);
    m_vehList.push_back(vehInfo);
    while (m_vehList.size() > 5) {
        m_vehList.pop_front();
    }
}

bool CTokenThread::HttpPost_urlencoded(const QString &sUrl, const QString &sContent,
                                       QString &sRespons)
{
    Curl curl;
    curl.addRequestHeaders(QString("Content-Type:application/x-www-form-urlencoded"));
    curl.addRequestHeaders(QString("authorization:%1").arg(QString("Basic YWRtaW46YWRtaW4=")));
    QByteArray content = sContent.toUtf8();
    curl.addRequestBody(content);
    quint32 nResponCode = curl.postRequest_NoForm(sUrl, false);
    if (200 == nResponCode) {
        sRespons = QString::fromUtf8(curl.getResponseBody());
        return true;
    }

    return false;
}

bool CTokenThread::HttpPost_ByToken(const QString &sUrl, const QString &sContent,
                                    const QString stoken, QString &sRespons)
{
    Curl curl;
    curl.addRequestHeaders(QString("Content-Type:application/json;charset=utf-8"));
    curl.addRequestHeaders(QString("authorization:Bearer %1").arg(stoken));
    QByteArray content = sContent.toUtf8();
    curl.addRequestBody(content);
    quint32 nResponCode = curl.postRequest_NoForm(sUrl);
    sRespons = QString::fromUtf8(curl.getResponseBody());
    DebugLog(QString("保存车型返回:%1,%2").arg(nResponCode).arg(sRespons));
    return nResponCode == 200;
}

bool CVehTypeLibMgr::ParseQueryResult(const QString &sResponse, CVehInfo_VehLib &vehInfo)
{
    QxtJSON parser;

    QVariantMap retMap = parser.parse(sResponse).toMap();
    if (retMap.isEmpty()) {
        DebugLog(QString("车型库查询返回结果为空"));
        return false;
    }
    if (!retMap.contains(QString("code"))) {
        ErrorLog("车型库查询返回结果不包含 code");
        return false;
    }

    int nCode = parser.stringify(retMap.value(QString("code"))).toInt();
    if (200 != nCode) {
        DebugLog(QString("车型库查询返回code:%1,查询失败").arg(nCode));
        return false;
    }

    if (retMap.contains(QString("message"))) {
        vehInfo.message = parser.stringify(retMap.value(QString("message")));
    }

    if (!retMap.contains(QString("result"))) {
        ErrorLog("车型库查询返回无result");
        return false;
    }

    QVariant varResult = retMap.value(QString("result"));
    QVariantMap resultMap = varResult.toMap();
    if (resultMap.isEmpty()) {
        DebugLog(QString("result is empty"));
        return false;
    }

    QString sKey = QString("vehicleType");
    if (!resultMap.contains(sKey)) {
        ErrorLog(QString("no vehicleType"));
        return false;
    }

    int nVehType = parser.stringify(resultMap.value(sKey)).toInt();
    QString svehTypeName = GetVehClassName(nVehType);
    if (svehTypeName.isEmpty()) {
        DebugLog(QString("error veh type,%1").arg(nVehType));
        return false;
    }
    vehInfo.vehClass = CVehClass(nVehType);
    sKey = QString("plateId");
    if (resultMap.contains(sKey)) {
        vehInfo.sVehicleId = parser.stringify(resultMap.value(sKey));
    }

    sKey = QString("plateColor");
    if (resultMap.contains(sKey)) {
        vehInfo.nColor = parser.stringify(resultMap.value(sKey)).toInt();
    }

    sKey = QString("seatingNum");
    if (resultMap.contains(sKey)) {
        vehInfo.seatingNum = parser.stringify(resultMap.value(sKey)).toInt();
    }

    sKey = QString("axleNum");
    if (resultMap.contains(sKey)) {
        vehInfo.axleNum = parser.stringify(resultMap.value(sKey)).toInt();
    }
    sKey = QString("ratedWeight");
    if (resultMap.contains(sKey)) {
        vehInfo.ratedWeight = parser.stringify(resultMap.value(sKey)).toUInt();
    }

    sKey = QString("vehicleClass");
    if (resultMap.contains(sKey)) {
        vehInfo.vehType = parser.stringify(resultMap.value(sKey)).toInt();
    }

    sKey = QString("imgs");
    if (resultMap.contains(sKey)) {
        vehInfo.imgs = parser.stringify(resultMap.value(sKey));
    }

    sKey = QString("score");
    if (resultMap.contains(sKey)) {
        vehInfo.score = parser.stringify(resultMap.value(sKey)).toInt();
    }
    
    // 构建vehicleTypeDbInfo字段（格式：车型|车种|轴数|轴型）
    QString axleType = " "; // 默认使用空格
    QString vehTypeStr = QString::number(vehInfo.vehClass);
    QString vehClassStr = QString::number(vehInfo.vehType);
    QString axleNumStr = QString::number(vehInfo.axleNum);
    
    // 检查各字段是否有效，无效则使用空格代替
    if (vehInfo.vehClass == VC_None) {
        vehTypeStr = " ";
    }
    
    if (vehInfo.vehType <= 0) {
        vehClassStr = " ";
    }
    
    if (vehInfo.axleNum <= 0) {
        axleNumStr = " ";
    }
    
    // 拼接车型库信息字符串
    vehInfo.vehicleTypeDbInfo = QString("%1|%2|%3|%4")
                                 .arg(vehTypeStr)    // 车型
                                 .arg(vehClassStr)   // 车种
                                 .arg(axleNumStr)    // 轴数
                                 .arg(axleType);     // 轴型
    
    DebugLog(QString("车型库信息: %1").arg(vehInfo.vehicleTypeDbInfo));
    
    return true;
}

CTokenThread::CTokenThread(QObject *parent) : CPausableThread(parent)
{
    setObjectName(QString("qrytokenthread"));
    m_waitTime = 1000;
    m_sKey = QString("bimxbimxbimxbimx");
}

bool CTokenThread::RunOnce()
{
    static time_t lastReqTime = 0;
    time_t curTime = QDateTime::currentDateTime().toTime_t();
    if (curTime - lastReqTime > 60 * 10) {  // 10
        CTokenInfo tokenInfo;
        tokenInfo.username = QString("admin");
        tokenInfo.password = QString("123456");
        if (this->QueryToken(tokenInfo)) {
            m_tokenInfo = tokenInfo;
            lastReqTime = curTime;
        } else {
            if (CheckExit(200)) return true;
            if (m_tokenInfo.access_token.isEmpty()) {
                return true;
            } else
                lastReqTime = curTime;
        }
    }

    //
    while (1) {
        CVehInfo_VehLib vehInfo;
        {
            QMutexLocker locker(&m_listMt);
            if (0 == m_vehList.size()) break;
            vehInfo = m_vehList.front();
            m_vehList.pop_front();
        }
        SaveVehType(m_tokenInfo.access_token, vehInfo);
        if (CheckExit(100)) {
            break;
        }
    }
    if (CheckExit(1000)) return true;
    return true;
}
