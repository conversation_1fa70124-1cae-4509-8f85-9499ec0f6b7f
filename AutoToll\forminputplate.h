#ifndef FORMPLATEINPUT_H
#define FORMPLATEINPUT_H
#include <QWidget>
#include "baseopwidget.h"
#include "lanetype.h"

//省份的拼音选择
typedef struct
{
    QString sPy;
    QString sHz;
} ProvSelect;

class FormInputPlate : public CBaseOpWidget
{
    Q_OBJECT
public:
    explicit FormInputPlate(QWidget *parent = 0);
    ~FormInputPlate();
signals:

public slots:

private:
    QLineEdit *m_pEdtPlate;
    QLineEdit *m_pEdtColor;

    QList<ProvSelect> *m_pProvSelList;
    QStringList m_lstWaitSelect;

    /*******
     * 由于车牌颜色对应字母键，要输入车牌颜色，有两种方式：
     * 1. 将输入的车牌信息删除到空（包括颜色m_nVlpColor=-1),然后输入
     * 2. 按（票号键），可直接进入
     * ************************************/
    //车牌颜色输入状态
    bool m_bColorInputState;
    //输入的车牌颜色
    VP_COLOR m_nVlpColor;
    //输入的车牌
    QString m_sPlate;
    //光标位置
    int m_nCursorPos;
    //已经输入的拼音
    QString m_sInputedPy;
    
    //翻页相关变量
    int m_nCurrentPage;        // 当前页码（从0开始）
    int m_nItemsPerPage;       // 每页显示的项目数量
    int m_nTotalPages;         // 总页数

    //初始的车牌
    VP_COLOR m_nOriginalVlpColor;
    QString m_sOriginalPlate;

protected:
    //清除拼音输入
    void ClearPinyinInput();
    //改变车牌颜色输入状态
    void SetColorInputState(bool bColorState);
    //加载省份选择文件
    bool LoadProvSelectFromFile();
    //根据拼音选择省份汉字
    void SelProvByPinyin(const QString &py, QStringList &lstProv);
    //更新车牌颜色显示
    void RefreshColorShow();
    //更新车牌输入显示
    void RefreshPlateShow();
    //更新汉字选择显示
    void RefreshProvSelect();
    //翻页相关方法
    void UpdatePagination();      // 更新翻页信息
    void NextPage();              // 下一页
    void PrevPage();              // 上一页
    void ResetPagination();       // 重置翻页状态
    //验证车牌合法性
    bool ValidateInput(QString &sErrMsg);
    bool CheckPlateMaxLen(const QString &sPlate);
    //恢复初始编辑的车牌
    void ResumeToOriginal();
    //
    void OnInputLetter(int keyInput);
    void OnInputNumber(int keyInput);
    //处理键盘输入
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);

public:
    void InitUI();
    //编辑车牌
    bool EditPlate(const QString &sPlate, VP_COLOR vlpColor);
    void GetInputResult(QString &sPlate, VP_COLOR &vlpColor);

protected:
    void paintEvent(QPaintEvent *);
};

#endif  // FORMPLATEINPUT_H
