#ifndef CWEIGHTDBMGR_H
#define CWEIGHTDBMGR_H

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QList>
#include <QMutex>
#include "ilogmsg.h"

class CWeightDB;
class SWeightData;
class SHttpServerInfo;
class CCheckHttpServerIP;

class CWeightDBMgr : public QObject
{
    Q_OBJECT
public:
    explicit CWeightDBMgr(QObject *parent = 0);
    ~CWeightDBMgr();

public:
    static CWeightDBMgr *GetInstance()
    {
        static CWeightDBMgr weightDBMgr;
        return &weightDBMgr;
    }
private slots:
    void onResetHttpServerInfo(const QList<SHttpServerInfo>&);

public:
    bool Init(const QString &);

    //查找数据
    /*函数名：FindData
     *参数1： const QString &，车牌号，特殊要求：unicode编码(调试状态下可识别的汉字)
     *参数2： const quint16 &，车牌颜色，无特殊要求
     *参数3： SWeightData &，  引用出参，保存有计重车辆的所有信息。非const类型！
     *返回值：true，查询成功；false，查询失败。
     */
    bool FindData(const QString&, const quint16&, SWeightData&);

    //新增数据
    bool AddData(const SWeightData&);

private:
    void InitHttpServiceLog();
    bool InitWeightDB(const QString &);
    bool GetHttpServerUrl(const QString &);
    void CheckHttpServer();

    bool ProcessWeightData(const QString&, const quint16&, SWeightData&, bool bAddData = true);
    //向称台查询计重信息
    bool AskforWeightPlatform(const QString&, const quint16&, SWeightData&);

    void MgrErrorLog(const QString &sLog)
    {
        ErrorLog(sLog);
    }
    void MgrDebugLog(const QString &sLog)
    {
        DebugLog(sLog);
    }
    void MgrTraceLog(const QString &sLog)
    {
        TraceLog(sLog);
    }

private:
    CWeightDB *m_pWeightDB;
    QList<SHttpServerInfo> m_lstHttpServerInfo;
    CCheckHttpServerIP *m_pCheckHttpIp;
    QMutex m_Mutex;                     // 互斥量
};

#endif // CWEIGHTDBMGR_H
