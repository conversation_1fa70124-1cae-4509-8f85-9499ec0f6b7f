﻿#include "VPRDev.h"

#include <QLibrary>
#ifdef Q_OS_WIN32
#include <windows.h>
#endif
#include <QByteArray>
#include <QDebug>
#include <QMessageBox>
#include <QTextCodec>

#include "common/globalutils.h"
#include "common/lanetype.h"
#include "common/vehplatefunc.h"
//#include "common/paramfilemgr.h"

bool CVPRDev::m_bDriverLoadedStd = false;
Func_VLPR_Init CVPRDev::VLPR_Init = NULL;
Func_VLPR_DeInit CVPRDev::VLPR_DeInit = NULL;
Func_VLPR_Login CVPRDev::VLPR_Login = NULL;
Func_VLPR_Logout CVPRDev::VLPR_Logout = NULL;
Func_VLPR_SetResultCallBack CVPRDev::VLPR_SetResultCallBack = NULL;
Func_VLPR_SetStatusCallBack CVPRDev::VLPR_SetStatusCallBack = NULL;
Func_VLPR_ManualSnap CVPRDev::VLPR_ManualSnap = NULL;
Func_VLPR_GetStatus CVPRDev::VLPR_GetStatus = NULL;
Func_VLPR_GetStatusMsg CVPRDev::VLPR_GetStatusMsg = NULL;
Func_VLPR_GetHWVersion CVPRDev::VLPR_GetHWVersion = NULL;

void CVPRResult::ClearResult()
{
    nColor = VP_COLOR_NONE;
    nRating = 0;
    nBigImgSize = 0;
    nSmallImgSize = 0;
    nBinImgSize = 0;
    nBinImgWidth = 0;
    nBinImgHeight = 0;
    memset(&VehClass, 0, sizeof VehClass);
    memset(&nVCRating, 0, sizeof nVCRating);
    memset(szVehPlate, 0, sizeof szVehPlate);
    nIndex = -1;
    sBigName.clear();
    sSmallName.clear();
    sBinName.clear();
}

bool CVPRResult::AllocateMem()
{
    ReleaseMem();
    /*
        lpBigImgBuf = new quint8[BIG_IMG_BUF_SIZE];
        nBigImgSize = 0;

        lpSmallImgBuf = new quint8[SMALL_IMG_BUF_SIZE];
        nSmallImgSize = 0;

        lpBinImgBuf = new quint8[BIN_IMG_BUF_SIZE];
        nBinImgSize = 0;*/

    return true;
}

void CVPRResult::ReleaseMem()
{
    /*
    if (NULL != lpBigImgBuf) {
        delete[] lpBigImgBuf;
        lpBigImgBuf = NULL;
    }
    if (NULL != lpSmallImgBuf) {
        delete[] lpSmallImgBuf;
        lpSmallImgBuf = NULL;
    }
    if (NULL != lpBinImgBuf) {
        delete[] lpBinImgBuf;
        lpBinImgBuf = NULL;
    }*/
}

bool CVPRResult::SaveBigImgAsFile(const QString &sFileName) const
{
    if (!HasBigImg()) {
        return false;
    }
    return false;  // WriteToFile(sFileName, (char *)lpBigImgBuf, nBigImgSize);
}

bool CVPRResult::HasBigImg() const
{
    return nBigImgSize >
           0;  //(NULL != lpBigImgBuf) && (nBigImgSize > 0) && (nBigImgSize <= BIG_IMG_BUF_SIZE);
}

bool CVPRResult::HasSmallImg() const
{
    return nSmallImgSize > 0;  //(NULL != lpSmallImgBuf) && (nSmallImgSize > 0) && (nSmallImgSize <=
                               // SMALL_IMG_BUF_SIZE);
}

bool CVPRResult::HasBinImg() const
{
    return nBinImgSize >
           0;  //(NULL != lpBinImgBuf) && (nBinImgSize > 0) && (nBinImgSize <= BIN_IMG_BUF_SIZE);
}
CVPRDev::CVPRDev(int nIndex) : CAbstractDev()
{
    VPR_RegisterMessage = NULL;
    VPR_RegisterCallback = NULL;
    VPR_Connect = NULL;
    VPR_Disconnect = NULL;
    VPR_Capture = NULL;
    VPR_GetPlate = NULL;
    VPR_GetBigImg = NULL;
    VPR_GetSmallImg = NULL;
    VPR_GetBinImg = NULL;
    VPR_GetAllVehInfo = NULL;
    VPR_GetVer = NULL;
    VR_GetVehClass = NULL;
    VR_GetVehClassGroup = NULL;
    VR_GetAllVehInfo = NULL;
    VR_GetSglVCByPlate = NULL;

    for (int i = 0; i < VPRResArraySize; i++) {
        m_VCRResultArray[i].AllocateMem();
    }
    m_bDriverLoaded = false;
    m_nHandle = 0;
    m_nDevIndex = nIndex;
    m_nCurIndex = -1;
}

CVPRDev::~CVPRDev()
{
    // 关闭设备
    CloseDev();
    // 初始化
    ReleaseDriver();
}

bool CVPRDev::LoadDriver()
{
    if (m_bDriverLoaded || m_bDriverLoadedStd) {
        return true;
    }

    QString sPath = GetCurrentPath() + m_sDriver;

    m_hLibModule.setFileName(sPath);
    if (!m_hLibModule.load()) {
        m_lastError = QString("加载车牌识别动态库%1失败").arg(sPath);
        ErrorLog(m_lastError);
        return false;
    }
    if (m_bStd) {
        VLPR_Init = (Func_VLPR_Init)m_hLibModule.resolve("VLPR_Init");
        VLPR_DeInit = (Func_VLPR_DeInit)m_hLibModule.resolve("VLPR_Deinit");
        VLPR_Login = (Func_VLPR_Login)m_hLibModule.resolve("VLPR_Login");
        VLPR_Logout = (Func_VLPR_Logout)m_hLibModule.resolve("VLPR_Logout");
        VLPR_SetResultCallBack =
            (Func_VLPR_SetResultCallBack)m_hLibModule.resolve("VLPR_SetResultCallBack");
        VLPR_SetStatusCallBack =
            (Func_VLPR_SetStatusCallBack)m_hLibModule.resolve("VLPR_SetStatusCallBack");
        VLPR_ManualSnap = (Func_VLPR_ManualSnap)m_hLibModule.resolve("VLPR_ManualSnap");
        VLPR_GetStatus = (Func_VLPR_GetStatus)m_hLibModule.resolve("VLPR_GetStatus");
        VLPR_GetStatusMsg = (Func_VLPR_GetStatusMsg)m_hLibModule.resolve("VLPR_GetStatusMsg");
        //      VLPR_GetHWVersion=
        //      (Func_VLPR_GetHWVersion)m_hLibModule.resolve("VLPR_GetHWVersion");
        if (NULL == VLPR_Init || NULL == VLPR_DeInit || NULL == VLPR_Login || NULL == VLPR_Logout ||
            NULL == VLPR_SetResultCallBack || NULL == VLPR_SetStatusCallBack ||
            NULL == VLPR_ManualSnap || NULL == VLPR_GetStatus || NULL == VLPR_GetStatusMsg) {
            m_lastError = QString("获取标准化车牌识别动态库[%1]中一个或多个函数失败").arg(sPath);
            ErrorLog(m_lastError);
            ReleaseDriver();
            return false;
        }
        m_bDriverLoadedStd = true;

    } else {
        DebugLog("非标准化");
        VPR_RegisterMessage = (Func_VPR_RegisterMessage)m_hLibModule.resolve("VPR_RegisterMessage");
        VPR_RegisterCallback =
            (Func_VPR_RegisterCallback)m_hLibModule.resolve("VPR_RegisterCallback");
        VPR_Connect = (Func_VPR_Connect)m_hLibModule.resolve("VPR_Connect");
        VPR_Disconnect = (Func_VPR_Disconnect)m_hLibModule.resolve("VPR_Disconnect");
        VPR_Capture = (Func_VPR_Capture)m_hLibModule.resolve("VPR_Capture");
        VPR_GetPlate = (Func_VPR_GetPlate)m_hLibModule.resolve("VPR_GetPlate");
        VPR_GetBigImg = (Func_VPR_GetBigImg)m_hLibModule.resolve("VPR_GetBigImg");
        VPR_GetSmallImg = (Func_VPR_GetSmallImg)m_hLibModule.resolve("VPR_GetSmallImg");
        VPR_GetBinImg = (Func_VPR_GetBinImg)m_hLibModule.resolve("VPR_GetBinImg");
        VPR_GetAllVehInfo = (Func_VPR_GetAllVehInfo)m_hLibModule.resolve("VPR_GetAllVehInfo");
        VPR_GetVer = (Func_VPR_GetVer)m_hLibModule.resolve("VPR_GetVer");
        VR_GetVehClass = (Func_VR_GetVehClass)m_hLibModule.resolve("VR_GetVehClass");
        // 以下为车型识别仪新增接口，为兼容原有车牌识别设备不做判断
        VR_GetVehClassGroup = (Func_VR_GetVehClassGroup)m_hLibModule.resolve("VR_GetVehClassGroup");
        VR_GetAllVehInfo = (Func_VR_GetAllVehInfo)m_hLibModule.resolve("VR_GetAllVehInfo");
        VR_GetSglVCByPlate = (Func_VR_GetSglVCByPlate)m_hLibModule.resolve("VR_GetSglVCByPlate");

        if (NULL == VPR_RegisterMessage || NULL == VPR_RegisterCallback || NULL == VPR_Connect ||
            NULL == VPR_Disconnect || NULL == VPR_Capture || NULL == VPR_GetPlate ||
            NULL == VPR_GetBigImg || NULL == VPR_GetSmallImg || NULL == VPR_GetBinImg ||
            NULL == VPR_GetAllVehInfo || NULL == VPR_GetAllVehInfo || NULL == VPR_GetVer) {
            m_lastError = QString("获取车牌识别动态库[%1]中一个或多个函数失败").arg(sPath);
            ErrorLog(m_lastError);
            ReleaseDriver();
            return false;
        }

        if (NULL == VR_GetVehClassGroup || NULL == VR_GetAllVehInfo || NULL == VR_GetSglVCByPlate) {
            DebugLog(QString("车牌识别动态库%1不支持车型识别").arg(sPath));
        }
        m_bDriverLoaded = true;
    }
    return true;
}

void CVPRDev::ReleaseDriver()
{
    // 在释放驱动前，确保调用DeInit清理
    if (m_bDriverLoadedStd && VLPR_DeInit) {
        DebugLog("调用 VLPR_DeInit 清理VPR资源");
        try {
            VLPR_DeInit();
        } catch (...) {
            DebugLog("VLPR_DeInit 调用异常");
        }
    }
    
    // 将动态库置为未加载
    m_bDriverLoaded = false;
    m_bDriverLoadedStd = false;

    VPR_RegisterMessage = NULL;
    VPR_RegisterCallback = NULL;
    VPR_Connect = NULL;
    VPR_Disconnect = NULL;
    VPR_Capture = NULL;
    VPR_GetPlate = NULL;
    VPR_GetBigImg = NULL;
    VPR_GetSmallImg = NULL;
    VPR_GetBinImg = NULL;
    VPR_GetAllVehInfo = NULL;
    VPR_GetVer = NULL;
    VR_GetVehClass = NULL;
    VR_GetVehClassGroup = NULL;
    VR_GetAllVehInfo = NULL;
    VR_GetSglVCByPlate = NULL;

    VLPR_Init = NULL;
    VLPR_DeInit = NULL;
    VLPR_Login = NULL;
    VLPR_Logout = NULL;
    VLPR_SetResultCallBack = NULL;
    VLPR_SetStatusCallBack = NULL;
    VLPR_ManualSnap = NULL;
    VLPR_GetStatus = NULL;
    VLPR_GetStatusMsg = NULL;
    VLPR_GetHWVersion = NULL;
    
    // 等待一小段时间确保所有资源都已释放
    SleeperThread::msleep(200);
    
    m_hLibModule.unload();
}

bool CVPRDev::StartDev()
{
    if (!m_bDriverLoaded && (!m_bDriverLoadedStd)) {
        return false;
    }
    return true;
}

bool CVPRDev::StartVPRDev()
{
    if ((!m_bDriverLoaded) && (!m_bDriverLoadedStd)) {
        return false;
    }

    CloseDev();
    DebugLog(QString("车牌识别参数:%1, %2").arg(m_sConnStr1).arg(m_sConnStr2));
    QByteArray ba1 = m_sConnStr1.toLatin1();
    QByteArray ba2 = m_sConnStr2.toLatin1();
    DebugLog(QString("VLPR_Login: %1 %2").arg(m_btype).arg(ba1.data()));
    if (m_bStdVPR) {
        if (VLPR_Init && (0 == VLPR_Init())) {
            int nHandle = VLPR_Login(m_btype, ba1.data());
            if (nHandle > 0) {
                m_nHandle = nHandle;
                DebugLog(QString("打开车牌识别成功"));
            } else {
                DebugLog(QString("打开车牌识别失败：%1").arg(nHandle));
                ChangeStatus(DEV_STATUS_CommErr);
                return false;
            }
        } else {
            ErrorLog(QString("VLPR_Init调用失败"));
            ChangeStatus(DEV_STATUS_CommErr);
            return false;
        }
    } else {
        int nRet = VPR_Connect(ba1.data(), ba2.data());

        if (0 != nRet) {
            m_lastError = QString("车型识别设备连接失败,ErrorCode:%1").arg(nRet);
            ErrorLog(m_lastError);
            ChangeStatus(DEV_STATUS_CommErr);
            return false;
        }

        // 设备版本及厂商型号不影响使用
        const int MAX_LEN = 128;
        char szVersion[MAX_LEN] = "";
        char szVender[MAX_LEN] = "";
        char szDevType[MAX_LEN] = "";
        nRet = VPR_GetVer(szVersion, szVender, szDevType);
        if (0 != nRet) {
            ErrorLog(
                QString("打开车型识别设备成功，但获取动态库版本信息失败，ErrorCode=%1").arg(nRet));
        } else {
            DebugLog(QString("车型识别设备连接成功,动态库版本[%1][%2,%3]")
                         .arg(szVersion)
                         .arg(szVender)
                         .arg(szDevType));
        }
    }
    ChangeStatus(DEV_STATUS_OK);
    return true;
}

void CVPRDev::CloseDev()
{
    if ((!m_bDriverLoaded) && (!m_bDriverLoadedStd)) {
        return;
    }
    
    if (m_bStdVPR) {
        DebugLog("释放标准化vpr");
        
        // 1. 先清除回调函数，避免在关闭过程中继续回调
        if (VLPR_SetResultCallBack && m_nHandle > 0) {
            DebugLog("清除VPR识别结果回调函数");
            try {
                VLPR_SetResultCallBack(m_nHandle, NULL, NULL);
            } catch (...) {
                DebugLog("清除VPR识别结果回调函数异常");
            }
        }
        
        if (VLPR_SetStatusCallBack && m_nHandle > 0) {
            DebugLog("清除VPR状态回调函数");
            try {
                VLPR_SetStatusCallBack(m_nHandle, 0, NULL, NULL);
            } catch (...) {
                DebugLog("清除VPR状态回调函数异常");
            }
        }
        
        // 2. 等待500毫秒，让正在执行的回调函数完成
        SleeperThread::msleep(500);
        
        // 3. 执行登出操作
        if (NULL == VLPR_Logout) return;
        DebugLog("调用释放标准化vpr");
        try {
            if (m_nHandle > 0) {
                VLPR_Logout(m_nHandle);
                m_nHandle = 0; // 重置句柄
            }
        } catch (...) {
            DebugLog("VLPR_Logout调用异常");
        }
    } else {
        DebugLog("释放非标准化vpr");
        if (NULL == VPR_Disconnect) return;
        DebugLog("释放非标准化vpr");
        try {
            VPR_Disconnect();
        } catch (...) {
            DebugLog("VPR_Disconnect调用异常");
        }
    }
}
//识别结果回调
void CVPRDev::GGetRegResult(int nHandle, T_VLPINFO *pVlpResult, void *pUser)
{
    Q_UNUSED(nHandle);
    Q_UNUSED(pUser);
    DebugLog(QString("车牌识别结果回调"));
    CVPRDev *pDev = (CVPRDev *)pUser;
    if (pDev) pDev->SaveCurrentVPRResult(pVlpResult);
}
//识别设备状态回调
void CVPRDev::GGetDevStatus(int nHandle, int nStatus, void *pUser)
{
    Q_UNUSED(nHandle);
    Q_UNUSED(pUser);
    DebugLog(QString("VPRDEV当前状态:%1").arg(nStatus));
}

// 注册窗口句柄和自定义消息。
int CVPRDev::RegisterMessage(HWND hWnd, uint nMsgID)
{
    if (NULL == VPR_RegisterMessage) {
        return -1;
    }
    if (m_bStdVPR) {
        return 0;
    }
    return VPR_RegisterMessage(hWnd, nMsgID);
}

//车型识别回调函数类型。 void (WINAPI *FnNotifyFunc)(unsigned uIndex);

// 注册车型识别回调函数，当有车型识别结果时回调注册的函数。
int CVPRDev::RegisterCallback(Func_NotifyFunc NotifyFunc)
{
    if (NULL == VPR_RegisterCallback) {
        return -1;
    }
    return VPR_RegisterCallback(NotifyFunc);
}

// 手动抓拍
bool CVPRDev::Capture()
{
    if ((!m_bDriverLoaded) && (!m_bDriverLoadedStd)) {
        return false;
    }
    if (m_bStdVPR) {
        if (NULL == VLPR_ManualSnap) {
            return false;
        }
        return 0 == VLPR_ManualSnap(m_nHandle);

    } else {
        if (NULL == VPR_Capture) {
            return false;
        }
        return 0 == VPR_Capture();
    }
}

bool CVPRDev::SaveCurrentVPRResult(T_VLPINFO *pVlpResult)
{
    if (!pVlpResult) return false;

    DebugLog(QString("save current vpr result,before curIndex:%1").arg(m_nCurIndex));
    m_nCurIndex++;
    m_nCurIndex = m_nCurIndex % VPRResArraySize;
    DebugLog(QString("save current vpr result,curIndex:%1").arg(m_nCurIndex));

    CVPRResult *pRlt = m_VCRResultArray + m_nCurIndex;
    pRlt->ClearResult();

    char szVehPlate[18] = {0} /*, szVehPlateModified[MAX_VEHPLATE_LEN+1]="\0"*/;
    // 去除车牌特殊字符
    RemovePlateSpecChar(szVehPlate, sizeof szVehPlate, (const char *)pVlpResult->sVlpText,
                        MAX_VEHPLATE_LEN + 1);

    QString sPlate = GB2312toUnicode(szVehPlate);
    quint8 bColor = 0xff;

    char szColor[3];
    memset(szColor, 0, sizeof szColor);
    memcpy(szColor, pVlpResult->bVlpColor, 2);
    QString scolor =
        GB2312toUnicode(szColor);  // QString("%1").arg(QString::fromLocal8Bit(szColor));

    DebugLog(QString("保存车牌识别结果,车牌号:%1,颜色:%2,结构大小：%3,大图大小：%4")
                 .arg(sPlate)
                 .arg(scolor)
                 .arg(pVlpResult->nVlpInfoSize)
                 .arg(pVlpResult->nImageLength[0]));

    if (sPlate.isEmpty() || sPlate.contains(QString("无车牌"))) {
        return false;
    }
    bool flag = false;
    if (sPlate.startsWith("黄绿") || sPlate.startsWith("蓝白")) {
        scolor = sPlate.left(2);
        sPlate = sPlate.remove(0, 2);
        flag = true;
    } else {
        if (sPlate.startsWith("蓝") || sPlate.startsWith("黄") ||  // sPlate.startsWith("黑")||
            sPlate.startsWith("白") || sPlate.startsWith("绿") || sPlate.startsWith("红")) {
            scolor = sPlate.left(1);
            sPlate = sPlate.remove(0, 1);
            flag = true;
        }
    }

    if (flag) {
        QByteArray ba = UnicodetoGB2312(sPlate);
        memset(pRlt->szVehPlate, 0, sizeof(pRlt->szVehPlate));
        memcpy(pRlt->szVehPlate, ba.constData(), ba.size());
        bColor = TransVehPlateColor(scolor);
    } else {
        memcpy(pRlt->szVehPlate, szVehPlate, sizeof(pRlt->szVehPlate));
    }

    pRlt->nDevIndex = m_nDevIndex;

    pRlt->nIndex = m_nCurIndex;

    //不知道如何从无符号字符数组转为int
    if (0xff == bColor) {
        pRlt->nColor = scolor.toInt();
    } else {
        pRlt->nColor = bColor;
    }

    pRlt->nBigImgSize = pVlpResult->nImageLength[0];
    pRlt->nSmallImgSize = pVlpResult->nImageLength[1];
    pRlt->nBinImgSize = pVlpResult->nImageLength[2];
    DebugLog(QString("转换车牌识别:%1,%2, BigImage Size:%3 SmallIage Size:%4, BinImage Size: %5")
                 .arg(GB2312toUnicode(pRlt->szVehPlate))
                 .arg(pRlt->nColor)
                 .arg(pRlt->nBigImgSize)
                 .arg(pRlt->nSmallImgSize)
                 .arg(pRlt->nBinImgSize));
    pRlt->sBigName = QString("bigimage.jpg");
    if (pVlpResult->sImage[0])
        WriteBufToFile(pRlt->sBigName.toLatin1().constData(), pVlpResult->sImage[0],
                       pVlpResult->nImageLength[0]);
    pRlt->sSmallName = QString("smallimage.jpg");
    if (pVlpResult->sImage[1])
        WriteBufToFile(pRlt->sSmallName.toLatin1().constData(), pVlpResult->sImage[1],
                       pVlpResult->nImageLength[1]);
    pRlt->sBinName = QString("binimage.jpg");
    if (pVlpResult->sImage[2])
        WriteBufToFile(pRlt->sBinName.toLatin1().constData(), pVlpResult->sImage[2],
                       pVlpResult->nImageLength[2]);
    // 车型相关信息
    memcpy(pRlt->VehClass, &pVlpResult->nVlpClass, sizeof pRlt->VehClass);
    memcpy(pRlt->nVCRating, &pVlpResult->VlpReliability, sizeof pRlt->nVCRating);
    this->sendMsg(pRlt);  // 触发信号
    return true;
}

// 保存当前车型识别结果
bool CVPRDev::SaveVPRResult(int nIndex)
{
    if ((!m_bDriverLoaded) && (!m_bDriverLoadedStd)) {
        return false;
    }

    // 检查参数合法性防止数组越界
    if (0 > nIndex) {
        return false;
    }

    int n = nIndex % VPRResArraySize;
    CVPRResult *pRlt = m_VCRResultArray + n;
    pRlt->ClearResult();

    CAllVehInfo vehInfo;
    memset(&vehInfo, 0, sizeof vehInfo);
    char szVehPlate[MAX_VEHPLATE_LEN + 1] = "";
    vehInfo.lpszPlate = szVehPlate;
    // vehInfo.lpBigImgBuf = pRlt->lpBigImgBuf;
    vehInfo.nBigImgSize = BIG_IMG_BUF_SIZE;
    // vehInfo.lpSmallImgBuf = pRlt->lpSmallImgBuf;
    vehInfo.nSmallImgSize = SMALL_IMG_BUF_SIZE;
    //  vehInfo.lpBinImgBuf = pRlt->lpBinImgBuf;
    vehInfo.nBinImgSize = BIN_IMG_BUF_SIZE;

    if (VPR_GetAllVehInfo) {
        int nRet = VPR_GetAllVehInfo(nIndex, (CAutoDetectedVehInfo *)&vehInfo);
        if (0 != nRet) {
            ErrorLog(QString("VPR_GetAllVehInfo调用失败，ErrorCode=%1").arg(nRet));
            return false;
        }
    } else
        return false;

    RemovePlateSpecChar(szVehPlate, sizeof szVehPlate, pRlt->szVehPlate, MAX_VEHPLATE_LEN + 1);

    QString sPlate = GB2312toUnicode(pRlt->szVehPlate);
    memset(pRlt->szVehPlate, 0, sizeof pRlt->szVehPlate);
    if (sPlate == QString("无车牌")) {
    } else {
        memcpy(pRlt->szVehPlate, szVehPlate, 12);
    }

    pRlt->nIndex = nIndex;
    pRlt->nColor = vehInfo.nColor;
    pRlt->nRating = vehInfo.nRating;
    pRlt->nBigImgSize = vehInfo.nBigImgSize;
    pRlt->nSmallImgSize = vehInfo.nSmallImgSize;
    pRlt->nBinImgSize = vehInfo.nBinImgSize;
    pRlt->nBinImgWidth = vehInfo.nBinImgWidth;
    pRlt->nBinImgHeight = vehInfo.nBinImgHeight;
    m_nCurIndex = nIndex;
    // 车型相关信息
    memcpy(pRlt->VehClass, vehInfo.VehClass, sizeof pRlt->VehClass);
    memcpy(pRlt->nVCRating, vehInfo.nVCRating, sizeof pRlt->nVCRating);
    this->sendMsg(pRlt);  // 触发信号
    return true;
}

// 取车辆识别结果----由ID号获取
CVPRResult *CVPRDev::GetVPRResult(int nIndex)
{
    if (0 > nIndex) {
        return NULL;
    }

    int n = nIndex % VPRResArraySize;
    CVPRResult *pRlt = (CVPRResult *)(m_VCRResultArray + n);
    if (nIndex == pRlt->nIndex) {
        return pRlt;
    } else {
        return NULL;
    }
}

// 取当前车牌抓拍结果----当前车辆
CVPRResult *CVPRDev::GetCurVPRResult() { return GetVPRResult(m_nCurIndex); }

// 清除车型识别结果
void CVPRDev::ClearResult() { m_nCurIndex = -1; }

bool CVPRDev::GetVersion(char *lpszVersion, char *lpszVender)
{
    if (m_bStdVPR) {
        if (NULL == VLPR_GetHWVersion) return false;
        int lver = 30;
        // typedef int (WINAPI *Func_VLPR_GetHWVersion)(int nHandle,char* sVersion,int
        // nVersionLen,char* sAPIVersion,int nAPIVersionLen);
        int nRet = VLPR_GetHWVersion(m_nHandle, lpszVersion, lver, lpszVender, lver);
        return (0 == nRet);
    } else {
        if (NULL == VPR_GetVer) {
            return false;
        }
        int nRet = VPR_GetVer(lpszVersion, lpszVender, NULL);
        return (0 == nRet);
    }
}

// 将抓拍大图保存为文件
bool CVPRDev::GetBigImage(quint32 nIndex, const char *lpszImgName)
{
    if (!m_bDriverLoaded) return false;

    if (NULL == VPR_GetBigImg) {
        return false;
    }

    unsigned nBuffLen = BIG_IMG_BUF_SIZE;
    quint8 *pcBuff = new quint8[nBuffLen];
    memset(pcBuff, 0, nBuffLen);
    int nRet = VPR_GetBigImg(nIndex, pcBuff, nBuffLen);
    if (0 != nRet) {
        // QMessageBox::information(NULL,"警告",QString("GetBigImg调用失败，ErrorCode=%1,
        // BuffLen=%2").arg(nRet).arg(nBuffLen),QMessageBox::Yes,QMessageBox::Yes);
        ErrorLog(QString("GetBigImg调用失败，ErrorCode=%1, BuffLen=%2").arg(nRet).arg(nBuffLen));
        delete[] pcBuff;
        return false;
    }
    bool bRet = WriteBufToFile(lpszImgName, pcBuff, nBuffLen);
    delete[] pcBuff;
    return bRet;
}

// 将抓拍小图保存为文件
bool CVPRDev::GetSmallImage(quint32 nIndex, const char *lpszImgName)
{
    if (!m_bDriverLoaded || NULL == VPR_GetSmallImg) {
        return false;
    }

    unsigned nBuffLen = SMALL_IMG_BUF_SIZE;
    unsigned char *pcBuff = new unsigned char[nBuffLen];
    int nRet = VPR_GetSmallImg(nIndex, pcBuff, nBuffLen);
    if (0 != nRet) {
        // QMessageBox::information(NULL,"警告",QString("GetSmallImg调用失败，ErrorCode=%1，BuffLen=%2").arg(nRet).arg(nBuffLen),QMessageBox::Yes,QMessageBox::Yes);
        ErrorLog(QString("GetSmallImg调用失败，ErrorCode=%1，BuffLen=%2").arg(nRet).arg(nBuffLen));
        delete[] pcBuff;
        return false;
    }
    bool bRlt = WriteBufToFile(lpszImgName, pcBuff, nBuffLen);
    delete[] pcBuff;
    return bRlt;
}

// 取二值化图像
bool CVPRDev::GetBinImage(quint32 nIndex, const char *pszImgName, quint32 &nWidth,
                          quint32 &pnHeight)
{
    if (!m_bDriverLoaded || NULL == VPR_GetBinImg) {
        return false;
    }

    unsigned nBuffLen = BIN_IMG_BUF_SIZE;
    unsigned char *pcBuff = new unsigned char[nBuffLen];
    int nRet = VPR_GetBinImg(nIndex, pcBuff, nBuffLen, nWidth, pnHeight);
    if (0 != nRet) {
        // 记录失败日志
        delete[] pcBuff;
        return false;
    }
    bool bRlt = WriteBufToFile(pszImgName, pcBuff, nBuffLen);
    delete[] pcBuff;
    return bRlt;
}

// 获取车牌识别结果，当收到车型识别自定义消息时使用，或在回调函数中调用。
int CVPRDev::GetVehClass(quint32 nIndex, qint32 &nVehClass, quint32 &nRating)
{
    if (NULL == VR_GetVehClass) {
        return -1;
    }
    int nRet = VR_GetVehClass(nIndex, nVehClass, nRating);
    if (0 != nRet) {
        // QMessageBox::information(NULL,"警告",QString("GetVehClass调用失败，ErrorCode=%1").arg(nRet),QMessageBox::Yes,QMessageBox::Yes);
        ErrorLog(QString("GetVehClass调用失败，ErrorCode=%1").arg(nRet));
        return nRet;
    }
    return nRet;
}

// 判断GetSglVCByPlate是否加载成功，仅供测试程序使用
bool CVPRDev::IsGetSglVCByPlateLoaded() { return NULL != VR_GetSglVCByPlate; }

bool CVPRDev::GetSglVCByPlate(quint32 nColor, char *lpszVehPlate)
{
    if (NULL == VR_GetSglVCByPlate) {
        return false;
    }

    int nRet = VR_GetSglVCByPlate(nColor, lpszVehPlate);
    if (0 != nRet) {
        // QMessageBox::information(NULL,"警告",QString("VR_GetSglVCByPlate调用失败，ErrorCode =
        // %1").arg(nRet),QMessageBox::Yes,QMessageBox::Yes);
        ErrorLog(QString("VR_GetSglVCByPlate调用失败，ErrorCode = %1").arg(nRet));
    }

    return 0 == nRet;
}

void CVPRDev::SetResultfCallBack(CBFunc_GetRegResult pGetRegResult, CBFunc_GetStatus pGetStatus)
{
    if (VLPR_SetResultCallBack) {
        DebugLog("注册车牌识别函数");
        VLPR_SetResultCallBack(m_nHandle, pGetRegResult, this);
    }
    if (VLPR_SetStatusCallBack) {
        DebugLog("注册车牌识别状态函数");
        VLPR_SetStatusCallBack(m_nHandle, 1 * 60 * 1000, pGetStatus, this);
    }
}

void CVPRDev::sendMsg(CVPRResult *pRlt) { emit NotifyVPRDev(pRlt); }
