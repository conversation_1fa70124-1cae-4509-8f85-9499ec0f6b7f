#include "uovertimedlg.h"

CUOverTimeDlg::CUOverTimeDlg(QWidget *parent) : CBaseOpWidget(parent)
{
    m_nOverTimeInFen = 0;
    m_pLabelName = NULL;
    m_pLineEdit = NULL;
    m_pLabelTitle = NULL;
    m_pLabelTime = NULL;
    m_pLabelTip = NULL;
    Init();

    filterChildrenKeyEvent();
}

CUOverTimeDlg::~CUOverTimeDlg()
{
    m_nOverTimeInFen = 0;
    if (m_pLabelName) {
        delete m_pLabelName;
        m_pLabelName = NULL;
    }
    if (m_pLabelTitle) {
        delete m_pLabelTitle;
        m_pLabelTitle = NULL;
    }
    if (m_pLabelTime) {
        delete m_pLabelTime;
        m_pLabelTime = NULL;
    }
    if (m_pLabelTip) {
        delete m_pLabelTip;
        m_pLabelTip = NULL;
    }
    if (m_pLineEdit) {
        delete m_pLineEdit;
        m_pLineEdit = NULL;
    }
}

void CUOverTimeDlg::closeEvent(QCloseEvent *event) { event->ignore(); }

void CUOverTimeDlg::Init()
{
    CBaseOpWidget::InitUI();

    QFont ftText;
    ftText.setFamily(QString::fromUtf8("微软雅黑"));
    ftText.setPixelSize(30);
    ftText.setStyleStrategy(QFont::PreferAntialias);

    if (NULL == m_pLabelTitle) m_pLabelTitle = new QLabel(this);
    m_pLabelTitle->setGeometry(0, 20, this->width() - 5, 60);
    m_pLabelTitle->setAlignment(Qt::AlignCenter);
    m_pLabelTitle->setStyleSheet("QLabel{color: rgb(30, 30, 30)}");
    m_pLabelTitle->setFont(ftText);
    m_pLabelTitle->setText("请输入U型行驶时间");

    ftText.setPixelSize(23);
    if (NULL == m_pLabelName) m_pLabelName = new QLabel(this);
    m_pLabelName->setGeometry(60, 140, 100, 40);
    m_pLabelName->setAlignment(Qt::AlignLeft);
    m_pLabelName->setStyleSheet("QLabel{color: rgb(30, 30, 30);}");
    m_pLabelName->setFont(ftText);
    m_pLabelName->setText("行驶时间");

    if (NULL == m_pLineEdit) m_pLineEdit = new CNumLineEdit(Type_Num, 5, this);
    m_pLineEdit->setGeometry(180, 138, 150, 40);
    m_pLineEdit->setAlignment(Qt::AlignLeft);
    m_pLineEdit->setFont(ftText);
    m_pLineEdit->setFocus();

    if (NULL == m_pLabelTime) m_pLabelTime = new QLabel(this);
    m_pLabelTime->setGeometry(340, 140, 50, 40);
    m_pLabelTime->setAlignment(Qt::AlignLeft);
    m_pLabelTime->setStyleSheet("QLabel{color: rgb(30, 30, 30);}");
    m_pLabelTime->setFont(ftText);
    m_pLabelTime->setText("分钟");

    if (NULL == m_pLabelTip) m_pLabelTip = new QLabel(this);
    m_pLabelTip->setGeometry(60, 200, 460, 40);
    m_pLabelTip->setAlignment(Qt::AlignLeft);
    m_pLabelTip->setStyleSheet("QLabel{color: rgb(30, 30, 30);}");
    m_pLabelTip->setFont(ftText);
    m_pLabelTip->setText("U型入口时间: ");

    CBaseOpWidget::SetMessage(QString("请输入U型车行驶时间，按【确定】键确认"));
}

quint64 CUOverTimeDlg::GetOverTime()
{
    if (m_nOverTimeInFen < 0) m_nOverTimeInFen = 0;
    return m_nOverTimeInFen;
}

void CUOverTimeDlg::SetEnTimeText()
{
    time_t unEnTime;
    int nPassTime;
    quint64 nOverTime = m_pLineEdit->text().toLong();
    if (nOverTime > 0) {
        time_t curTime = QDateTime::currentDateTime().toTime_t();
        time_t enTime = curTime - nOverTime * 60;
        unEnTime = enTime;
        nPassTime = nOverTime * 60;
    } else {
        unEnTime = QDateTime(QDate(2000, 1, 1), QTime(0, 0, 0)).toTime_t();
    }
    QDateTime EnTime;
    OldCardTime2QDateTime(unEnTime, EnTime);
    QString sEnTime = EnTime.toString("yyyy-MM-dd hh:mm:ss");

    if (NULL == m_pLineEdit || NULL == m_pLabelTip) return;

    //设置入口时间
    QString sText;
    if (m_pLabelTip) {
        sText = QString("U型入口时间: ") + sEnTime;
    }
    m_pLabelTip->setText(sText);
}

// 换上发票、修改票号处理
int CUOverTimeDlg::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    mtcKeyEvent->setKeyType(KC_Func);
    if (mtcKeyEvent->isFuncKey() && mtcKeyEvent->func() == KeyConfirm) {  // 确定键
        if (NULL == m_pLineEdit) return 0;
        m_nOverTimeInFen = m_pLineEdit->text().toLong();
        this->setModalResult(1);
    }

    if (mtcKeyEvent->isNumKey()) {  // 数字
        mtcKeyEvent->setKeyType(KC_Number);
        m_pLineEdit->SetLineEditText(mtcKeyEvent);
        SetEnTimeText();
    }

    if (mtcKeyEvent->isFuncKey() && mtcKeyEvent->func() == KeyDel) {  // 删除键
        m_pLineEdit->DeleteLineEditText();
        SetEnTimeText();
    }

    if (mtcKeyEvent->isFuncKey() && mtcKeyEvent->func() == KeyEsc) {  // 退出键
        if (!m_bAllowCancel) return 1;
        m_nOverTimeInFen = 0;
        this->setModalResult(0);
    }
    return 1;
}
