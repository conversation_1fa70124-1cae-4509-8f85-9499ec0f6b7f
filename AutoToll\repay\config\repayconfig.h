#ifndef REPAYCONFIG_H
#define REPAYCONFIG_H

#include <QObject>
#include <QSettings>
#include <QMutex>
#include <QString>
#include <QMap>
#include <QVariant>

class RepayConfig : public QObject
{
    Q_OBJECT
    
public:
    static RepayConfig* GetInstance();
    
    // 初始化配置
    bool Initialize();
    
    // 重新加载配置
    void ReloadConfig();
    
    // 省中心接口配置
    QString GetBaseUrl() const { return m_sBaseUrl; }
    int GetTimeoutMs() const { return m_nTimeoutMs; }
    int GetRetryTimes() const { return m_nRetryTimes; }
    bool IsAsyncEnabled() const { return m_bEnableAsync; }
    
    // 查询方式枚举
    enum MaxFeeQueryType {
        QueryType_Config = 0,      // 配置文件查询
        QueryType_SpParaTable = 1, // SpParaTable查询
        QueryType_Default = 2      // 默认值查询
    };
    
    // 补费限额配置（方案1：整合现有MaxFee系统）
    int GetMaxFee(int vehType, MaxFeeQueryType queryType = QueryType_SpParaTable) const;  // 根据查询方式获取最大费额
    int GetDefaultMaxFee(int vehType) const;    // 获取硬编码默认值
    int GetGlobalMaxFee() const;                // 获取全局补费上限
    int GetDailyMaxAmount() const;              // 获取单日补费上限
    
    // 授权配置
    bool IsRequireAuth() const { return m_bRequireAuth; }
    int GetMaxRetryTimes() const { return m_nMaxRetryTimes; }
    int GetAuthTimeoutSecs() const { return m_nAuthTimeoutSecs; }
    
    // 注意：日志功能使用项目已有的日志系统，不再单独配置
    
    // 界面配置
    bool IsShowDebtDetail() const { return m_bShowDebtDetail; }
    int GetAutoRefreshInterval() const { return m_nAutoRefreshInterval; }
    int GetMaxDetailItems() const { return m_nMaxDetailItems; }
    
    // 支付配置
    int GetPaymentTimeoutSecs() const { return 60; }  // 默认60秒
    
    // 功能切换配置
    bool IsUseNewRepayFunction() const { return m_bUseNewRepayFunction; }
    bool IsAllowRuntimeSwitch() const { return m_bAllowRuntimeSwitch; }
    bool IsSwitchNotificationEnabled() const { return m_bSwitchNotificationEnabled; }
    
    // 兼容性配置
    bool IsLegacyMenuStyle() const { return m_bLegacyMenuStyle; }
    bool IsShowFunctionSwitchPrompt() const { return m_bShowFunctionSwitchPrompt; }
    bool IsEnableHotSwitch() const { return m_bEnableHotSwitch; }
    
    // 设置配置项（运行时修改）
    bool SetUseNewRepayFunction(bool useNew);
    bool SetAllowRuntimeSwitch(bool allow);
    bool SetSwitchNotificationEnabled(bool enabled);
    
    // 配置验证和管理
    bool ValidateMaxFeeConfig() const;                    // 验证补费限额配置合理性
    QMap<QString, QVariant> GetAllConfigs() const;       // 获取所有配置项

signals:
    void ConfigChanged(const QString &section, const QString &key, const QVariant &value);

private:
    RepayConfig(QObject *parent = 0);
    void LoadConfig();
    void SaveConfig();
    
    // 私有辅助方法
    int GetMaxFeeFromSpParaTable(int vehType) const;      // 从特殊参数表查询MaxFee
    
private:
    static RepayConfig *m_pInstance;
    static QMutex m_mutex;
    
    QSettings *m_pSettings;
    
    // 省中心接口配置
    QString m_sBaseUrl;
    int m_nTimeoutMs;
    int m_nRetryTimes;
    bool m_bEnableAsync;
    
    // 补费限额配置
    QMap<int, int> m_mapMaxFee;
    
    // 授权配置
    bool m_bRequireAuth;
    int m_nMaxRetryTimes;
    int m_nAuthTimeoutSecs;
    
    // 界面配置
    bool m_bShowDebtDetail;
    int m_nAutoRefreshInterval;
    int m_nMaxDetailItems;
    
    // 功能切换配置
    bool m_bUseNewRepayFunction;
    bool m_bAllowRuntimeSwitch;
    bool m_bSwitchNotificationEnabled;
    
    // 兼容性配置
    bool m_bLegacyMenuStyle;
    bool m_bShowFunctionSwitchPrompt;
    bool m_bEnableHotSwitch;
};

#endif // REPAYCONFIG_H