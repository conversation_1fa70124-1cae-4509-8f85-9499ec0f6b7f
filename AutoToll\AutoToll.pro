#-------------------------------------------------
#
# Project created by QtC<PERSON> 2018-05-23T10:22:00 1
#
#-------------------------------------------------

QT       += core gui
QT       += network
QT       += sql
QT       += multimedia
QT       += phonon
CONFIG += c++11
CONFIG += debug_and_release
QMAKE_CXXFLAGS += -Wno-unused-parameter

DEFINES += CALC_FEE_ADD
DEFINES += USELIBCURL
DEFINES += E_PAPER
#DEFINES += CHECKAXIS_11

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

#TARGET = AutoToll

CONFIG(debug, debug|release) {
    TARGET = AutoToll_debug
    OBJECTS_DIR +=../obj_debug/AutoToll
}else{
    TARGET = AutoToll
    OBJECTS_DIR +=../obj_release/AutoToll
    #win32:QMAKE_POST_LINK += "$$PWD/win32.bat"
}
TEMPLATE = app

#QMAKE_CXXFLAGS_RELEASE = $$QMAKE_CFLAGS_RELEASE_WITH_DEBUGINFO
#QMAKE_LFLAGS_RELEASE = $$QMAKE_LFLAGS_RELEASE_WITH_DEBUGINFO

MOC_DIR +=../moc

UI_DIR +=../form
win32:DESTDIR +=../bin
unix:DESTDIR +=../bin-linux
INCLUDEPATH += . \
                ../common \
                ../log4qt \
                ../cryptlib \
                ../devices \
                ../comport \
                ../protofiles

include  (../log4qt/log4qt.pri)
include  (../qtsingleapplication/qtsingleapplication.pri)
include (../qextserialport/qextserialport.pri)
include  (../cryptlib/cryptlib.pri)
include (../MtcKey/MtcKey.pri)
include (../common/common.pri)
include (../devices/devices.pri)
include(../WebService/webservice.pri)
include (../comport/comport.pri)
include (../protofiles/protofiles.pri)
include (../libqrencode-4.1.1/qrencode.pri)
include ($$PWD/../httpserver/httpserver.pri)

unix:include  (../quazip-0.7.1/quazip/quazip.pri)
#win32: LIBS += -L$$PWD -lquazip

SOURCES += main.cpp\
        dlgmain.cpp \
    formmobilepaybase.cpp \
    formmobilepaytwdev.cpp \
    remotecontrolmgr.cpp \
        ui_splashscreen.cpp \
        baseetcdialog.cpp \
        fancylineedit.cpp \
        fancybutton.cpp \
        loginuser.cpp \
        baseopwidget.cpp \
        authfm.cpp    \
        abstractstate.cpp \
        paraminfodlg.cpp \
        lanestate_vehinput.cpp \
        lanestate_unlogin.cpp \
        lanestate_waitvehpass.cpp \
        laneinfo.cpp \
        funcfm.cpp \
        myapplication.cpp \
        messagedialog.cpp \
        listdlg.cpp \
        funcmenu.cpp \
        localdatamgr.cpp \
        sysparamdlg.cpp \
        datasendmgr.cpp \
        messagebox.cpp \
        peerthread.cpp \
        laneserver.cpp \
        utypereducefm.cpp \
        sernumfm.cpp \
        sernum.cpp \
        confirmdlg.cpp \
        devicefactory.cpp \
        fdandsoundevent.cpp \
        lanestate_motorcade.cpp \
        soundplayer.cpp \
        motorcadefm.cpp \
        update.cpp \
        creversedlg.cpp \
        uovertimedlg.cpp \
        transinfo.cpp \
        rsuframemgr.cpp \
        unloginopverifyfm.cpp \
    vscrolllabel.cpp \
    CalcDibsForm.cpp \
    httpreqparams.cpp \
    httpupload.cpp \
    etclanectrl.cpp \
    speventmgr.cpp \
    tollgantrymgr.cpp \
    cpsamdlg.cpp \
    lanestate_psamauth.cpp \
    csysinfo.cpp \
    stdlog.cpp \
    laneconfigdlg.cpp \
    rsudataprocessor.cpp \
    globalui.cpp \
    formvehimage.cpp \
    formdevstatus.cpp \
    formlaneinfo.cpp \
    formvideocard.cpp \
    formlog.cpp \
    formparamversion.cpp \
    formetctradeinfo.cpp \
    formfaredisplayer.cpp \
    formhistory.cpp \
    formvehqueue.cpp \
    formpromptmsg.cpp \
    formunlogin.cpp \
    formlogin.cpp \
    formweight.cpp \
    formvehinfo.cpp \
    forminputaxistype.cpp \
    forminputweight.cpp \
    forminputsplitweight.cpp \
    forminputlimit.cpp \
    formcanopylight.cpp \
    formshowimage.cpp \
    forminputplate.cpp \
    formconfirminvoice.cpp \
    forminputinvoiceno.cpp \
    formextrainfo.cpp \
    formvehmoney.cpp \
    lanestate_vehmoney.cpp \
    formselectstation.cpp \
    formqrcode.cpp \
    qrcode.cpp \
    lanestate_vehinputentry.cpp \
    lanestate_vehinputexit.cpp \
    cfarecalcunit.cpp \
    forminputcardno.cpp \
    forminputenstationid.cpp \
    formloading.cpp \
    formmobilepay.cpp \
    formetcpay.cpp \
    formreverse.cpp \
    forminputbigtruck.cpp \
    formselectbigveh.cpp \
    remoteserver.cpp \
    remoteclient.cpp \
    remotemsgmgr.cpp \
    formrepay.cpp \
    lanestate_etcstate.cpp \
    papercardmgr.cpp \
    vehtypelibmgr.cpp \
    vipumgr.cpp \
    clearfilemgr.cpp \
    etcdelay.cpp \
    prepaymgr.cpp \
    etcprompt.cpp \
    prodebpayment.cpp \
    rsuinitworker.cpp

HEADERS  += dlgmain.h \
        baseetcdialog.h \
        baseopwidget.h \
        fancylineedit.h \
        fancybutton.h \
    formmobilepaybase.h \
    formmobilepaytwdev.h \
    remotecontrolmgr.h \
    remotemsgdefine.h \
        ui_splashscreen.h \
        loginuser.h \
        authfm.h    \
        abstractstate.h \
        paraminfodlg.h \
        lanestate_vehinput.h \
        lanestate_unlogin.h \
        lanestate_waitvehpass.h \
        laneinfo.h \
        funcfm.h \
        myapplication.h \
        messagedialog.h \
        listdlg.h \
        funcmenu.h \
        localdatamgr.h \
        sysparamdlg.h \
        datasendmgr.h \
        messagebox.h \
        peerthread.h \
        laneserver.h \
        sernumfm.h \
        sernum.h \
        utypereducefm.h\
        confirmdlg.h \
        devicefactory.h \
        fdandsoundevent.h \
        lanestate_motorcade.h \
        soundplayer.h \
        motorcadefm.h \
        update.h \
        creversedlg.h \
        uovertimedlg.h\
        transinfo.h\
        speventmgr.h \
        rsuframemgr.h \
        unloginopverifyfm.h \
    vscrolllabel.h \
    CalcDibsForm.h \
    httpreqparams.h \
    httpupload.h \
    etclanectrl.h \
    tollgantrymgr.h \
    cpsamdlg.h \
    lanestate_psamauth.h \
    csysinfo.h \
    stdlog.h \
    laneconfigdlg.h \
    rsudataprocessor.h \
    globalui.h \
    device.h \
    formvehimage.h \
    formdevstatus.h \
    formlaneinfo.h \
    formvideocard.h \
    formlog.h \
    formparamversion.h \
    formetctradeinfo.h \
    formfaredisplayer.h \
    formhistory.h \
    formvehqueue.h \
    formpromptmsg.h \
    formunlogin.h \
    formlogin.h \
    formweight.h \
    formvehinfo.h \
    forminputaxistype.h \
    forminputweight.h \
    forminputsplitweight.h \
    forminputlimit.h \
    formcanopylight.h \
    formshowimage.h \
    forminputplate.h \
    forminputinvoiceno.h \
    formconfirminvoice.h \
    formextrainfo.h \
    formvehmoney.h \
    lanestate_vehmoney.h \
    formselectstation.h \
    formqrcode.h \
    qrcode.h \
    lanestate_vehinputentry.h \
    lanestate_vehinputexit.h \
    cfarecalcunit.h \
    cfarecalctypes.h \
    forminputcardno.h \
    forminputenstationid.h \
    formloading.h \
    formmobilepay.h \
    formetcpay.h \
    formreverse.h \
    forminputbigtruck.h \
    formselectbigveh.h \
    remoteserver.h \
    remoteclient.h \
    remotemsgmgr.h \
    formrepay.h \
    lanestate_etcstate.h \
    papercardmgr.h \
    vehtypelibmgr.h \
    vipumgr.h \
    clearfilemgr.h \
    etcdelay.h \
    prepaymgr.h \
    etcprompt.h \
    prodebpayment.h \
    rsuinitworker.h



FORMS    += \
        logindlg.ui \
        loginuser.ui \
        funcfm.ui \
        listdlg.ui \
        sernumfm.ui \
        confirmdlg.ui \
        platechangefm.ui \
        unloginopverifyfm.ui \
    CalcDibsForm.ui \
    cpsamdlg.ui \
    claneconfigdlg.ui \
    formhistory.ui \
    formunlogin.ui


CONFIG += exceptions

RESOURCES += \
    images/images.qrc

unix{
    LIBS += -L$$PWD/../bin-linux/lib -lcurl -lz -lssl3 -lcrypto -lm
    QMAKE_LFLAGS += -Wl,-rpath,\'\$\$ORIGIN/lib/\'
    DEFINES += WINAPI
}

#为了二维码动态库产生的gdi对象删除引入
win32: LIBS += -lgdi32

win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../quazip-0.7.1/lib/ -lquazip
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../quazip-0.7.1/lib/ -lquazipd

INCLUDEPATH += $$PWD/../quazip-0.7.1/quazip
DEPENDPATH += $$PWD/../quazip-0.7.1/quazip
INCLUDEPATH += $$PWD/../zlib1.2.8/include

#openssl
INCLUDEPATH += ../include
win32: LIBS +=$$quote($$PWD/../OpenSSL-Win32/libcrypto.lib)
win32: LIBS +=$$quote($$PWD/../OpenSSL-Win32/libssl.lib)

#libcurl
INCLUDEPATH += $$quote($$PWD/../curl/include/)
LIBS +=$$quote($$PWD/../curl/lib/libcurldll.a)

INCLUDEPATH += $$quote($$PWD/../GmSSL 3.1.1/include)
LIBS +=$$quote($$PWD/../GmSSL 3.1.1/lib-32/gmssl.lib)

RC_FILE = logo.rc

DISTFILES += \
    ../protofiles/protofiles.pri

# 添加Windows API版本定义，确保兼容Windows 7
DEFINES += WINVER=0x0601 _WIN32_WINNT=0x0601
