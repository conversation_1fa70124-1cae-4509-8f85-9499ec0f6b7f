#include "repayconfig.h"
#include <QSettings>
#include <QMutexLocker>
#include <QCoreApplication>
#include <QFileSystemWatcher>
#include "../../common/BaseParamFile.h"
#include "../../common/parafileold.h"
#include "../../common/paramfilemgr.h"  // CParamFileMgr类定义
#include "../../common/lanetype.h"      // cfSpPara枚举定义
#include "../../log4qt/ilogmsg.h"  // 使用项目已有的log4qt日志系统

RepayConfig* RepayConfig::m_pInstance = 0;
QMutex RepayConfig::m_mutex;

RepayConfig::RepayConfig(QObject *parent)
    : QObject(parent)
    , m_pSettings(0)
    , m_nTimeoutMs(10000)
    , m_nRetryTimes(3)
    , m_bEnableAsync(true)
    , m_bRequireAuth(true)
    , m_nMaxRetryTimes(3)
    , m_nAuthTimeoutSecs(300)
    , m_bShowDebtDetail(true)
    , m_nAutoRefreshInterval(5)
    , m_nMaxDetailItems(100)
    , m_bUseNewRepayFunction(true)
    , m_bAllowRuntimeSwitch(true)
    , m_bSwitchNotificationEnabled(true)
    , m_bLegacyMenuStyle(false)
    , m_bShowFunctionSwitchPrompt(true)
    , m_bEnableHotSwitch(true)
{
}

RepayConfig* RepayConfig::GetInstance()
{
    if (m_pInstance == 0) {
        QMutexLocker locker(&m_mutex);
        if (m_pInstance == 0) {
            m_pInstance = new RepayConfig();
        }
    }
    return m_pInstance;
}

bool RepayConfig::Initialize()
{
    // 初始化配置文件
    QString configPath = QCoreApplication::applicationDirPath() + "/lane.ini";
    m_pSettings = new QSettings(configPath, QSettings::IniFormat, this);
    m_pSettings->setIniCodec("UTF-8");
    
    // 加载配置
    LoadConfig();
    
    DebugLog("补费配置初始化成功");
    return true;
}

void RepayConfig::LoadConfig()
{
    if (!m_pSettings) {
        DebugLog("配置对象未初始化");
        return;
    }
    
    // 省中心接口配置
    m_pSettings->beginGroup("ProDebtPayment");
    m_sBaseUrl = m_pSettings->value("BaseUrl", "http://127.0.0.1:8080/ps").toString();
    m_nTimeoutMs = m_pSettings->value("TimeoutMs", 10000).toInt();
    m_nRetryTimes = m_pSettings->value("RetryTimes", 3).toInt();
    m_bEnableAsync = m_pSettings->value("EnableAsync", true).toBool();
    m_pSettings->endGroup();
    
    // 补费限额配置 - 从RepayLimit段读取
    m_pSettings->beginGroup("RepayLimit");
    m_mapMaxFee.clear();
    
    // 加载各车型补费上限
    for (int vehType = 1; vehType <= 6; ++vehType) {
        QString key = QString("VehType%1_MaxFee").arg(vehType);
        if (m_pSettings->contains(key)) {
            int maxFee = m_pSettings->value(key, 0).toInt();
            if (maxFee > 0) {
                m_mapMaxFee.insert(vehType, maxFee);
                DebugLog(QString("加载车型%1补费限额：%2分").arg(vehType).arg(maxFee));
            }
        }
    }
    
    // 加载全局限额和单日限额
    int globalMaxFee = m_pSettings->value("GlobalMaxFee", 50000).toInt();
    int dailyMaxAmount = m_pSettings->value("DailyMaxAmount", 100000).toInt();
    bool useSpParaMaxFee = m_pSettings->value("UseSpParaMaxFee", true).toBool();
    
    if (globalMaxFee > 0) {
        m_mapMaxFee.insert(0, globalMaxFee); // 0表示全局限额
    }
    if (dailyMaxAmount > 0) {
        m_mapMaxFee.insert(-1, dailyMaxAmount); // -1表示单日限额
    }
    if (useSpParaMaxFee) {
        m_mapMaxFee.insert(-2, 1); // -2表示启用特殊参数MaxFee后备
    }
    
    m_pSettings->endGroup();
    
    // 授权配置
    m_pSettings->beginGroup("RepayAuth");
    m_bRequireAuth = m_pSettings->value("RequireAuth", true).toBool();
    m_nMaxRetryTimes = m_pSettings->value("MaxRetryTimes", 3).toInt();
    m_nAuthTimeoutSecs = m_pSettings->value("AuthTimeoutSecs", 300).toInt();
    m_pSettings->endGroup();
    
    // 注意：日志功能使用项目已有的日志系统，不再单独配置
    
    // 界面配置
    m_pSettings->beginGroup("RepayUI");
    m_bShowDebtDetail = m_pSettings->value("ShowDebtDetail", true).toBool();
    m_nAutoRefreshInterval = m_pSettings->value("AutoRefreshInterval", 5).toInt();
    m_nMaxDetailItems = m_pSettings->value("MaxDetailItems", 100).toInt();
    m_pSettings->endGroup();
    
    // 功能切换配置
    m_pSettings->beginGroup("RepaySystem");
    m_bUseNewRepayFunction = m_pSettings->value("UseNewRepayFunction", true).toBool();
    m_bAllowRuntimeSwitch = m_pSettings->value("AllowRuntimeSwitch", true).toBool();
    m_bSwitchNotificationEnabled = m_pSettings->value("SwitchNotificationEnabled", true).toBool();
    m_pSettings->endGroup();
    
    // 兼容性配置
    m_pSettings->beginGroup("RepayCompatibility");
    m_bLegacyMenuStyle = m_pSettings->value("LegacyMenuStyle", false).toBool();
    m_bShowFunctionSwitchPrompt = m_pSettings->value("ShowFunctionSwitchPrompt", true).toBool();
    m_bEnableHotSwitch = m_pSettings->value("EnableHotSwitch", true).toBool();
    m_pSettings->endGroup();
    
    DebugLog("补费配置加载成功");
}

void RepayConfig::ReloadConfig()
{
    if (m_pSettings) {
        m_pSettings->sync();
        LoadConfig();
        InfoLog("补费配置已重新加载");
    }
}

int RepayConfig::GetMaxFee(int vehType, MaxFeeQueryType queryType) const
{
    switch (queryType) {
        case QueryType_Config:
    {
            // 从RepayLimit配置文件查询
            if (m_mapMaxFee.contains(vehType)) {
                int configMaxFee = m_mapMaxFee.value(vehType);
                DebugLog(QString("从配置获取最大费额：车型=%1，限额=%2分").arg(vehType).arg(configMaxFee));
                return configMaxFee;
            }
            // 配置文件查询失败时，回退到默认值而非返回0
            WarnLog(QString("配置文件中未找到车型%1的最大费额配置，使用默认值").arg(vehType));
            int defaultMaxFee = GetDefaultMaxFee(vehType);
            DebugLog(QString("回退到默认最大费额：车型=%1，限额=%2分").arg(vehType).arg(defaultMaxFee));
            return defaultMaxFee;
    }
        case QueryType_SpParaTable:
            // 从SpParaTable查询（特殊参数106）
            {
                SpParaTable *pTable = (SpParaTable *)CParamFileMgr::GetParamFile(cfSpPara);
                if (pTable) {
                    quint32 nMaxFee = 0;
                    double baseFee = 0;
                    if (pTable->QryMaxFee(vehType, nMaxFee, baseFee)) {
                        DebugLog(QString("从SpParaTable获取最大费额：车型=%1，限额=%2分").arg(vehType).arg(nMaxFee));
                        return static_cast<int>(nMaxFee);
                    }
                }
                // SpParaTable查询失败时，回退到默认值而非返回0
                WarnLog(QString("SpParaTable中未找到车型%1的最大费额配置，使用默认值").arg(vehType));
                int defaultMaxFee = GetDefaultMaxFee(vehType);
                DebugLog(QString("回退到默认最大费额：车型=%1，限额=%2分").arg(vehType).arg(defaultMaxFee));
                return defaultMaxFee;
            }
            
        case QueryType_Default:
        default:
            // 硬编码默认值
            {
                int defaultMaxFee = GetDefaultMaxFee(vehType);
                DebugLog(QString("使用默认最大费额：车型=%1，限额=%2分").arg(vehType).arg(defaultMaxFee));
                return defaultMaxFee;
            }
    }
}

int RepayConfig::GetDefaultMaxFee(int vehType) const
{
    // 参考现有的CheckMaxFee逻辑
    switch (vehType) {
        case 1:  // VC_Car1 - 1类车
            return 10000;  // 100元
        case 2:
        case 3:
        case 4:
        case 5:
        case 6:
            return 20000;  // 200元
        case 11:  // VC_Truck1 - 货车1类
        case 12:  // VC_Truck2 - 货车2类
        case 13:  // VC_Truck3 - 货车3类
        case 14:  // VC_Truck4 - 货车4类
        case 15:  // VC_Truck5 - 货车5类
        case 16:  // VC_Truck6 - 货车6类
            return 30000;  // 300元
        case 21:  // VC_YJ1 - 一类专项作业车
        case 22:  // VC_YJ2 - 二类专项作业车
        case 23:  // VC_YJ3 - 三类专项作业车
        case 24:  // VC_YJ4 - 四类专项作业车
        case 25:  // VC_YJ5 - 五类专项作业车
        case 26:  // VC_YJ6 - 六类专项作业车
            return 30000;  // 300元，专项作业车按货车标准
        default:
            return 15000;  // 默认150元
    }
}

int RepayConfig::GetGlobalMaxFee() const
{
    // 查询全局补费上限
    if (m_mapMaxFee.contains(0)) {
        return m_mapMaxFee.value(0);
    }
    return 50000; // 默认500元
}

int RepayConfig::GetDailyMaxAmount() const
{
    // 查询单日补费上限
    if (m_mapMaxFee.contains(-1)) {
        return m_mapMaxFee.value(-1);
    }
    return 100000; // 默认1000元
}

bool RepayConfig::SetUseNewRepayFunction(bool useNew)
{
    if (!m_bAllowRuntimeSwitch) {
        WarnLog("运行时切换功能已禁用");
        return false;
    }
    
    bool oldValue = m_bUseNewRepayFunction;
    m_bUseNewRepayFunction = useNew;
    
    // 保存到配置文件
    SaveConfig();
    
    if (oldValue != useNew) {
        emit ConfigChanged("RepaySystem", "UseNewRepayFunction", useNew);
        InfoLog(QString("补费功能版本已切换：%1 → %2").arg(oldValue ? "新版" : "旧版").arg(useNew ? "新版" : "旧版"));
    }
    
    return true;
}

bool RepayConfig::SetAllowRuntimeSwitch(bool allow)
{
    bool oldValue = m_bAllowRuntimeSwitch;
    m_bAllowRuntimeSwitch = allow;
    
    SaveConfig();
    
    if (oldValue != allow) {
        emit ConfigChanged("RepaySystem", "AllowRuntimeSwitch", allow);
    }
    
    return true;
}

bool RepayConfig::SetSwitchNotificationEnabled(bool enabled)
{
    bool oldValue = m_bSwitchNotificationEnabled;
    m_bSwitchNotificationEnabled = enabled;
    
    SaveConfig();
    
    if (oldValue != enabled) {
        emit ConfigChanged("RepaySystem", "SwitchNotificationEnabled", enabled);
    }
    
    return true;
}

void RepayConfig::SaveConfig()
{
    if (!m_pSettings) {
        ErrorLog("配置未初始化");
        return;
    }
    
    // 保存功能切换配置
    m_pSettings->beginGroup("RepaySystem");
    m_pSettings->setValue("UseNewRepayFunction", m_bUseNewRepayFunction);
    m_pSettings->setValue("AllowRuntimeSwitch", m_bAllowRuntimeSwitch);
    m_pSettings->setValue("SwitchNotificationEnabled", m_bSwitchNotificationEnabled);
    m_pSettings->endGroup();
    
    m_pSettings->sync();
    InfoLog("补费配置已保存");
}

bool RepayConfig::ValidateMaxFeeConfig() const
{
    // 验证补费限额配置的合理性
    bool isValid = true;
    
    // 检查各车型限额是否合理
    for (int vehType = 1; vehType <= 6; ++vehType) {
        int maxFee = GetMaxFee(vehType);
        if (maxFee <= 0 || maxFee > 1000000) { // 限额应在0-10000元之间
            WarnLog(QString("车型%1最大费额无效：%2分").arg(vehType).arg(maxFee));
            isValid = false;
        }
    }
    
    // 检查全局限额
    int globalMaxFee = GetGlobalMaxFee();
    if (globalMaxFee <= 0) {
        WarnLog(QString("全局最大费额无效：%1分").arg(globalMaxFee));
        isValid = false;
    }
    
    return isValid;
}

QMap<QString, QVariant> RepayConfig::GetAllConfigs() const
{
    QMap<QString, QVariant> configs;
    
    // 省中心接口配置
    configs["ProDebtPayment.BaseUrl"] = m_sBaseUrl;
    configs["ProDebtPayment.TimeoutMs"] = m_nTimeoutMs;
    configs["ProDebtPayment.RetryTimes"] = m_nRetryTimes;
    configs["ProDebtPayment.EnableAsync"] = m_bEnableAsync;
    
    // 补费限额配置
    for (QMap<int, int>::const_iterator it = m_mapMaxFee.begin(); it != m_mapMaxFee.end(); ++it) {
        QString key = QString("RepayLimit.VehType%1_MaxFee").arg(it.key());
        configs[key] = it.value();
    }
    
    // 授权配置
    configs["RepayAuth.RequireAuth"] = m_bRequireAuth;
    configs["RepayAuth.MaxRetryTimes"] = m_nMaxRetryTimes;
    configs["RepayAuth.AuthTimeoutSecs"] = m_nAuthTimeoutSecs;
    
    // 界面配置
    configs["RepayUI.ShowDebtDetail"] = m_bShowDebtDetail;
    configs["RepayUI.AutoRefreshInterval"] = m_nAutoRefreshInterval;
    configs["RepayUI.MaxDetailItems"] = m_nMaxDetailItems;
    
    // 功能切换配置
    configs["RepaySystem.UseNewRepayFunction"] = m_bUseNewRepayFunction;
    configs["RepaySystem.AllowRuntimeSwitch"] = m_bAllowRuntimeSwitch;
    configs["RepaySystem.SwitchNotificationEnabled"] = m_bSwitchNotificationEnabled;
    
    return configs;
}
