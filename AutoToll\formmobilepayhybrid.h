#ifndef FORMOBILEPAYHYBRID_H
#define FORMOBILEPAYHYBRID_H

#include <QTimer>
#include <QWidget>
#include <QLibrary>

#include "formmobilepaybase.h"
#include "transinfo.h"

// DLL函数指针定义
typedef int (*IF_InitEnvironment)(HWND hWnd, unsigned int nThreadID, const char* pAreaInfo,
                                 const char* pLoaclStation, int nLaneID, int iProvinceID);
typedef int (*IF_Destroy)();
typedef int (*IF_DebitMoney)(const char* pDebitInfo, char* pRetInfo);
typedef int (*IF_DebitCancel)(const char* pDebitInfo);
typedef int (*IF_GetDebitResult)(char* pRetInfo);

// 支付结果数据结构
struct CPayResult
{
    qint32 ExecuteCode;        // 执行码
    qint32 ResultType;         // 结果类型
    QString ExecuteDesc;       // 执行描述
    quint32 ConsumeTime;       // 扣费时长
    QString DebitOrder;        // 订单号
    QString DebitTime;         // 扣费时间
    QString PayIdentifier;     // 交易识别码
    qint32 PayPlatformType;    // 支付平台类型
    QString TradeSecurityCode; // 交易安全码

    void Clear()
    {
        ExecuteCode = 0;
        ResultType = 0;
        ExecuteDesc.clear();
        ConsumeTime = 0;
        DebitOrder.clear();
        DebitTime.clear();
        PayIdentifier.clear();
        PayPlatformType = 0;
        TradeSecurityCode.clear();
    }

    CPayResult() { Clear(); }
};

// 与FormMobilePayTwDev中相同的结构体定义
struct CDebitParam_Debit
{
    qint32 Money;           // 扣费金额
    QString PayIdentifier;  // 识别码 guid 32位
    QString SubTime;        // 过车时间
    qint32 Type;            // 扣款业务类型 1：封闭路段出口扣款

    CDebitParam_Debit()
    {
        Money = 0;
        Type = 0;
    }
};

struct CDebitParam_Entry
{
    qint32 AreaID;        // 区域
    qint32 RoadID;        // 路段
    qint32 LaneID;        // 车道
    QString License;      // 入口车牌
    qint32 StationID;     // 入口站代码
    QString StationName;  // 入口站名称
    QString Time;         // 入口时间
    qint32 VClass;        // 入口车型
    qint32 VColor;        // 入口车牌颜色
    qint32 VType;         // 入口车种

    CDebitParam_Entry()
    {
        AreaID = 0;
        RoadID = 0;
        LaneID = 0;
        StationID = 0;
        VClass = 0;
        VColor = 0;
        VType = 0;
    }
};

struct CDebitParam_Operation
{
    QString CardID;              // 卡号
    qint32 PassCertificateType;  // 通行凭证类型
    qint32 ShiftID;              // 班次
    quint32 TollDate;            // 工班日
    QString TicketNo;            // 票号
    QString OperatorID;          // 操作员工号
    QString OperatorName;        // 操作员姓名

    CDebitParam_Operation()
    {
        PassCertificateType = 0;
        ShiftID = 0;
        TollDate = 0;
    }
};

struct CDebitParam_Vehicle
{
    qint32 AxisCount;     // 轴数
    qint32 Class;         // 车型
    QString License;      // 车牌
    qint32 Type;          // 车种
    qint32 VLColor;       // 颜色
    quint32 Weight;       // 重量
    quint32 LimitWeight;  // 限载

    CDebitParam_Vehicle()
    {
        AxisCount = 0;
        Class = 0;
        Type = 0;
        VLColor = 0;
        Weight = 0;
        LimitWeight = 0;
    }
};

struct CDebitParam_AutoPlate
{
    QString AutoLicense;  // 车牌号
    qint32 AutoColor;     // 车牌颜色

    CDebitParam_AutoPlate() { AutoColor = 0; }
};

/**
 * @brief The FormMobilePayHybrid class
 * 混合支付窗体，结合FormMobilePay的界面交互和FormMobilePayTwDev的收费逻辑
 */
class FormMobilePayHybrid : public FormMobilePayBase
{
    Q_OBJECT
public:
    explicit FormMobilePayHybrid(QWidget *parent = 0);
    ~FormMobilePayHybrid();

protected slots:
    void OnQryOrderTimer();
    void OnReverseTimer();
    void OnScanResultEvent(QString qrCode);
    void OnWaitPayTimer();
    void OnResultCallBack(qint32 option, qint32 result, QString sJson);
public slots:
    void OnRemoteStopPay();

protected:
    enum PayStep
    {
        PS_Waiting,        // 等待支付，未读到二维码，也未读到卡
        PS_Paying,         // 正在处理支付,等待结果
        PS_InputPassword,  // 等待用户输入密码状态
        PS_PaySuccess,     // 支付成功
        PS_PayFail         // 支付失败
    };

    // 支付状态
    PayStep m_payStep;
    // 支付倒计时
    int m_nWaitPaySec;
    QTimer m_WaitPayTimer;
    // 判断订单状态
    QTimer *m_pTmQryOrder;
    // 判断退款状态
    QTimer *m_pTmReverse;
    // 尝试次数
    quint32 m_nReverseTimes;
    // 最后支付方式
    CTransPayType m_payType;
    // 支付通道类型
    quint8 m_nChannelType;

    // 交易信息
    CPayResult m_payResult;
    QString m_sPayId;      // 支付标识
    CTransInfo *m_pCurTransInfo;

    // 外屏显示信息
    QString m_sExvlp;
    QString m_sExVehClassName;
    QString m_sExVehTypeName;
    QString m_sEnStation;

    // DLL相关
    static QLibrary m_hLibModule;
    static bool m_bDriverLoaded;
    bool m_bInited;

    // DLL函数指针
    static IF_InitEnvironment m_pIF_InitEnvironment;
    static IF_Destroy m_pIF_Destroy;
    static IF_DebitMoney m_pIF_DebitMoney;
    static IF_DebitCancel m_pIF_DebitCancel;
    static IF_GetDebitResult m_pIF_GetDebitResult;

    // 扣款参数
    CDebitParam_Debit m_debit;
    CDebitParam_Entry m_entry;
    CDebitParam_Operation m_operation;
    CDebitParam_Vehicle m_vehicle;
    CDebitParam_AutoPlate m_auPlate;

protected:
    void paintEvent(QPaintEvent *);
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);

    // 停止支付
    int OnStopPay();
    // 重新支付
    virtual bool BeginPay();
    // 预处理支付信息
    bool PreparePayInfo(CTransInfo *pTransInfo);

    // 当支付失败时
    void OnPayFail(QString sHelpMsg);
    // 当支付成功时
    void OnPaySuccess();

    // 退单操作
    bool ReverseOrder();
    bool ReverseOrderOnce();

    bool ProcessProCardEvent(CCardReader *pReader, int &nErrorCode, QString &sError, QString &sFDMsg);
    void DisplayErrorMsg(const QString &sError, const QString &sFDMsg);

    virtual bool OnOpenCardEvent(int nReadId, int nCardType);

    // 生成UUID
    QString GetUUID();

    // DLL相关
    bool LoadDriver();
    void ReleaseDriver();
    bool InitEnViroment(quint32 nThreadID, const QString &sAreaId, const QString &sStationId,
                       qint32 nLaneId, qint32 iProvinceID);
    void DestroyEnViroment();
    bool ProcessRespons_DebitResut(qint32 nResult, QVariant &varData, CPayResult &payResult);
    QString GetDebitFaildReason(qint32 nResult);

public:
    // 进行移动支付
    virtual bool Pay(CTransPayType &payType, PayClass payClass, CTransInfo *pTransInfo);
};

#endif // FORMOBILEPAYHYBRID_H
