#include "globalui.h"

#include <QApplication>
#include <QDesktopWidget>
#include <QSettings>

GlobalUI g_GlobalUI;

GlobalUI::GlobalUI() {}

//加载配置文件
bool GlobalUI::LoadCfg(const QString& name)
{
    QString sUIPath = QApplication::applicationDirPath();
    QString sconfigName = QString("%1/%2").arg(sUIPath).arg(name);
    QSettings uiConfig(sconfigName, QSettings::IniFormat);
    uiConfig.setIniCodec("GB2312");

    int nScreenIndex = uiConfig.value("Screen/ScreenIndex", 0).toInt();
    m_screenRect = QApplication::desktop()->screenGeometry(nScreenIndex);

    m_FontName = "微软雅黑";

    //主窗口位置
    QSize wndSize = uiConfig.value("MainWnd/wndSize", QSize(1280, 762)).toSize();
    m_RectMainWnd.setSize(wndSize);
    m_RectMainWnd.moveTo(m_screenRect.left() + (m_screenRect.width() - m_RectMainWnd.width()) / 2,
                         (m_screenRect.height() - m_RectMainWnd.height()) / 2);

    //标题栏
    main_RectTitle = uiConfig.value("MainWnd/titleRect", QRect(0, 0, 1280, 50)).toRect();
    //标题文字
    main_AppTitle = uiConfig.value("MainWnd/title", "高速公路智能自助收费系统").toString();

    //标题区域
    main_TitleTextRect = uiConfig.value("MainWnd/titleTextRect", QRect(0, 0, 1280, 50)).toRect();
    //时间区域
    main_TimeTextRect = uiConfig.value("MainWnd/timeTextRect", QRect(0, 0, 1280, 50)).toRect();
    // logo的大小
    main_RectLogo = uiConfig.value("MainWnd/logoRect", QRect()).toRect();
    //状态栏
    main_RectStatus = uiConfig.value("MainWnd/statusRect", QRect(0, 742, 1280, 20)).toRect();

    //标题字体
    main_TitleFontSize = uiConfig.value("MainWnd/titleFontSize", 24).toInt();
    //标题字间距
    main_TitleFontSpace = uiConfig.value("MainWnd/titleFontSpace", 10).toInt();
    //状态栏字体
    main_StatusFontSize = uiConfig.value("MainWnd/statusFontSize", 12).toInt();
    // © 符号在ini文件加载不出来
    main_CopyRight =
        "江西交通监控指挥中心版权所有 江西锦路科技开发有限公司技术支持";  // "Copyright©2021
                                                                          // 江西锦路科技开发有限公司版权所有";
    //子窗口显示区域
    m_RectSubForm = uiConfig.value("MainWnd/subWndRect", QRect(0, 50, 1280, 692)).toRect();

    //子窗口边缘margin
    sub_margin = uiConfig.value("SubWnd/margin", 10).toInt();

    /*************Out Of Service界面*******************/
    oos_FontSize = uiConfig.value("FormOutOfService/fontSize", 100).toInt();
    oos_FontSpace = uiConfig.value("FormOutOfService/fontSpace", 100).toInt();
    //行间距（共两行）
    oos_LineSpace = uiConfig.value("FormOutOfService/lineSpace", 10).toInt();
    //图标区域
    oos_IconRect = uiConfig.value("FormOutOfService/iconRect", QRect(0, 50, 1280, 692)).toRect();

    /********等待车辆界面**********/
    //圆形显示框
    waitveh_roundRect =
        uiConfig.value("FormWaitVehicle/roundRect", QRect(0, 50, 1280, 692)).toRect();
    //字体大小
    waitveh_FontSize = uiConfig.value("FormWaitVehicle/fontSize", 10).toInt();
    //字间距
    waitveh_FontSpace = uiConfig.value("FormWaitVehicle/fontSpace", 10).toInt();
    //行间距（共两行）
    waitveh_LineSpace = uiConfig.value("FormWaitVehicle/lineSpace", 10).toInt();

    /************提示界面*************/
    //图标显示
    prompt_iconRect = uiConfig.value("FormPrompt/iconRect", QRect(0, 50, 1280, 692)).toRect();
    //提示文字区域
    prompt_textRect = uiConfig.value("FormPrompt/textRect", QRect(0, 50, 1280, 692)).toRect();
    //字体大小
    prompt_FontSize = uiConfig.value("FormPrompt/fontSize", 10).toInt();
    //字间距
    prompt_FontSpace = uiConfig.value("FormPrompt/fontSpace", 10).toInt();

    /********插卡界面*********/
    //圆形显示框
    incard_bigRoundRect =
        uiConfig.value("FormInsertCard/bigRoundRect", QRect(0, 50, 1280, 692)).toRect();
    //小圆形显示框
    incard_smallRoundRect =
        uiConfig.value("FormInsertCard/smallRoundRect", QRect(0, 50, 1280, 692)).toRect();
    //大字提示显示框
    incard_bigTextRect =
        uiConfig.value("FormInsertCard/bigTextRect", QRect(0, 50, 1280, 692)).toRect();
    //小字提示显示框
    incard_smallTextRect =
        uiConfig.value("FormInsertCard/smallTextRect", QRect(0, 50, 1280, 692)).toRect();
    //字体大小
    incard_BigFontSize = uiConfig.value("FormInsertCard/bigFontSize", 10).toInt();
    //字间距
    incard_BigFontSpace = uiConfig.value("FormInsertCard/bigFontSpace", 10).toInt();
    //大字体大小
    incard_SmallFontSize = uiConfig.value("FormInsertCard/smallFontSize", 10).toInt();
    //大字体间距
    incard_SmallFontSpace = uiConfig.value("FormInsertCard/smallFontSpace", 10).toInt();

    /********显示金额界面*******/
    //显示收费信息的小矩形
    money_smallRoundRect =
        uiConfig.value("FormShowTollMoney/smallRoundRect", QRect(0, 50, 1280, 692)).toRect();
    //小矩形之前的间隔
    money_verticalMargin = uiConfig.value("FormShowTollMoney/verticalMargin", 10).toInt();
    //收费字体大小
    money_tollInfoFontSize = uiConfig.value("FormShowTollMoney/tollInfoFontSize", 10).toInt();
    //金额字体大小
    money_moneyFontSize = uiConfig.value("FormShowTollMoney/moneyFontSize", 10).toInt();
    //标题说明字体大小
    money_labelFontSize = uiConfig.value("FormShowTollMoney/labelFontSize", 10).toInt();
    //标题宽度
    money_labelWidth = uiConfig.value("FormShowTollMoney/labelWidth", 10).toInt();
    //右侧图示位置
    money_imageRect =
        uiConfig.value("FormShowTollMoney/imageRect", QRect(0, 50, 1280, 692)).toRect();
    //提示文字位置
    money_tipRect = uiConfig.value("FormShowTollMoney/tipRect", QRect(0, 50, 1280, 692)).toRect();
    //提示文字字体大小
    money_tipFontSize = uiConfig.value("FormShowTollMoney/tipFontSize", 10).toInt();
    //提示文字间隔
    money_tipFontSpace = uiConfig.value("FormShowTollMoney/tipFontSpace", 10).toInt();
    //支付标识大小
    money_alipaylogoRect =
        uiConfig.value("FormShowTollMoney/alipayRect", QRect(0, 50, 1280, 692)).toRect();
    money_wechatlogoRect =
        uiConfig.value("FormShowTollMoney/wechatRect", QRect(0, 50, 1280, 692)).toRect();
    return true;
}

void GlobalUI::Init() { LoadCfg("autoextollui.ini"); }
