#ifndef SERNUMFM_H
#define SERNUMFM_H

#include <QDialog>
#include "sernum.h"
#include "baseetcdialog.h"
namespace Ui {
class SerNumFm;
}

class SerNumFm : public CBaseEtcDialog
{
    Q_OBJECT

public:
    explicit SerNumFm(QWidget *parent = 0);
    ~SerNumFm();
    void Init();
private slots:
//    void on_pushButton_clicked();
    
    void on_pb_OK_clicked();
    void mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
private:
    Ui::SerNumFm *ui;
    SerNum *ser;
    int nOrgCode;
private:
    void GetLocalInfo();
};

#endif // SERNUMFM_H
