 #include "device_init_fix.h"
#include <QApplication>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QStandardPaths>
#include <QProcess>
#include <QtConcurrent>

// 全局静态成员初始化
DeviceInitializationManager* DeviceInitializationManager::s_instance = 0;
QMutex DeviceInitializationManager::s_mutex;

ApplicationWatchdog* ApplicationWatchdog::s_instance = 0;
QMutex ApplicationWatchdog::s_mutex;

/**
 * @brief DeviceInitTimeoutController 构造函数
 * @param config 设备配置
 * @param parent 父对象
 */
DeviceInitTimeoutController::DeviceInitTimeoutController(const DeviceInitConfig& config, QObject *parent)
    : QObject(parent), m_config(config)
{
    m_pTimeoutTimer = new QTimer(this);
    m_pTimeoutTimer->setSingleShot(true);
    connect(m_pTimeoutTimer, SIGNAL(timeout()), this, SLOT(onTimeout()));
}

DeviceInitTimeoutController::~DeviceInitTimeoutController()
{
    if (m_pTimeoutTimer) {
        m_pTimeoutTimer->stop();
    }
}

void DeviceInitTimeoutController::startTimeout()
{
    if (m_pTimeoutTimer && m_config.timeoutMs > 0) {
        m_pTimeoutTimer->start(m_config.timeoutMs);
        DetailedDeviceLogger::logInitStep(DEVICE_TYPE_COUNT, m_config.deviceName, 
                                         0, QString("开始超时监控，超时时间: %1ms").arg(m_config.timeoutMs));
    }
}

void DeviceInitTimeoutController::stopTimeout()
{
    if (m_pTimeoutTimer) {
        m_pTimeoutTimer->stop();
    }
}

void DeviceInitTimeoutController::resetTimeout()
{
    if (m_pTimeoutTimer && m_pTimeoutTimer->isActive()) {
        m_pTimeoutTimer->stop();
        m_pTimeoutTimer->start(m_config.timeoutMs);
    }
}

void DeviceInitTimeoutController::onTimeout()
{
    DetailedDeviceLogger::logInitTimeout(DEVICE_TYPE_COUNT, m_config.deviceName, m_config.timeoutMs);
    emit timeoutOccurred(m_config.deviceName);
}

/**
 * @brief AsyncDeviceInitWorker 构造函数
 */
AsyncDeviceInitWorker::AsyncDeviceInitWorker(DeviceType deviceType, 
                                           const DeviceInitConfig& config,
                                           QObject *parent)
    : QObject(parent), m_deviceType(deviceType), m_config(config)
{
}

void AsyncDeviceInitWorker::performInitialization()
{
    DeviceInitResult result;
    result.deviceType = m_deviceType;
    result.status = DEVICE_INIT_RUNNING;
    
    QElapsedTimer timer;
    timer.start();
    
    QString errorMsg;
    bool success = false;
    
    DetailedDeviceLogger::logInitStart(m_deviceType, m_config.deviceName);
    
    try {
        // 根据设备类型执行相应的初始化
        switch (m_deviceType) {
            case DEVICE_TYPE_VPR:
                success = initVPRDevice(errorMsg);
                break;
            case DEVICE_TYPE_IOCARD:
                success = initIOCardDevice(errorMsg);
                break;
            case DEVICE_TYPE_CARDREADER:
                success = initCardReaderDevice(errorMsg);
                break;
            case DEVICE_TYPE_RSU:
                success = initRSUDevice(errorMsg);
                break;
            case DEVICE_TYPE_PRINTER:
                success = initPrinterDevice(errorMsg);
                break;
            case DEVICE_TYPE_NETWORK:
                success = initNetworkModule(errorMsg);
                break;
            case DEVICE_TYPE_DATABASE:
                success = initDatabaseConnection(errorMsg);
                break;
            default:
                success = false;
                errorMsg = "未知设备类型";
                break;
        }
        
        result.status = success ? DEVICE_INIT_SUCCESS : DEVICE_INIT_FAILED;
        result.errorMessage = errorMsg;
        
    } catch (const std::exception& e) {
        result.status = DEVICE_INIT_FAILED;
        result.errorMessage = QString("设备初始化异常: %1").arg(e.what());
        DetailedDeviceLogger::logInitError(m_deviceType, m_config.deviceName, result.errorMessage);
    } catch (...) {
        result.status = DEVICE_INIT_FAILED;
        result.errorMessage = "设备初始化发生未知异常";
        DetailedDeviceLogger::logInitError(m_deviceType, m_config.deviceName, result.errorMessage);
    }
    
    result.initTime = timer.elapsed();
    
    DetailedDeviceLogger::logInitComplete(m_deviceType, m_config.deviceName, 
                                         success, result.initTime, 
                                         success ? "成功" : result.errorMessage);
    
    emit initializationCompleted(result);
}

bool AsyncDeviceInitWorker::initVPRDevice(QString& errorMsg)
{
    emit initializationProgress(m_deviceType, 10, "正在连接车牌识别设备...");
    
    // 模拟VPR设备初始化过程
    QThread::msleep(100); // 模拟设备响应延迟
    
    emit initializationProgress(m_deviceType, 30, "正在配置车牌识别参数...");
    QThread::msleep(200);
    
    emit initializationProgress(m_deviceType, 60, "正在启动车牌识别服务...");
    QThread::msleep(300);
    
    emit initializationProgress(m_deviceType, 90, "正在验证车牌识别功能...");
    QThread::msleep(150);
    
    emit initializationProgress(m_deviceType, 100, "车牌识别设备初始化完成");
    
    // 这里应该调用实际的VPR设备初始化代码
    // 例如: return CDeviceFactory::GetVPRDev(0)->StartVPRDev();
    
    return true; // 模拟成功
}

bool AsyncDeviceInitWorker::initIOCardDevice(QString& errorMsg)
{
    emit initializationProgress(m_deviceType, 10, "正在检测IO卡硬件...");
    QThread::msleep(200);
    
    emit initializationProgress(m_deviceType, 40, "正在初始化IO卡驱动...");
    QThread::msleep(300);
    
    emit initializationProgress(m_deviceType, 70, "正在配置IO卡参数...");
    QThread::msleep(200);
    
    emit initializationProgress(m_deviceType, 100, "IO卡设备初始化完成");
    
    // 这里应该调用实际的IO卡初始化代码
    // 例如: return CDeviceFactory::GetIOCard()->StartIoCard(dwInput);
    
    return true; // 模拟成功
}

bool AsyncDeviceInitWorker::initCardReaderDevice(QString& errorMsg)
{
    emit initializationProgress(m_deviceType, 20, "正在连接读卡器...");
    QThread::msleep(150);
    
    emit initializationProgress(m_deviceType, 60, "正在初始化读卡器协议...");
    QThread::msleep(250);
    
    emit initializationProgress(m_deviceType, 100, "读卡器设备初始化完成");
    
    return true; // 模拟成功
}

bool AsyncDeviceInitWorker::initRSUDevice(QString& errorMsg)
{
    emit initializationProgress(m_deviceType, 15, "正在连接RSU设备...");
    QThread::msleep(200);
    
    emit initializationProgress(m_deviceType, 50, "正在配置RSU通信参数...");
    QThread::msleep(300);
    
    emit initializationProgress(m_deviceType, 80, "正在验证RSU连接...");
    QThread::msleep(200);
    
    emit initializationProgress(m_deviceType, 100, "RSU设备初始化完成");
    
    return true; // 模拟成功
}

bool AsyncDeviceInitWorker::initPrinterDevice(QString& errorMsg)
{
    emit initializationProgress(m_deviceType, 25, "正在检测打印机状态...");
    QThread::msleep(100);
    
    emit initializationProgress(m_deviceType, 75, "正在初始化打印机驱动...");
    QThread::msleep(200);
    
    emit initializationProgress(m_deviceType, 100, "打印机设备初始化完成");
    
    return true; // 模拟成功
}

bool AsyncDeviceInitWorker::initNetworkModule(QString& errorMsg)
{
    emit initializationProgress(m_deviceType, 10, "正在初始化网络模块...");
    QThread::msleep(100);
    
    emit initializationProgress(m_deviceType, 30, "正在测试网络连接...");
    QThread::msleep(500); // 网络测试可能需要更长时间
    
    emit initializationProgress(m_deviceType, 70, "正在配置网络参数...");
    QThread::msleep(200);
    
    emit initializationProgress(m_deviceType, 100, "网络模块初始化完成");
    
    return true; // 模拟成功
}

bool AsyncDeviceInitWorker::initDatabaseConnection(QString& errorMsg)
{
    emit initializationProgress(m_deviceType, 20, "正在连接数据库...");
    QThread::msleep(300);
    
    emit initializationProgress(m_deviceType, 60, "正在验证数据库结构...");
    QThread::msleep(200);
    
    emit initializationProgress(m_deviceType, 90, "正在初始化数据库连接池...");
    QThread::msleep(150);
    
    emit initializationProgress(m_deviceType, 100, "数据库连接初始化完成");
    
    return true; // 模拟成功
}

/**
 * @brief DeviceInitializationManager 实现
 */
DeviceInitializationManager* DeviceInitializationManager::getInstance()
{
    QMutexLocker locker(&s_mutex);
    if (!s_instance) {
        s_instance = new DeviceInitializationManager();
    }
    return s_instance;
}

DeviceInitializationManager::DeviceInitializationManager(QObject *parent)
    : QObject(parent)
{
    setupDefaultConfigs();
}

DeviceInitializationManager::~DeviceInitializationManager()
{
    cleanup();
}

void DeviceInitializationManager::setupDefaultConfigs()
{
    // 设置各设备的默认配置
    m_deviceConfigs[DEVICE_TYPE_VPR] = DeviceConfigHelper::getVPRConfig();
    m_deviceConfigs[DEVICE_TYPE_IOCARD] = DeviceConfigHelper::getIOCardConfig();
    m_deviceConfigs[DEVICE_TYPE_CARDREADER] = DeviceConfigHelper::getCardReaderConfig();
    m_deviceConfigs[DEVICE_TYPE_RSU] = DeviceConfigHelper::getRSUConfig();
    m_deviceConfigs[DEVICE_TYPE_PRINTER] = DeviceConfigHelper::getPrinterConfig();
    m_deviceConfigs[DEVICE_TYPE_NETWORK] = DeviceConfigHelper::getNetworkConfig();
    m_deviceConfigs[DEVICE_TYPE_DATABASE] = DeviceConfigHelper::getDatabaseConfig();
}

void DeviceInitializationManager::registerDeviceConfig(DeviceType deviceType, const DeviceInitConfig& config)
{
    QMutexLocker locker(&m_resultMutex);
    m_deviceConfigs[deviceType] = config;
}

bool DeviceInitializationManager::initializeAllDevices(QString& errorMsg)
{
    resetAllDevices();
    
    QStringList errors;
    int successCount = 0;
    int totalDevices = m_deviceConfigs.count();
    
    for (auto it = m_deviceConfigs.begin(); it != m_deviceConfigs.end(); ++it) {
        DeviceType deviceType = it.key();
        QString deviceError;
        
        emit initializationProgress(totalDevices, successCount, it.value().deviceName);
        
        if (initializeDevice(deviceType, deviceError)) {
            successCount++;
        } else {
            errors.append(QString("%1: %2").arg(it.value().deviceName).arg(deviceError));
            
            // 如果设备配置为失败时不跳过，则停止初始化
            if (!it.value().skipOnFailure) {
                errorMsg = QString("关键设备 %1 初始化失败: %2").arg(it.value().deviceName).arg(deviceError);
                return false;
            }
        }
    }
    
    bool allSuccess = (successCount == totalDevices);
    if (!allSuccess && !errors.isEmpty()) {
        errorMsg = QString("部分设备初始化失败: %1").arg(errors.join("; "));
    }
    
    QString summary = QString("设备初始化完成: 成功 %1/%2").arg(successCount).arg(totalDevices);
    emit allDevicesInitialized(allSuccess, summary);
    
    return allSuccess || errors.isEmpty();
}

void DeviceInitializationManager::initializeAllDevicesAsync()
{
    resetAllDevices();
    
    for (auto it = m_deviceConfigs.begin(); it != m_deviceConfigs.end(); ++it) {
        DeviceType deviceType = it.key();
        const DeviceInitConfig& config = it.value();
        
        // 创建工作线程
        QThread* thread = new QThread(this);
        AsyncDeviceInitWorker* worker = new AsyncDeviceInitWorker(deviceType, config);
        worker->moveToThread(thread);
        
        // 连接信号槽
        connect(thread, SIGNAL(started()), worker, SLOT(performInitialization()));
        connect(worker, SIGNAL(initializationCompleted(DeviceInitResult)), 
                this, SLOT(onDeviceInitCompleted(DeviceInitResult)));
        connect(worker, SIGNAL(initializationProgress(DeviceType, int, QString)),
                this, SLOT(onDeviceInitProgress(DeviceType, int, QString)));
        connect(worker, SIGNAL(initializationCompleted(DeviceInitResult)),
                thread, SLOT(quit()));
        connect(thread, SIGNAL(finished()), worker, SLOT(deleteLater()));
        connect(thread, SIGNAL(finished()), thread, SLOT(deleteLater()));
        
        // 创建超时控制器
        if (config.enableWatchdog) {
            DeviceInitTimeoutController* timeoutController = 
                new DeviceInitTimeoutController(config, this);
            connect(timeoutController, SIGNAL(timeoutOccurred(QString)),
                    this, SLOT(onDeviceTimeout(QString)));
            
            m_timeoutControllers[deviceType] = timeoutController;
            timeoutController->startTimeout();
        }
        
        m_workerThreads[deviceType] = thread;
        m_workers[deviceType] = worker;
        
        // 启动线程
        thread->start();
    }
}

bool DeviceInitializationManager::initializeDevice(DeviceType deviceType, QString& errorMsg)
{
    if (!m_deviceConfigs.contains(deviceType)) {
        errorMsg = "设备配置未找到";
        return false;
    }
    
    const DeviceInitConfig& config = m_deviceConfigs[deviceType];
    DeviceInitResult result;
    result.deviceType = deviceType;
    result.status = DEVICE_INIT_RUNNING;
    
    {
        QMutexLocker locker(&m_resultMutex);
        m_deviceResults[deviceType] = result;
    }
    
    // 创建异步工作器在当前线程中同步执行
    AsyncDeviceInitWorker worker(deviceType, config);
    
    bool success = false;
    QString workerError;
    
    // 这里应该调用实际的设备初始化逻辑
    // 为了演示，我们直接调用worker的方法
    
    QElapsedTimer timer;
    timer.start();
    
    try {
        // 根据设备类型调用相应的初始化方法
        switch (deviceType) {
            case DEVICE_TYPE_VPR:
                success = worker.initVPRDevice(workerError);
                break;
            case DEVICE_TYPE_IOCARD:
                success = worker.initIOCardDevice(workerError);
                break;
            case DEVICE_TYPE_CARDREADER:
                success = worker.initCardReaderDevice(workerError);
                break;
            case DEVICE_TYPE_RSU:
                success = worker.initRSUDevice(workerError);
                break;
            case DEVICE_TYPE_PRINTER:
                success = worker.initPrinterDevice(workerError);
                break;
            case DEVICE_TYPE_NETWORK:
                success = worker.initNetworkModule(workerError);
                break;
            case DEVICE_TYPE_DATABASE:
                success = worker.initDatabaseConnection(workerError);
                break;
            default:
                success = false;
                workerError = "未知设备类型";
                break;
        }
    } catch (const std::exception& e) {
        success = false;
        workerError = QString("设备初始化异常: %1").arg(e.what());
    }
    
    result.status = success ? DEVICE_INIT_SUCCESS : DEVICE_INIT_FAILED;
    result.errorMessage = workerError;
    result.initTime = timer.elapsed();
    
    {
        QMutexLocker locker(&m_resultMutex);
        m_deviceResults[deviceType] = result;
    }
    
    emit deviceInitialized(result);
    
    if (!success) {
        errorMsg = workerError;
    }
    
    return success;
}

DeviceInitStatus DeviceInitializationManager::getDeviceStatus(DeviceType deviceType) const
{
    QMutexLocker locker(&const_cast<DeviceInitializationManager*>(this)->m_resultMutex);
    
    if (m_deviceResults.contains(deviceType)) {
        return m_deviceResults[deviceType].status;
    }
    return DEVICE_INIT_PENDING;
}

DeviceInitResult DeviceInitializationManager::getDeviceResult(DeviceType deviceType) const
{
    QMutexLocker locker(&const_cast<DeviceInitializationManager*>(this)->m_resultMutex);
    
    if (m_deviceResults.contains(deviceType)) {
        return m_deviceResults[deviceType];
    }
    
    DeviceInitResult result;
    result.deviceType = deviceType;
    return result;
}

bool DeviceInitializationManager::isAllDevicesInitialized() const
{
    QMutexLocker locker(&const_cast<DeviceInitializationManager*>(this)->m_resultMutex);
    
    for (auto it = m_deviceResults.begin(); it != m_deviceResults.end(); ++it) {
        DeviceInitStatus status = it.value().status;
        if (status == DEVICE_INIT_PENDING || status == DEVICE_INIT_RUNNING) {
            return false;
        }
    }
    return true;
}

int DeviceInitializationManager::getSuccessfulDeviceCount() const
{
    QMutexLocker locker(&const_cast<DeviceInitializationManager*>(this)->m_resultMutex);
    
    int count = 0;
    for (auto it = m_deviceResults.begin(); it != m_deviceResults.end(); ++it) {
        if (it.value().status == DEVICE_INIT_SUCCESS) {
            count++;
        }
    }
    return count;
}

void DeviceInitializationManager::resetAllDevices()
{
    cleanup();
    
    QMutexLocker locker(&m_resultMutex);
    m_deviceResults.clear();
}

void DeviceInitializationManager::cleanup()
{
    // 停止所有超时控制器
    for (auto it = m_timeoutControllers.begin(); it != m_timeoutControllers.end(); ++it) {
        it.value()->stopTimeout();
        delete it.value();
    }
    m_timeoutControllers.clear();
    
    // 等待所有工作线程结束
    for (auto it = m_workerThreads.begin(); it != m_workerThreads.end(); ++it) {
        if (it.value()->isRunning()) {
            it.value()->quit();
            it.value()->wait(3000); // 等待3秒
        }
    }
    m_workerThreads.clear();
    m_workers.clear();
}

void DeviceInitializationManager::onDeviceInitCompleted(const DeviceInitResult& result)
{
    {
        QMutexLocker locker(&m_resultMutex);
        m_deviceResults[result.deviceType] = result;
    }
    
    // 停止对应的超时控制器
    if (m_timeoutControllers.contains(result.deviceType)) {
        m_timeoutControllers[result.deviceType]->stopTimeout();
    }
    
    emit deviceInitialized(result);
    
    // 检查是否所有设备初始化完成
    if (isAllDevicesInitialized()) {
        int successCount = getSuccessfulDeviceCount();
        int totalCount = m_deviceResults.count();
        bool allSuccess = (successCount == totalCount);
        
        QString summary = QString("异步设备初始化完成: 成功 %1/%2").arg(successCount).arg(totalCount);
        emit allDevicesInitialized(allSuccess, summary);
    }
}

void DeviceInitializationManager::onDeviceInitProgress(DeviceType deviceType, int progress, const QString& message)
{
    // 更新应用程序看门狗心跳
    ApplicationWatchdog::getInstance()->updateHeartbeat();
    
    // 可以在这里添加进度显示逻辑
    DetailedDeviceLogger::logInitStep(deviceType, 
                                     m_deviceConfigs[deviceType].deviceName, 
                                     progress, message);
}

void DeviceInitializationManager::onDeviceTimeout(const QString& deviceName)
{
    // 查找对应的设备类型
    DeviceType deviceType = DEVICE_TYPE_COUNT;
    for (auto it = m_deviceConfigs.begin(); it != m_deviceConfigs.end(); ++it) {
        if (it.value().deviceName == deviceName) {
            deviceType = it.key();
            break;
        }
    }
    
    if (deviceType != DEVICE_TYPE_COUNT) {
        DeviceInitResult result;
        result.deviceType = deviceType;
        result.status = DEVICE_INIT_TIMEOUT;
        result.errorMessage = QString("设备初始化超时: %1").arg(deviceName);
        
        {
            QMutexLocker locker(&m_resultMutex);
            m_deviceResults[deviceType] = result;
        }
        
        emit deviceInitialized(result);
        
        // 强制停止对应的工作线程
        if (m_workerThreads.contains(deviceType)) {
            QThread* thread = m_workerThreads[deviceType];
            if (thread->isRunning()) {
                thread->quit();
                if (!thread->wait(1000)) {
                    thread->terminate();
                }
            }
        }
    }
}

/**
 * @brief ApplicationWatchdog 实现
 */
ApplicationWatchdog* ApplicationWatchdog::getInstance()
{
    QMutexLocker locker(&s_mutex);
    if (!s_instance) {
        s_instance = new ApplicationWatchdog();
    }
    return s_instance;
}

ApplicationWatchdog::ApplicationWatchdog(QObject *parent)
    : QObject(parent), m_lastHeartbeat(0), m_timeoutMs(30000), 
      m_autoRestart(false), m_isActive(false)
{
    m_pWatchdogTimer = new QTimer(this);
    connect(m_pWatchdogTimer, SIGNAL(timeout()), this, SLOT(checkHeartbeat()));
}

ApplicationWatchdog::~ApplicationWatchdog()
{
    stopWatchdog();
}

void ApplicationWatchdog::startWatchdog(int checkIntervalMs, int timeoutMs)
{
    m_timeoutMs = timeoutMs;
    updateHeartbeat();
    
    m_pWatchdogTimer->start(checkIntervalMs);
    m_isActive = true;
    
    DetailedDeviceLogger::writeToSystemLog(
        QString("应用程序看门狗已启动 - 检查间隔: %1ms, 超时时间: %2ms")
        .arg(checkIntervalMs).arg(timeoutMs));
}

void ApplicationWatchdog::stopWatchdog()
{
    if (m_pWatchdogTimer) {
        m_pWatchdogTimer->stop();
    }
    m_isActive = false;
    
    DetailedDeviceLogger::writeToSystemLog("应用程序看门狗已停止");
}

void ApplicationWatchdog::updateHeartbeat()
{
    m_lastHeartbeat = QDateTime::currentMSecsSinceEpoch();
}

void ApplicationWatchdog::setAutoRestart(bool enable)
{
    m_autoRestart = enable;
}

void ApplicationWatchdog::checkHeartbeat()
{
    if (!m_isActive) {
        return;
    }
    
    qint64 current = QDateTime::currentMSecsSinceEpoch();
    qint64 elapsed = current - m_lastHeartbeat;
    
    if (elapsed > m_timeoutMs) {
        DetailedDeviceLogger::writeToSystemLog(
            QString("检测到应用程序可能挂起 - 上次心跳时间: %1, 当前时间: %2, 超时: %3ms")
            .arg(QDateTime::fromMSecsSinceEpoch(m_lastHeartbeat).toString("yyyy-MM-dd hh:mm:ss.zzz"))
            .arg(QDateTime::fromMSecsSinceEpoch(current).toString("yyyy-MM-dd hh:mm:ss.zzz"))
            .arg(elapsed));
        
        emit applicationHangDetected(m_lastHeartbeat);
        
        if (m_autoRestart) {
            DetailedDeviceLogger::writeToSystemLog("准备自动重启应用程序...");
            emit preparingRestart();
            
            // 延迟重启，给应用程序一些清理时间
            QTimer::singleShot(2000, []() {
                QApplication::exit(999); // 使用特殊退出码表示看门狗重启
            });
        }
    }
}

/**
 * @brief DetailedDeviceLogger 实现
 */
void DetailedDeviceLogger::logInitStart(DeviceType deviceType, const QString& deviceName)
{
    QString message = QString("[%1] 设备初始化开始 - %2 (%3)")
                     .arg(getCurrentTimestamp())
                     .arg(deviceName)
                     .arg(getDeviceTypeName(deviceType));
    
    writeToSystemLog(message);
    writeToDeviceLog(message);
}

void DetailedDeviceLogger::logInitStep(DeviceType deviceType, const QString& deviceName, 
                                      int step, const QString& message)
{
    QString logMessage = QString("[%1] 设备初始化步骤 - %2 [%3%] %4")
                        .arg(getCurrentTimestamp())
                        .arg(deviceName)
                        .arg(step)
                        .arg(message);
    
    writeToSystemLog(logMessage);
    writeToDeviceLog(logMessage);
}

void DetailedDeviceLogger::logInitComplete(DeviceType deviceType, const QString& deviceName, 
                                          bool success, qint64 elapsedTime, const QString& result)
{
    QString status = success ? "成功" : "失败";
    QString message = QString("[%1] 设备初始化完成 - %2 状态:%3 耗时:%4ms 结果:%5")
                     .arg(getCurrentTimestamp())
                     .arg(deviceName)
                     .arg(status)
                     .arg(elapsedTime)
                     .arg(result);
    
    writeToSystemLog(message);
    writeToDeviceLog(message);
}

void DetailedDeviceLogger::logInitError(DeviceType deviceType, const QString& deviceName, 
                                       const QString& errorMessage)
{
    QString message = QString("[%1] 设备初始化错误 - %2 错误:%3")
                     .arg(getCurrentTimestamp())
                     .arg(deviceName)
                     .arg(errorMessage);
    
    writeToSystemLog(message);
    writeToDeviceLog(message);
}

void DetailedDeviceLogger::logInitTimeout(DeviceType deviceType, const QString& deviceName, 
                                         int timeoutMs)
{
    QString message = QString("[%1] 设备初始化超时 - %2 超时时间:%3ms")
                     .arg(getCurrentTimestamp())
                     .arg(deviceName)
                     .arg(timeoutMs);
    
    writeToSystemLog(message);
    writeToDeviceLog(message);
}

QString DetailedDeviceLogger::generateInitSummary(const QMap<DeviceType, DeviceInitResult>& results)
{
    QStringList summary;
    summary.append(QString("=== 设备初始化摘要报告 ==="));
    summary.append(QString("生成时间: %1").arg(getCurrentTimestamp()));
    summary.append(QString(""));
    
    int successCount = 0;
    int failedCount = 0;
    int timeoutCount = 0;
    qint64 totalTime = 0;
    
    for (auto it = results.begin(); it != results.end(); ++it) {
        const DeviceInitResult& result = it.value();
        totalTime += result.initTime;
        
        QString status;
        switch (result.status) {
            case DEVICE_INIT_SUCCESS:
                status = "成功";
                successCount++;
                break;
            case DEVICE_INIT_FAILED:
                status = "失败";
                failedCount++;
                break;
            case DEVICE_INIT_TIMEOUT:
                status = "超时";
                timeoutCount++;
                break;
            default:
                status = "未知";
                break;
        }
        
        summary.append(QString("设备: %1 状态: %2 耗时: %3ms")
                      .arg(getDeviceTypeName(result.deviceType))
                      .arg(status)
                      .arg(result.initTime));
        
        if (!result.errorMessage.isEmpty()) {
            summary.append(QString("  错误信息: %1").arg(result.errorMessage));
        }
    }
    
    summary.append(QString(""));
    summary.append(QString("统计信息:"));
    summary.append(QString("  总设备数: %1").arg(results.count()));
    summary.append(QString("  成功: %1").arg(successCount));
    summary.append(QString("  失败: %1").arg(failedCount));
    summary.append(QString("  超时: %1").arg(timeoutCount));
    summary.append(QString("  总耗时: %1ms").arg(totalTime));
    summary.append(QString("=========================================="));
    
    return summary.join("\n");
}

QString DetailedDeviceLogger::getDeviceTypeName(DeviceType deviceType)
{
    switch (deviceType) {
        case DEVICE_TYPE_VPR: return "车牌识别设备";
        case DEVICE_TYPE_IOCARD: return "IO卡设备";
        case DEVICE_TYPE_CARDREADER: return "读卡器设备";
        case DEVICE_TYPE_RSU: return "RSU设备";
        case DEVICE_TYPE_PRINTER: return "打印机设备";
        case DEVICE_TYPE_NETWORK: return "网络模块";
        case DEVICE_TYPE_DATABASE: return "数据库";
        default: return "未知设备";
    }
}

QString DetailedDeviceLogger::getCurrentTimestamp()
{
    // 使用北京时间 (UTC+8)
    QDateTime beijingTime = QDateTime::currentDateTime().toTimeZone(QTimeZone("Asia/Shanghai"));
    return beijingTime.toString("yyyy-MM-dd hh:mm:ss.zzz");
}

void DetailedDeviceLogger::writeToDeviceLog(const QString& message)
{
    QString logDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs";
    QDir().mkpath(logDir);
    
    QString logFile = logDir + "/device_init.log";
    QFile file(logFile);
    
    if (file.open(QIODevice::WriteOnly | QIODevice::Append)) {
        QTextStream stream(&file);
        stream.setCodec("GB2312"); // 使用GB2312编码
        stream << message << "\n";
        file.close();
    }
}

void DetailedDeviceLogger::writeToSystemLog(const QString& message)
{
    // 这里可以调用现有的系统日志接口
    // 例如: DebugLog(message);
    qDebug() << message;
}

/**
 * @brief DeviceConfigHelper 实现
 */
namespace DeviceConfigHelper {

DeviceInitConfig getVPRConfig()
{
    DeviceInitConfig config;
    config.deviceName = "车牌识别设备";
    config.timeoutMs = 15000;      // 15秒超时
    config.retryCount = 2;         // 重试2次
    config.skipOnFailure = true;   // 失败时跳过
    config.asyncInit = true;       // 异步初始化
    config.enableWatchdog = true;  // 启用看门狗
    return config;
}

DeviceInitConfig getIOCardConfig()
{
    DeviceInitConfig config;
    config.deviceName = "IO卡设备";
    config.timeoutMs = 10000;      // 10秒超时
    config.retryCount = 3;         // 重试3次
    config.skipOnFailure = false;  // 失败时不跳过（关键设备）
    config.asyncInit = false;      // 同步初始化（关键设备）
    config.enableWatchdog = true;  // 启用看门狗
    return config;
}

DeviceInitConfig getCardReaderConfig()
{
    DeviceInitConfig config;
    config.deviceName = "读卡器设备";
    config.timeoutMs = 8000;       // 8秒超时
    config.retryCount = 2;         // 重试2次
    config.skipOnFailure = true;   // 失败时跳过
    config.asyncInit = true;       // 异步初始化
    config.enableWatchdog = true;  // 启用看门狗
    return config;
}

DeviceInitConfig getRSUConfig()
{
    DeviceInitConfig config;
    config.deviceName = "RSU设备";
    config.timeoutMs = 12000;      // 12秒超时
    config.retryCount = 2;         // 重试2次
    config.skipOnFailure = true;   // 失败时跳过
    config.asyncInit = true;       // 异步初始化
    config.enableWatchdog = true;  // 启用看门狗
    return config;
}

DeviceInitConfig getPrinterConfig()
{
    DeviceInitConfig config;
    config.deviceName = "打印机设备";
    config.timeoutMs = 6000;       // 6秒超时
    config.retryCount = 1;         // 重试1次
    config.skipOnFailure = true;   // 失败时跳过
    config.asyncInit = true;       // 异步初始化
    config.enableWatchdog = true;  // 启用看门狗
    return config;
}

DeviceInitConfig getNetworkConfig()
{
    DeviceInitConfig config;
    config.deviceName = "网络模块";
    config.timeoutMs = 20000;      // 20秒超时（网络可能较慢）
    config.retryCount = 3;         // 重试3次
    config.skipOnFailure = false;  // 失败时不跳过（关键模块）
    config.asyncInit = true;       // 异步初始化
    config.enableWatchdog = true;  // 启用看门狗
    return config;
}

DeviceInitConfig getDatabaseConfig()
{
    DeviceInitConfig config;
    config.deviceName = "数据库连接";
    config.timeoutMs = 15000;      // 15秒超时
    config.retryCount = 3;         // 重试3次
    config.skipOnFailure = false;  // 失败时不跳过（关键模块）
    config.asyncInit = true;       // 异步初始化
    config.enableWatchdog = true;  // 启用看门狗
    return config;
}

} // namespace DeviceConfigHelper