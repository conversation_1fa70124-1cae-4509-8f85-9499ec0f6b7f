#include "sysparamdlg.h"
#include "common/lanetype.h"

CSysParamDlg::CSysParamDlg(QWidget *parent):
    CBaseOpWidget(parent)
{
    m_ptblWidget = NULL;
    m_pListWrapper = NULL;
    m_pListLabel.clear();

    Init();
    filterChildrenKeyEvent();
}

CSysParamDlg::~CSysParamDlg()
{
    if(m_pListWrapper) {
        delete m_pListWrapper;
        m_pListWrapper = NULL;
    }
}

void CSysParamDlg::Init()
{
    CBaseOpWidget::SetTitle("系统工作参数");

    QFont ftText;
    ftText.setFamily(QString::fromUtf8("微软雅黑"));
    ftText.setPixelSize(17);
    ftText.setStyleStrategy(QFont::PreferAntialias);

    // init
    m_pListLabel.clear();
    for(int i=0; i<MAX_PARAM_NUM/2; i++) {
        for(int j=0; j<2; j++) {
            QLabel *label = new QLabel(this);
            if(i==3 && j==1) {
                label->setGeometry(15, 60+(i+1)*30, this->width()-100, 30);
            } else
                label->setGeometry(15+j*((this->width()-120)/2), 60+i*30, (this->width()-120)/2, 30);
            label->setFont(ftText);
            label->setAlignment(Qt::AlignLeft);
            m_pListLabel.push_back(label);
        }
    }

    // add
    CSysParaDicTable *pSysTable = (CSysParaDicTable*)CParamFileMgr::GetParamFile(cfSysParaDic);
    if(!pSysTable) return;
    const CSysParamInfo *pSysInfo =  pSysTable->GetSysParam();
    if(!pSysInfo) return ;
    QString sHolidayFreeVer;
    pSysTable->GetHolidayFreeVer(sHolidayFreeVer);
    if(MAX_PARAM_NUM == m_pListLabel.size()){
        QString sID = QString("收费站编码: %1").arg(pSysInfo->m_nStationID);
        QString sSubCenter = QString("所属分中心: %1").arg(pSysInfo->m_nBLSubCenter);
        QString sCenter = QString("所属中心: %1").arg(pSysInfo->m_nBLCenter);
        m_pListLabel[0]->setText(sID);
        m_pListLabel[1]->setText(tr("收费站名称: ") + pSysInfo->m_sStationName);
        m_pListLabel[2]->setText(sSubCenter);
        m_pListLabel[3]->setText(sCenter);
        m_pListLabel[4]->setText(tr("收费站服务器: ") + pSysInfo->m_sStationIP);
        m_pListLabel[5]->setText(tr("中心服务器: ") + pSysInfo->m_sCenterIP);
        m_pListLabel[6]->setText(tr("坏卡查询服务: ") + pSysInfo->m_sBadCardQryIP);
        m_pListLabel[7]->setText(tr("重大节假日免费信息: ") + sHolidayFreeVer);
    }

    // init list
    if(!m_ptblWidget) m_ptblWidget = new QTableWidget(this);
    if(!m_pListWrapper) m_pListWrapper = new QListWrapper(m_ptblWidget);
    m_ptblWidget->installEventFilter(this);
    m_ptblWidget->setGeometry(0, 210, this->width()-1, this->height()-230);
    m_pListWrapper->insertColumn(0,tr("节假日名称"),90);
    m_pListWrapper->insertColumn(1,tr("开始时间"),110);
    m_pListWrapper->insertColumn(2,tr("结束时间"),110);
    m_pListWrapper->insertColumn(3,tr("客车|货车"),100);
    m_pListWrapper->insertColumn(4,tr("不免超限率"),90);

    // add list
    QList<CHolidayFreeRec> list = pSysTable->GetHolidayList();
    int nSize = list.size();
    CHolidayFreeRec holidayFree;
    int nMaxNum = 0;
    QList<CHolidayFreeRec>::ConstIterator it = list.constBegin();
    for(; it!=list.constEnd(); it++){
        nMaxNum++;
        holidayFree  = *it;
        if(m_pListWrapper->getRowCount() < nSize)
            m_pListWrapper->insertRow(nMaxNum-1);

        QString sHolidayName = GetHolidayFreeName(holidayFree.HolidayType);
        m_pListWrapper->setItemText(nMaxNum-1, 0, sHolidayName);
        m_pListWrapper->setItemText(nMaxNum-1, 1, holidayFree.stBeginTime.toString("yyyy-MM-dd hh:mm:ss"));
        m_pListWrapper->setItemText(nMaxNum-1, 2, holidayFree.stEndTime.toString("yyyy-MM-dd hh:mm:ss"));
        QByteArray by(holidayFree.FreeVehClass);
        QString sy = QString(QLatin1String(by.left(4).data())) + "|" + QString(QLatin1String(by.mid(4,5).data()));
        m_pListWrapper->setItemText(nMaxNum-1, 3, sy);
        m_pListWrapper->setItemText(nMaxNum-1, 4, QString::number(holidayFree.nOverWeightRate));
    }

    m_ptblWidget->setCurrentCell(0, QItemSelectionModel::Select);
    m_ptblWidget->setFocus();
}

void CSysParamDlg::closeEvent(QCloseEvent *event)
{
    event->ignore();
}

// 键盘事件处理
void CSysParamDlg::SetMove(int key)
{
   int nTotalRows = m_ptblWidget->rowCount();
   int nCurrentRow = m_ptblWidget->currentRow();

   if(nTotalRows == 0)
       return;

   // 上下键选择
   if(nCurrentRow == 0 && KeyUp == key){                     // 第一条
       nCurrentRow = 0;

   } else if(nCurrentRow == nTotalRows-1 && KeyDown == key){ // 最后一条
       nCurrentRow = nTotalRows - 1;

   } else if(nCurrentRow > 0 && KeyUp == key){
       nCurrentRow = nCurrentRow-1;

   } else if(nCurrentRow >= 0 && KeyDown == key) {
       nCurrentRow = nCurrentRow + 1;
   }
   m_ptblWidget->setCurrentCell(nCurrentRow, QItemSelectionModel::Select);
}

int CSysParamDlg::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if(mtcKeyEvent->isFuncKey() && mtcKeyEvent->func() == KeyConfirm)
        this->OnOk();

    // 上下键处理
    if(mtcKeyEvent->isFuncKey() && (mtcKeyEvent->func()==KeyUp ||
            mtcKeyEvent->func()==KeyDown))
        this->SetMove(mtcKeyEvent->func());

    // esc
    if(mtcKeyEvent->isFuncKey() && mtcKeyEvent->func()==KeyEsc)
        this->OnCancel();

    return 0;
}


