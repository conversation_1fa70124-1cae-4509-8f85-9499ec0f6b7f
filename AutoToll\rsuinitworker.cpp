#include "rsuinitworker.h"
#include "devices/rsudev.h"
#include "devicefactory.h"
#include "ilogmsg.h"
#include <QMutexLocker>
#include <QThread>
#include <exception>

/**
 * @brief 构造函数
 * @param pRsuDev 天线设备指针
 * @param nRsuIndex 天线索引
 * @param parent 父对象
 */
RsuInitWorker::RsuInitWorker(CRsuDev *pRsuDev, int nRsuIndex, QObject *parent)
    : QObject(parent)
    , m_pRsuDev(pRsuDev)
    , m_nRsuIndex(nRsuIndex)
{
}

RsuInitWorker::~RsuInitWorker()
{
}

/**
 * @brief 执行天线重新初始化
 * @note 在工作线程中异步执行，完成后发送 initFinished 信号
 *       执行步骤：1.关闭天线 -> 2.等待500ms -> 3.打开天线 -> 4.等待1000ms -> 5.重新初始化
 */
void RsuInitWorker::doInit()
{
    bool bSuccess = false;
    QString sError;
    
    try {
        // 参数检查
        if (!m_pRsuDev) {
            sError = QString("天线%1设备对象为空").arg(m_nRsuIndex);
            ErrorLog(sError);
            emit initFinished(m_nRsuIndex, false, sError);
            return;
        }
        
        // 验证设备对象有效性
        try {
            QString devId = m_pRsuDev->GetDevId();
            if (devId.isEmpty()) {
                sError = QString("天线%1设备ID无效").arg(m_nRsuIndex);
                ErrorLog(sError);
                emit initFinished(m_nRsuIndex, false, sError);
                return;
            }
            DebugLog(QString("开始异步重新初始化天线%1，设备ID: %2").arg(m_nRsuIndex).arg(devId));
        }
        catch (...) {
            sError = QString("天线%1设备对象访问异常").arg(m_nRsuIndex);
            ErrorLog(sError);
            emit initFinished(m_nRsuIndex, false, sError);
            return;
        }
        
        // 执行天线重新初始化
        // 这是一个可能耗时的操作，包含硬件通信
        
        // 1. 先关闭天线
        DebugLog(QString("天线%1重新初始化：第1步 - 关闭天线").arg(m_nRsuIndex));
        if (!m_pRsuDev->OpenRsu(0)) {
            sError = QString("天线%1关闭失败").arg(m_nRsuIndex);
            ErrorLog(sError);
            emit initFinished(m_nRsuIndex, false, sError);
            return;
        }
        
        // 2. 等待500毫秒让天线完全关闭
        QThread::msleep(500);
        
        // 3. 重新打开天线
        DebugLog(QString("天线%1重新初始化：第2步 - 打开天线").arg(m_nRsuIndex));
        if (!m_pRsuDev->OpenRsu(1)) {
            sError = QString("天线%1打开失败").arg(m_nRsuIndex);
            ErrorLog(sError);
            emit initFinished(m_nRsuIndex, false, sError);
            return;
        }
        
        // 4. 等待1000毫秒让天线稳定
        QThread::msleep(1000);
        
        // 5. 执行天线重新初始化
        DebugLog(QString("天线%1重新初始化：第3步 - 执行重新初始化").arg(m_nRsuIndex));
        bSuccess = m_pRsuDev->ReInitRsu();
        
        if (bSuccess) {
            DebugLog(QString("天线%1异步重新初始化成功").arg(m_nRsuIndex));
        } else {
            sError = QString("天线%1重新初始化失败").arg(m_nRsuIndex);
            ErrorLog(sError);
        }
    }
    catch (const std::exception& e) {
        sError = QString("天线%1重新初始化异常: %2").arg(m_nRsuIndex).arg(e.what());
        ErrorLog(sError);
        bSuccess = false;
    }
    catch (...) {
        sError = QString("天线%1重新初始化发生未知异常").arg(m_nRsuIndex);
        ErrorLog(sError);
        bSuccess = false;
    }
    
    // 发送完成信号
    emit initFinished(m_nRsuIndex, bSuccess, sError);
}
