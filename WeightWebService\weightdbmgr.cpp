#include "weightdbmgr.h"
#include <QCoreApplication>
#include <QDebug>
#include <QTcpSocket>

#include "weight.h"
#include "weightdb.h"
#include "httpclient.h"
#include "checkhttpserverip.h"
#include "globalutils.h"

CWeightDBMgr::CWeightDBMgr(QObject *parent) : QObject(parent)
{
    m_pCheckHttpIp = NULL;
    m_pWeightDbMgrLog = new CLog4Qt();
    m_lstHttpServerInfo.clear();

    m_pWeightDB = new CWeightDB();
}

CWeightDBMgr::~CWeightDBMgr()
{
    if(m_pWeightDB)
    {
        delete m_pWeightDB;
        m_pWeightDB = NULL;
    }

    if(m_pCheckHttpIp)
    {
        QObject::disconnect(m_pCheckHttpIp, SIGNAL(resethttpserverinfo(QList<SHttpServerInfo>)),
                            this, SLOT(onResetHttpServerInfo(QList<SHttpServerInfo>)));
        m_pCheckHttpIp->deleteLater();
    }

    if(m_pWeightDbMgrLog)
    {
        m_pWeightDbMgrLog->deleteLater();
    }
}

void CWeightDBMgr::onResetHttpServerInfo(const QList<SHttpServerInfo> &list)
{
    {
        QMutexLocker locker(&m_Mutex);
        m_lstHttpServerInfo = list;
    }
}

bool CWeightDBMgr::Init(const QString &sDBPath)
{
    InitHttpServiceLog();

    GetHttpServerUrl(sDBPath);
    CheckHttpServer();

    m_pCheckHttpIp = new CCheckHttpServerIP(m_lstHttpServerInfo);
    QObject::connect(m_pCheckHttpIp, SIGNAL(resethttpserverinfo(QList<SHttpServerInfo>)),
                     this, SLOT(onResetHttpServerInfo(QList<SHttpServerInfo>)));

    return InitWeightDB(sDBPath + "WeightDB/");
}

bool CWeightDBMgr::InitWeightDB(const QString &sDBPath)
{
    if(!m_pWeightDB->InitDB(sDBPath, m_pWeightDbMgrLog))
    {
        MgrErrorLog("数据库连接失败");
        return false;
    }

    if(!m_pWeightDB->InitSqlQuery())
    {
        MgrErrorLog("数据库操作准备失败");
        return false;
    }

    return true;
}

void CWeightDBMgr::InitHttpServiceLog()
{
    QString sLogFilePath;
    sLogFilePath =GetCurrentPath() + QString("HttpServiceLog");
    QDir dir(sLogFilePath);
    if(!dir.exists())
    {
        if(!dir.mkpath(sLogFilePath))
        {
            ErrorLog("创建HttpServiceLog路径失败");
            sLogFilePath = GetCurrentPath();
        }
    }
    QString sFileName=QString("WeightdbMgr");
    m_pWeightDbMgrLog->InitLog4Qt(sLogFilePath,sFileName,LOG_ALL);
}

void CWeightDBMgr::CheckHttpServer()
{
    QTcpSocket socket(0);
    QList<SHttpServerInfo>::iterator iter = m_lstHttpServerInfo.begin();
    for(; iter!=m_lstHttpServerInfo.end(); ++iter)
    {
        iter->bCanUse = false;

        QString sUrl = iter->sHttpServerUrl;
        QString sIp = sUrl.left(sUrl.indexOf(":"));
        quint16 wPort = sUrl.mid(sUrl.indexOf(":")+1).toUShort();
        if(0 == wPort)
        {
            continue;
        }

        socket.abort(); //取消原有连接
        socket.connectToHost(sIp,wPort); //建立一个TCP连接
        if(socket.waitForConnected(2*1000))
        {
            iter->bCanUse = true;
        }
        socket.close();
    }
}

bool CWeightDBMgr::FindData(const QString &sPlate, const quint16 &wColor, SWeightData &data)
{
    if(!m_pWeightDB->IsConneted())
    {
        MgrErrorLog("数据库未连接，请检查信息");
        return false;
    }

    QString sTmp = QString::fromUtf8(sPlate.toStdString().c_str());

    data.Clear();
    if(!ProcessWeightData(sTmp, wColor, data, false))
    {
        MgrDebugLog(QString("查无此车辆(车牌:%1,颜色:%2)计重信息！需要向治超端查询！").arg(sTmp).arg(wColor));
        if(!AskforWeightPlatform(sTmp, wColor, data))
        {
            MgrErrorLog(QString("称台端查无此车辆(车牌:%1,颜色:%2)计重信息！").arg(sTmp).arg(wColor));
            return false;
        }
    }

    return true;
}

bool CWeightDBMgr::AddData(const SWeightData &data)
{
    if(!m_pWeightDB->IsConneted())
    {
        MgrErrorLog("数据库未连接，请检查信息");
        return false;
    }

    QString sPlate = data.sVehPlate;
    quint16 wColor = data.wColor;
    SWeightData weightData = data;

    if(!ProcessWeightData(sPlate, wColor, weightData))
    {
        MgrErrorLog(QString("新增车辆(车牌:%1,颜色:%2)计重信息失败！").arg(sPlate).arg(wColor));
        return false;
    }

    return true;
}

bool CWeightDBMgr::GetHttpServerUrl(const QString &sDBPath)
{
    QString sLaneIni = sDBPath + QString("lane.ini");
    QSettings readLaneConfig(sLaneIni, QSettings::IniFormat);
    readLaneConfig.beginGroup("HttpServerUrl");

    QString sUrl;
    QStringList str = readLaneConfig.allKeys();
    foreach(QString key,str)
    {
        sUrl.clear();
        sUrl = readLaneConfig.value(key).toString();
        if(!sUrl.isEmpty())
        {
            SHttpServerInfo info;
            info.sHttpServerUrl = sUrl;
            info.bCanUse = false;

            m_lstHttpServerInfo.append(info);
        }
    }

    readLaneConfig.endGroup();
    return true;
}

bool CWeightDBMgr::ProcessWeightData(const QString &sPlate, const quint16 &wColor, SWeightData &data, bool bAddData /*= true*/)
{
    QMutexLocker locker(&m_Mutex);

    MgrDebugLog(QString("开始处理：车牌[%1],颜色[%2]的 %3 业务！").arg(sPlate).arg(wColor).arg((bAddData?"数据插入":"数据查询")));

    bool bRet = false;
    if(bAddData)
    {
        bRet = m_pWeightDB->UpdateWeightData(data);
    }
    else
    {
        bRet = m_pWeightDB->SelectWeightData(sPlate, wColor, data);
    }

    return bRet;
}

bool CWeightDBMgr::AskforWeightPlatform(const QString &sPlate, const quint16 &wColor, SWeightData &data)
{
    bool bPostRet = false;
    QList<SHttpServerInfo>::iterator iter = m_lstHttpServerInfo.begin();
    for(; iter!=m_lstHttpServerInfo.end(); ++iter)
    {
        int nPostRet = 99;
        if(iter->bCanUse)
        {
            MgrDebugLog(QString("向分离式治超端[%1]请求数据").arg(iter->sHttpServerUrl));
            CHttpClient httpclient(m_pWeightDbMgrLog);
            nPostRet = httpclient.PostData(iter->sHttpServerUrl, sPlate, wColor);

            //很有可能是网络超时引起的，总之，就是到对端的连接不通
            if(100 == nPostRet)
            {
                iter->bCanUse = false;
            }
        }
        bPostRet = (bPostRet || (0 == nPostRet));
    }

    if(bPostRet)
    {
        //事件循环结束后，再查一次
        bPostRet = ProcessWeightData(sPlate, wColor, data, false);
    }

    return bPostRet;
}


