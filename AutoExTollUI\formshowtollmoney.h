#ifndef FORMSHOWTOLLMONEY_H
#define FORMSHOWTOLLMONEY_H

#include "baseform.h"

class FormShowTollMoney : public BaseForm
{
    Q_OBJECT
public:
    explicit FormShowTollMoney(QWidget* parent = 0);
    virtual ~FormShowTollMoney();

private:
    QString m_sVlp, m_sVehClassName, m_sVehTypeName, m_sEnStation;
    quint32 m_nMoney;
    //支付前
    bool m_isBeforePay;
    //展示的文字
    QString m_sShowText;
    //二维码
    QString m_sQrCode;
    //支付方式
    CTransPayType m_channelType;

    quint8* m_pQrCode;
    quint32 m_nMaxQrCodeLen;

protected:
    void paintEvent(QPaintEvent*);
    //播放通行费
    void PlayMoneySound();
    //播放支付成功声音
    void PlaySuccessSound();

    //播放支付成功领取电子发票声音
    void PlaySuccessSound_ETicket(quint32 money, CTransPayType channelType);
    // BaseForm interface

    quint8* ReAllocData(int nDataLen);

public:
    void ShowMoney(const QString& vlp, const QString& vehClassName, const QString& vehTypeName,
                   const QString& enStation, quint32 money, bool beforePay,
                   CTransPayType channelType);
    //显示电子发票
    void ShowETicketInfo(const QString& vlp, const QString& vehClassName,
                         const QString& vehTypeName, const QString& enStation, quint32 money,
                         CTransPayType channelType, const QString& qrCode);
};

#endif  // FORMSHOWTOLLMONEY_H
