#include "formoutofservice.h"

FormOutOfService::FormOutOfService(QWidget *parent) : BaseForm(parent) {}

void FormOutOfService::paintEvent(QPaintEvent *)
{
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    On<PERSON><PERSON>ter(painter);

    //绘制文字
    QFont font(g_GlobalUI.m_FontName, g_GlobalUI.oos_FontSize, QFont::Bold);
    font.setLetterSpacing(QFont::AbsoluteSpacing, g_GlobalUI.oos_FontSpace);
    QColor clrText(240, 175, 1);
    painter.setFont(font);
    painter.setPen(clrText);
    QString sText = QString("车道关闭");
    QRect rectLine1 = rectClient;
    rectLine1.adjust(0, 0, 0, -(rectLine1.height() / 2 + g_GlobalUI.oos_LineSpace / 2));
    painter.drawText(rectLine1, Qt::AlignHCenter | Qt::AlignBottom, sText);

    sText = QString("暂停服务");
    QRect rectLine2 = rectClient;
    rectLine2.adjust(0, rectLine2.height() / 2 + g_GlobalUI.oos_LineSpace / 2, 0, 0);
    painter.drawText(rectLine2, Qt::AlignHCenter | Qt::AlignTop, sText);

    //绘制锥桶
    if (g_GlobalUI.oos_IconRect.width() > 0) {
        QPixmap pmIcon;
        pmIcon.load(":/images/cones.png");
        painter.drawPixmap(g_GlobalUI.oos_IconRect, pmIcon.scaled(g_GlobalUI.oos_IconRect.size(), Qt::KeepAspectRatio, Qt::SmoothTransformation));
    }

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}


void FormOutOfService::ShowForm()
{
    BaseForm::ShowForm();
    PlaySound("logout.wav");
}
