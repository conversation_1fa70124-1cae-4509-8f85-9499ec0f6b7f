#include "vehicleaxischeck.h"
#include <QDebug>

VehicleAxisCheck::VehicleAxisCheck(QObject *parent) : QObject(parent)
{
}

VehicleAxisCheck::~VehicleAxisCheck()
{
}

bool VehicleAxisCheck::checkNormalTruckAxis(int scaleAxisCount, int cameraAxisCount, int dbAxisCount)
{
    // 非ETC普通货车轴型校验
    // 地磅轴数a与车型识别设备轴数b一致，且不小于车型库轴数c
    if (scaleAxisCount == cameraAxisCount && scaleAxisCount >= dbAxisCount) {
        return true;
    }
    return false;
}

bool VehicleAxisCheck::checkETCTruckAxis(int scaleAxisCount, int obuAxisCount, int dbAxisCount)
{
    // ETC普通货车轴型校验
    // 地磅轴数a与OBU发行车型对应轴数b一致，且不小于车型库轴数c
    if (scaleAxisCount == obuAxisCount && scaleAxisCount >= dbAxisCount) {
        return true;
    }
    return false;
}

QString VehicleAxisCheck::getVehicleTypeDbInfo(int vehicleType)
{
    QString dbInfo = queryVehicleTypeDb(vehicleType);
    if (dbInfo.isEmpty()) {
        // 如果车型库没有信息，返回空值
        return QString("%1| | | ").arg(vehicleType);
    }
    return dbInfo;
}

int VehicleAxisCheck::calculateVehicleType(int axisCount, int vehicleType)
{
    // 根据轴数计算实际计费车型
    // 货车车型代码：11-一型货车 12-二型货车 13-三型货车 14-四型货车 15-五型货车 16-六型货车
    if (axisCount <= 2) {
        return 11; // 一型货车
    } else if (axisCount == 3) {
        return 12; // 二型货车
    } else if (axisCount == 4) {
        return 13; // 三型货车
    } else if (axisCount == 5) {
        return 14; // 四型货车
    } else if (axisCount == 6) {
        return 15; // 五型货车
    } else {
        return 16; // 六型货车（轴数大于6轴的货车按照货六填写）
    }
}

QString VehicleAxisCheck::queryVehicleTypeDb(int vehicleType)
{
    // TODO: 实现车型库查询逻辑
    // 这里需要根据实际车型库接口实现查询功能
    // 返回格式：车型|车种|轴数|轴型
    return QString();
} 