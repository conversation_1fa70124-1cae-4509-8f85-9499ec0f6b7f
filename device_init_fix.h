 #ifndef DEVICE_INIT_FIX_H
#define DEVICE_INIT_FIX_H

#include <QObject>
#include <QTimer>
#include <QElapsedTimer>
#include <QMutex>
#include <QThread>
#include <QDateTime>
#include <QDebug>

/**
 * @brief 设备初始化修复类
 * @description 为解决AutoToll.exe应用程序挂起问题而设计的设备初始化增强类
 * 
 * 主要功能：
 * 1. 为设备初始化添加超时机制
 * 2. 提供异步初始化能力
 * 3. 增强错误处理和日志记录
 * 4. 实现看门狗监控机制
 * 
 * 使用场景：
 * - 车牌识别设备初始化
 * - IO卡设备初始化
 * - 网络模块初始化
 * - 数据库连接初始化
 * 
 * <AUTHOR>
 * @date 2025-01-21
 * @version 1.0
 */

// 设备类型枚举
enum DeviceType {
    DEVICE_TYPE_VPR = 0,        // 车牌识别设备
    DEVICE_TYPE_IOCARD,         // IO卡设备
    DEVICE_TYPE_CARDREADER,     // 读卡器设备
    DEVICE_TYPE_RSU,            // RSU设备
    DEVICE_TYPE_PRINTER,        // 打印机设备
    DEVICE_TYPE_NETWORK,        // 网络模块
    DEVICE_TYPE_DATABASE,       // 数据库
    DEVICE_TYPE_COUNT
};

// 设备初始化状态
enum DeviceInitStatus {
    DEVICE_INIT_PENDING = 0,    // 待初始化
    DEVICE_INIT_RUNNING,        // 初始化中
    DEVICE_INIT_SUCCESS,        // 初始化成功
    DEVICE_INIT_FAILED,         // 初始化失败
    DEVICE_INIT_TIMEOUT         // 初始化超时
};

// 设备初始化配置结构
struct DeviceInitConfig {
    int timeoutMs;              // 超时时间（毫秒）
    int retryCount;             // 重试次数
    bool skipOnFailure;         // 失败时是否跳过
    bool asyncInit;             // 是否异步初始化
    bool enableWatchdog;        // 是否启用看门狗
    QString deviceName;         // 设备名称
    
    DeviceInitConfig() {
        timeoutMs = 30000;      // 默认30秒超时
        retryCount = 3;         // 默认重试3次
        skipOnFailure = true;   // 默认失败时跳过
        asyncInit = false;      // 默认同步初始化
        enableWatchdog = true;  // 默认启用看门狗
        deviceName = "未知设备";
    }
};

// 设备初始化结果
struct DeviceInitResult {
    DeviceType deviceType;
    DeviceInitStatus status;
    QString errorMessage;
    qint64 initTime;           // 初始化耗时（毫秒）
    
    DeviceInitResult() {
        deviceType = DEVICE_TYPE_COUNT;
        status = DEVICE_INIT_PENDING;
        initTime = 0;
    }
};

/**
 * @brief 设备初始化超时控制类
 * @description 为单个设备初始化提供超时控制
 */
class DeviceInitTimeoutController : public QObject {
    Q_OBJECT
    
public:
    explicit DeviceInitTimeoutController(const DeviceInitConfig& config, QObject *parent = 0);
    virtual ~DeviceInitTimeoutController();
    
    // 开始超时监控
    void startTimeout();
    
    // 停止超时监控
    void stopTimeout();
    
    // 重置超时计时器
    void resetTimeout();
    
signals:
    // 超时信号
    void timeoutOccurred(const QString& deviceName);
    
private slots:
    void onTimeout();
    
private:
    QTimer *m_pTimeoutTimer;    // 超时定时器
    DeviceInitConfig m_config;  // 设备配置
};

/**
 * @brief 异步设备初始化工作线程
 * @description 在独立线程中执行设备初始化操作
 */
class AsyncDeviceInitWorker : public QObject {
    Q_OBJECT
    
public:
    explicit AsyncDeviceInitWorker(DeviceType deviceType, 
                                  const DeviceInitConfig& config,
                                  QObject *parent = 0);
    
public slots:
    // 执行设备初始化
    void performInitialization();
    
signals:
    // 初始化完成信号
    void initializationCompleted(const DeviceInitResult& result);
    
    // 初始化进度信号
    void initializationProgress(DeviceType deviceType, int progress, const QString& message);
    
private:
    DeviceType m_deviceType;
    DeviceInitConfig m_config;
    
    // 具体的设备初始化方法
    bool initVPRDevice(QString& errorMsg);
    bool initIOCardDevice(QString& errorMsg);
    bool initCardReaderDevice(QString& errorMsg);
    bool initRSUDevice(QString& errorMsg);
    bool initPrinterDevice(QString& errorMsg);
    bool initNetworkModule(QString& errorMsg);
    bool initDatabaseConnection(QString& errorMsg);
};

/**
 * @brief 设备初始化管理器
 * @description 统一管理所有设备的初始化过程
 */
class DeviceInitializationManager : public QObject {
    Q_OBJECT
    
public:
    static DeviceInitializationManager* getInstance();
    
    // 注册设备配置
    void registerDeviceConfig(DeviceType deviceType, const DeviceInitConfig& config);
    
    // 同步初始化所有设备
    bool initializeAllDevices(QString& errorMsg);
    
    // 异步初始化所有设备
    void initializeAllDevicesAsync();
    
    // 初始化单个设备
    bool initializeDevice(DeviceType deviceType, QString& errorMsg);
    
    // 获取设备初始化状态
    DeviceInitStatus getDeviceStatus(DeviceType deviceType) const;
    
    // 获取设备初始化结果
    DeviceInitResult getDeviceResult(DeviceType deviceType) const;
    
    // 是否所有设备初始化完成
    bool isAllDevicesInitialized() const;
    
    // 获取已成功初始化的设备数量
    int getSuccessfulDeviceCount() const;
    
    // 重置所有设备状态
    void resetAllDevices();
    
signals:
    // 所有设备初始化完成信号
    void allDevicesInitialized(bool success, const QString& summary);
    
    // 单个设备初始化完成信号
    void deviceInitialized(const DeviceInitResult& result);
    
    // 初始化进度信号
    void initializationProgress(int totalDevices, int completedDevices, const QString& currentDevice);
    
private slots:
    void onDeviceInitCompleted(const DeviceInitResult& result);
    void onDeviceInitProgress(DeviceType deviceType, int progress, const QString& message);
    void onDeviceTimeout(const QString& deviceName);
    
private:
    explicit DeviceInitializationManager(QObject *parent = 0);
    ~DeviceInitializationManager();
    
    static DeviceInitializationManager* s_instance;
    static QMutex s_mutex;
    
    QMap<DeviceType, DeviceInitConfig> m_deviceConfigs;
    QMap<DeviceType, DeviceInitResult> m_deviceResults;
    QMap<DeviceType, DeviceInitTimeoutController*> m_timeoutControllers;
    QMap<DeviceType, QThread*> m_workerThreads;
    QMap<DeviceType, AsyncDeviceInitWorker*> m_workers;
    
    QMutex m_resultMutex;
    
    // 默认设备配置
    void setupDefaultConfigs();
    
    // 清理资源
    void cleanup();
};

/**
 * @brief 应用程序看门狗类
 * @description 监控应用程序主线程响应状态，防止挂起
 */
class ApplicationWatchdog : public QObject {
    Q_OBJECT
    
public:
    static ApplicationWatchdog* getInstance();
    
    // 启动看门狗
    void startWatchdog(int checkIntervalMs = 5000, int timeoutMs = 30000);
    
    // 停止看门狗
    void stopWatchdog();
    
    // 更新心跳
    void updateHeartbeat();
    
    // 设置自动重启
    void setAutoRestart(bool enable);
    
signals:
    // 应用程序挂起信号
    void applicationHangDetected(qint64 lastHeartbeatTime);
    
    // 准备重启信号
    void preparingRestart();
    
private slots:
    void checkHeartbeat();
    
private:
    explicit ApplicationWatchdog(QObject *parent = 0);
    ~ApplicationWatchdog();
    
    static ApplicationWatchdog* s_instance;
    static QMutex s_mutex;
    
    QTimer *m_pWatchdogTimer;
    qint64 m_lastHeartbeat;
    int m_timeoutMs;
    bool m_autoRestart;
    bool m_isActive;
};

/**
 * @brief 详细日志记录器
 * @description 提供设备初始化过程的详细日志记录
 */
class DetailedDeviceLogger {
public:
    // 记录设备初始化开始
    static void logInitStart(DeviceType deviceType, const QString& deviceName);
    
    // 记录设备初始化步骤
    static void logInitStep(DeviceType deviceType, const QString& deviceName, 
                           int step, const QString& message);
    
    // 记录设备初始化完成
    static void logInitComplete(DeviceType deviceType, const QString& deviceName, 
                               bool success, qint64 elapsedTime, const QString& result);
    
    // 记录设备初始化错误
    static void logInitError(DeviceType deviceType, const QString& deviceName, 
                            const QString& errorMessage);
    
    // 记录设备初始化超时
    static void logInitTimeout(DeviceType deviceType, const QString& deviceName, 
                              int timeoutMs);
    
    // 生成初始化摘要报告
    static QString generateInitSummary(const QMap<DeviceType, DeviceInitResult>& results);
    
private:
    // 获取设备类型名称
    static QString getDeviceTypeName(DeviceType deviceType);
    
    // 获取当前时间戳字符串（北京时间）
    static QString getCurrentTimestamp();
    
    // 写入设备专用日志文件
    static void writeToDeviceLog(const QString& message);
    
    // 写入系统日志
    static void writeToSystemLog(const QString& message);
};

// 全局配置辅助函数
namespace DeviceConfigHelper {
    // 获取VPR设备配置
    DeviceInitConfig getVPRConfig();
    
    // 获取IO卡设备配置  
    DeviceInitConfig getIOCardConfig();
    
    // 获取读卡器设备配置
    DeviceInitConfig getCardReaderConfig();
    
    // 获取RSU设备配置
    DeviceInitConfig getRSUConfig();
    
    // 获取打印机设备配置
    DeviceInitConfig getPrinterConfig();
    
    // 获取网络模块配置
    DeviceInitConfig getNetworkConfig();
    
    // 获取数据库配置
    DeviceInitConfig getDatabaseConfig();
}

#endif // DEVICE_INIT_FIX_H