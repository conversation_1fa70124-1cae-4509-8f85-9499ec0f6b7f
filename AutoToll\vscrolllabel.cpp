#include "vscrolllabel.h"

#include <QPainter>
#include <QTimerEvent>
#include <QTimer>
#include <QTextCodec>
#include <QDebug>

#define FONT_NAME "微软雅黑"

const int SCROLL_SPEED = 50;
const int SCROLL_FONT_PIXELSIZE = 10;

VScrollLabel::VScrollLabel(QWidget *parent, Qt::WindowFlags f) :
    QLabel(parent,f)
{
    m_bChanged = false;
    m_nOffset = m_nTimerID = m_nTextHeight = m_nCurHeight = 0;

    m_nSize = SCROLL_FONT_PIXELSIZE;             //字体大小
    m_nCharSet = 134;       //字符集,  0 西方  134 汉字
    m_nColor = 0;          //字体颜色
    m_nBKColor = 0;     //背景颜色
    m_bBold = false;            //字形 0-常规，1-粗体
    m_bUnderLine = false;
    m_bRollShow = 0;    //显示方式	显示方式 0-滚动，1-固定
    m_sFont = FONT_NAME;
}

void VScrollLabel::setText(const QString &curText)
{
    QFont font;
    font.setFamily(m_sFont);          //字体
    font.setPointSize(m_nSize);         //字体大小
    font.setBold(m_bBold);              //加粗
    font.setUnderline(m_bUnderLine);    //下划线
    setStyleSheet(QString(/*"border-width: 2px;border-style: solid;border-color: rgb(255, 170, 0);"*/
                          "background-color: rgb(%1, %2, %3);color:rgb(%4, %5, %6);")
                  .arg(m_nBKColor & 0xFF).arg((m_nBKColor >> 8) & 0xFF).arg((m_nBKColor >> 16) & 0xFF)
                  .arg(m_nColor & 0xFF).arg((m_nColor >> 8) & 0xFF).arg((m_nColor >> 16) & 0xFF));
    setFont(font);

    getHeight(curText);
}

// 开始启动定时器 设为 50
void VScrollLabel::startScroll()
{
    m_nTimerID = startTimer(SCROLL_SPEED);
}

//测试用
void VScrollLabel::metrics()
{
//    m_nOffset = 0;

//    // getHeight + QLabel高度是为了 一段过长的文字显示完后再重新开始滚动显示
//    // 不然的话第一行会紧接着末尾显示出来。
////    m_nTextHeight = getHeight() +height() ;
//    QTimer::singleShot(500, this, SLOT(startScroll()));
//    this->update();
    setText(QString("观自在菩萨，行深般若波罗蜜多时，照见五蕴皆空，度一切苦厄。\
                    舍利子，色不异空，空不异色，色即是空，空即是色，受想行识，亦复如是。\
                    舍利子，是诸法空相，不生不灭，不垢不净，不增不减。\
                    是故空中无色，无受想行识，无眼耳鼻舌身意，无色声香味触法，无眼界，乃至无意识界；\
                    无无明，亦无无明尽，乃至无老死，亦无老死尽。无苦集灭道，无智亦无得，以无所得故。\
                    菩提萨埵，依般若波罗蜜多故，心无挂该；无挂碍故，无有恐怖，远离颠倒梦想，究竟涅槃；\
                    三世诸佛，依般若波罗蜜多故，得阿耨多罗三藐三菩提。\
                    故知般若波罗蜜多，是大神咒，是大明咒，是无上咒，是不等等咒，能除一切苦，真实不虚；\
                    故说般若波罗密多咒。即说咒曰：揭谛揭谛，波罗揭谛，波罗僧揭谛，菩提萨婆诃"));

}

// 这里是重点，我们得到的一段信息有没有换行符号，要自动换行
// 这就要得到字体的高度和宽度来处理这一段文字，处理完后调用
// setText() 设置到Qlabel 上面去。
void VScrollLabel::getHeight(const QString &sText)
{
    QFontMetrics fm = fontMetrics();

    int nRowCount = 1;
    int nCurrRowWidth = 0;
    int nScreenWidth = width();

    m_sNewText.clear();
    for(int i = 0; i < sText.size(); i++)
    {
        QChar szText = sText.at(i);

        if(szText == QChar('\n'))
        {
            nRowCount += 1;
            nCurrRowWidth = 0;

            m_sNewText.append("\n");
            continue;
        }
        int nWordWidth = fm.width(szText);

        nCurrRowWidth += nWordWidth;
        if(nCurrRowWidth >= nScreenWidth)
        {
            m_sNewText.append("\n");
            m_sNewText.append(szText);

            nRowCount += 1;
            nCurrRowWidth = nWordWidth;
            continue;
        }

        m_sNewText.append(szText);
    }

    m_nNewTextHeight = nRowCount * fm.height() ;
    m_bChanged = true;
}

void VScrollLabel::SetLabelAttrbute(qint32 nSize, qint32 nCharSet, qint32 nColor, qint32 nBKColor,
                                    bool bBold, bool bUnderLine, bool bShowWay, QString sFont)
{
    m_nSize             = nSize;
    m_nCharSet       = nCharSet;
    m_nColor          = nColor;
    m_nBKColor     = nBKColor;
    m_bBold           = bBold;
    m_bUnderLine  = bUnderLine;
    m_bRollShow   = bShowWay;
    m_sFont = sFont;
}

// 显示我们就启动定时器
void VScrollLabel::showEvent(QShowEvent *)
{
    m_nCurHeight = height();
    m_nTimerID = startTimer(SCROLL_SPEED);
}

void VScrollLabel::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    QPainter painter(this);

    if(m_bRollShow)
    {
        painter.drawText(0, m_nCurHeight, width(), m_nTextHeight,  Qt::AlignLeft | Qt::AlignTop, m_sShowText);
    }
    else
    {
        painter.drawText(0, 0, width(), m_nTextHeight,  Qt::AlignLeft | Qt::AlignTop, m_sShowText);
    }
}

void VScrollLabel::timerEvent(QTimerEvent *event)
{
    if (event->timerId() == m_nTimerID) {
        if (m_nCurHeight <= -m_nTextHeight)
        {
            if (m_bChanged)
            {
                m_nTextHeight = m_nNewTextHeight;
                m_sShowText = m_sNewText;
                m_bChanged = false;
            }
            m_nCurHeight = height();
        } else
        {
            m_nCurHeight--;
        }
        update();
    } else {
        QWidget::timerEvent(event);
    }
}

// 隐藏我们就停止掉 定时器
void VScrollLabel::hideEvent(QHideEvent *)
{
    m_nCurHeight = 0;
    if (m_nTimerID) killTimer(m_nTimerID);
}
