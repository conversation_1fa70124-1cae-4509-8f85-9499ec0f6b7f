#include "unloginopverifyfm.h"

#include "abstractstate.h"
#include "etclanectrl.h"
#include "laneinfo.h"
#include "messagedialog.h"
#include "ui_unloginopverifyfm.h"

CUnloginOpVerifyFm::CUnloginOpVerifyFm(QWidget *parent)
    : CBaseOpWidget(parent), ui(new Ui::CUnloginOpVerify)
{
    ui->setupUi(this);
    m_nPswErrorNum = 0;
    ui->lblInfo->clear();
    ui->lblInfo->setAlignment(Qt::AlignCenter);
    this->hide();
    filterChildrenKeyEvent();
}

CUnloginOpVerifyFm::~CUnloginOpVerifyFm()
{
    m_nPswErrorNum = 0;
    delete ui;
}

void CUnloginOpVerifyFm::closeEvent(QCloseEvent *event)
{
    CAbstractState *pState = CAbstractState::GetCurState();
    if (pState && pState->GetCurStateId() == CAbstractState::StateID_UnLogin) {
        event->ignore();
        return;
    }
}

bool CUnloginOpVerifyFm::OnOpenCardEvent(int nReadId, int nCardType)
{
    Q_UNUSED(nCardType)

    return true;
}

// 清空密码和提示信息
void CUnloginOpVerifyFm::ToLoginDlg()
{
    m_nPswErrorNum = 0;
    ui->lblInfo->clear();
    ui->lineEdit->clear();
    ShowErrorMsg("密码错误，请重新输入");
    this->setModalResult(0);
}

//密码校验
bool CUnloginOpVerifyFm::OperVerifyPassword(QString strPassword, QString &sError)
{
    if (Ptr_Info->IsETCLane()) {
        if (strPassword == "11111111") {
            return true;
        }

        sError = QString("密码错误，请重新输入");
        ++m_nPswErrorNum;
        return false;
    }
    // 正式
    m_LoginOperInfo = Ptr_ETCCtrl->GetOperInfo();
    COperTable *pTable = (COperTable *)CParamFileMgr::GetParamFile(cfOper);
    COperInfo operInfo;
    bool bRet = pTable->VerifyOperatorInput(Ptr_Info->GetStationID(), m_LoginOperInfo.dwOper,
                                            strPassword, operInfo, sError);
    if (!bRet) {
        sError = QString("密码错误，请重新输入");
        ++m_nPswErrorNum;
        return false;
    } else {
        QCryptographicHash Hash(QCryptographicHash::Md5);
        Hash.addData(strPassword.toLocal8Bit());
        QByteArray HashResult = Hash.result();
        QString sResult = HashResult.toHex().toUpper();
        if (m_LoginOperInfo.sPassword == sResult) return true;
    }

    return false;
}

int CUnloginOpVerifyFm::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    mtcKeyEvent->setKeyType(KC_Func);
    if (mtcKeyEvent->func() == KeyConfirm) {
        QString sError;
        QString sPassword = ui->lineEdit->text();
#ifdef QT_DEBUG
        if (true)
#else
        if (OperVerifyPassword(sPassword, sError))
#endif
        {
            this->setModalResult(1);
            return 0;
        } else {
            ui->lblInfo->setText(sError);
            ui->lineEdit->setText("");
            if (m_nPswErrorNum >= 3) {
                ToLoginDlg();
            }
        }
        return 0;
    }

    if (mtcKeyEvent->func() == KeyDel) {
        mtcKeyEvent->setKeyType(KC_Func);
        QString sPassword = ui->lineEdit->text().trimmed();
        if (sPassword.length() > 0) {
            QString strTemp;
            strTemp = sPassword.left(sPassword.length() - 1);
            ui->lineEdit->setText(strTemp);
        }
        return 0;
    }

    if (mtcKeyEvent->isNumKey()) {
        mtcKeyEvent->setKeyType(KC_Number);
        ui->lineEdit->setText(ui->lineEdit->text().trimmed() +
                              QString::number(mtcKeyEvent->key() - 48));
    }

    if (mtcKeyEvent->func() == KeyEsc) {
        ToLoginDlg();
    }

    return 0;
}
