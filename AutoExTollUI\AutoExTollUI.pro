#-------------------------------------------------
#
# Project created by QtCreator 2021-08-20T14:45:46
#
#-------------------------------------------------

QT       += core gui
QT       += network
QT       += sql
QT       += phonon

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets


TEMPLATE = app

INCLUDEPATH += . \
                ../common \
                ../log4qt \

SOURCES += main.cpp\
        mainwindow.cpp \
    globalui.cpp \
    uiserver.cpp \
    parammgr.cpp \
    ../common/globalutils.cpp \
    formwaitvehicle.cpp \
    forminsertcard.cpp \
    formshowtollmoney.cpp \
    formshowinvoice.cpp \
    formshowprompt.cpp \
    formoutofservice.cpp \
    baseform.cpp \
    ../common/wavfile.cpp

HEADERS  += mainwindow.h \
    globalui.h \
    uiserver.h \
    parammgr.h \
    ../common/globalutils.h \
    uimessage.h \
    formwaitvehicle.h \
    forminsertcard.h \
    formshowtollmoney.h \
    formshowinvoice.h \
    formshowprompt.h \
    formoutofservice.h \
    baseform.h \
    ../common/wavfile.h

FORMS    += \
    mainwindow.ui


include  (../log4qt/log4qt.pri)


win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../quazip-0.7.1/lib/ -lquazip
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../quazip-0.7.1/lib/ -lquazipd

INCLUDEPATH += $$PWD/../quazip-0.7.1/quazip
DEPENDPATH += $$PWD/../quazip-0.7.1/quazip
INCLUDEPATH += $$PWD/../zlib1.2.8/include



CONFIG(debug, debug|release) {
    TARGET = AutoExTollUI_debug
#    OBJECTS_DIR +=../AutoExTollUI_obj_debug
}else{
    TARGET = AutoExTollUI
#    OBJECTS_DIR +=../AutoExTollUI_obj
}

win32:DESTDIR +=../bin
unix:DESTDIR +=../bin-linux

DISTFILES +=

RESOURCES += \
    images/images.qrc


