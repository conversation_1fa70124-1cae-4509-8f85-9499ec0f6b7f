#include "formwaitvehicle.h"

#include "globalutils.h"

FormWaitVehicle::FormWaitVehicle(QWidget *parent) : BaseForm(parent) {}

void FormWaitVehicle::paintEvent(QPaintEvent *)
{
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    On<PERSON><PERSON>ter(painter);
    //绘制圆形框
    painter.setBrush(QColor(44, 54, 67));
    painter.drawEllipse(g_GlobalUI.waitveh_roundRect.left(), g_GlobalUI.waitveh_roundRect.top(), g_GlobalUI.waitveh_roundRect.width(),
                        g_GlobalUI.waitveh_roundRect.height());
    //绘制圆形图片
    QRect rectImage = g_GlobalUI.waitveh_roundRect;
    int nMargin = rectImage.width() / 10;
    rectImage.adjust(nMargin, nMargin, -nMargin, -nMargin);
    QPixmap image(GetCurrentPath() + "/images/selfservice.png");
    QPixmap roundImage = generatePixmap(image, rectImage.width() / 2);
    painter.drawPixmap(rectImage, roundImage);
    //绘制文字

    QFont font(g_GlobalUI.m_FontName, g_GlobalUI.waitveh_FontSize, QFont::Bold);
    font.setLetterSpacing(QFont::AbsoluteSpacing, g_GlobalUI.waitveh_FontSpace);
    QColor clrText(240, 175, 1);
    painter.setFont(font);
    painter.setPen(clrText);
    QString sText = QString("自助缴费");
    QRect rectLine1 = rectClient;
    rectLine1.adjust(rectImage.right(), 0, 0, -(rectLine1.height() / 2 + g_GlobalUI.waitveh_LineSpace / 2));
    painter.drawText(rectLine1, Qt::AlignHCenter | Qt::AlignBottom, sText);

    sText = QString("为您服务");
    QRect rectLine2 = rectClient;
    rectLine2.adjust(rectImage.right(), rectLine2.height() / 2 + g_GlobalUI.waitveh_LineSpace / 2, 0, 0);
    painter.drawText(rectLine2, Qt::AlignHCenter | Qt::AlignTop, sText);

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

void FormWaitVehicle::Login()
{
    BaseForm::ShowForm();
    PlaySound("login.wav");
}
