#ifndef UI_SPLASHSCREEN_H
#define UI_SPLASHSCREEN_H

#include <QSplashScreen>
#include <QLabel>
#include <QDateTime>
//#include "timeproofread.h"

//启动界面提示显示
class Ui_SplashScreen : public QSplashScreen
{
    Q_OBJECT
public:
    explicit Ui_SplashScreen(QWidget *parent = 0);
public slots:
    void showMessage(const QString message);
    void showTime(QDateTime &dt);
private:
    QLabel lbl_text;
    //TimeProofread dlg_time;
};

#endif // UI_SPLASHSCREEN_H
