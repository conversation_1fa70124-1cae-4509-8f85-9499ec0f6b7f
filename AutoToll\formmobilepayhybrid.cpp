#include "formmobilepayhybrid.h"
#include "qxtjson.h"
#include "globalui.h"
#include "dlgmain.h"
#include "jxlanetype.h"
#include "log4qt/ilogmsg.h"
#include "devicefactory.h"
#include "messagebox.h"

// 设置等待支付的时间
#ifdef QT_DEBUG
#define WAIT_PAY_SECOND (30)
#else
#define WAIT_PAY_SECOND (90)
#endif

// 静态成员初始化
QLibrary FormMobilePayHybrid::m_hLibModule;
bool FormMobilePayHybrid::m_bDriverLoaded = false;
IF_InitEnvironment FormMobilePayHybrid::m_pIF_InitEnvironment = NULL;
IF_Destroy FormMobilePayHybrid::m_pIF_Destroy = NULL;
IF_DebitMoney FormMobilePayHybrid::m_pIF_DebitMoney = NULL;
IF_DebitCancel FormMobilePayHybrid::m_pIF_DebitCancel = NULL;
IF_GetDebitResult FormMobilePayHybrid::m_pIF_GetDebitResult = NULL;

FormMobilePayHybrid::FormMobilePayHybrid(QWidget *parent) : FormMobilePayBase(parent)
{
    m_payStep = PS_Waiting;
    m_nWaitPaySec = 0;
    m_pTmQryOrder = new QTimer(this);
    m_pTmReverse = new QTimer(this);
    m_nReverseTimes = 0;
    m_pCurTransInfo = NULL;
    m_nChannelType = 0;
    m_bInited = false;

    connect(&m_WaitPayTimer, SIGNAL(timeout()), this, SLOT(OnWaitPayTimer()));
    connect(m_pTmQryOrder, SIGNAL(timeout()), this, SLOT(OnQryOrderTimer()));
    connect(m_pTmReverse, SIGNAL(timeout()), this, SLOT(OnReverseTimer()));

    // 加载驱动
    if (!m_bDriverLoaded) {
        LoadDriver();
    }

    // 初始化环境
    if (m_bDriverLoaded) {
        // 初始化环境参数，这里填入实际配置
        QString sAreaId = "36"; // 区域ID
        QString sStationId = QString::number(Ptr_Info->GetStationID()); // 站点ID
        qint32 nLaneId = Ptr_Info->GetLaneID(); // 车道ID
        qint32 iProvinceID = 36; // 省份ID

        InitEnViroment(QApplication::applicationPid(), sAreaId, sStationId, nLaneId, iProvinceID);
    }
}

FormMobilePayHybrid::~FormMobilePayHybrid()
{
    if (m_pTmQryOrder)
    {
        m_pTmQryOrder->stop();
        delete m_pTmQryOrder;
    }

    if (m_pTmReverse)
    {
        m_pTmReverse->stop();
        delete m_pTmReverse;
    }

    // 销毁环境
    DestroyEnViroment();
}

bool FormMobilePayHybrid::LoadDriver()
{
    if (m_bDriverLoaded) return true;

    QString sDriverPath = GetCurrentPath() + "TWSM.dll";
    m_hLibModule.setFileName(sDriverPath);

    if (!m_hLibModule.load()) {
        ErrorLog(QString("加载动态库%1失败").arg(sDriverPath));
        return false;
    }

    m_pIF_InitEnvironment = (IF_InitEnvironment)m_hLibModule.resolve("IF_InitEnvironment");
    m_pIF_Destroy = (IF_Destroy)m_hLibModule.resolve("IF_Destroy");
    m_pIF_DebitMoney = (IF_DebitMoney)m_hLibModule.resolve("IF_DebitMoney");
    m_pIF_DebitCancel = (IF_DebitCancel)m_hLibModule.resolve("IF_DebitCancel");
    m_pIF_GetDebitResult = (IF_GetDebitResult)m_hLibModule.resolve("IF_GetDebitResult");

    if (!m_pIF_InitEnvironment || !m_pIF_Destroy || !m_pIF_DebitMoney ||
        !m_pIF_DebitCancel || !m_pIF_GetDebitResult) {
        ErrorLog("加载动态库函数失败");
        m_hLibModule.unload();
        return false;
    }

    m_bDriverLoaded = true;
    DebugLog("加载动态库成功");
    return true;
}

void FormMobilePayHybrid::ReleaseDriver()
{
    if (m_bDriverLoaded) {
        m_pIF_InitEnvironment = NULL;
        m_pIF_Destroy = NULL;
        m_pIF_DebitMoney = NULL;
        m_pIF_DebitCancel = NULL;
        m_pIF_GetDebitResult = NULL;

        m_hLibModule.unload();
        m_bDriverLoaded = false;
    }
}

bool FormMobilePayHybrid::InitEnViroment(quint32 nThreadID, const QString &sAreaId,
                                        const QString &sStationId, qint32 nLaneId, qint32 iProvinceID)
{
    if (!m_bDriverLoaded) {
        ErrorLog("驱动未加载，无法初始化环境");
        return false;
    }

    if (m_bInited) {
        DebugLog("环境已初始化，不重复初始化");
        return true;
    }

    HWND hWnd = (HWND)this->winId();
    int nRet = m_pIF_InitEnvironment(hWnd, nThreadID, sAreaId.toUtf8().data(),
                                    sStationId.toUtf8().data(), nLaneId, iProvinceID);

    if (0 == nRet) {
        m_bInited = true;
        DebugLog("初始化环境成功");
        return true;
    } else {
        ErrorLog(QString("初始化环境失败，返回码：%1").arg(nRet));
        return false;
    }
}

void FormMobilePayHybrid::DestroyEnViroment()
{
    if (!m_bDriverLoaded || !m_bInited) return;

    if (m_pIF_Destroy) {
        int nRet = m_pIF_Destroy();
        if (0 == nRet) {
            DebugLog("销毁环境成功");
        } else {
            ErrorLog(QString("销毁环境失败，返回码：%1").arg(nRet));
        }
    }

    m_bInited = false;
}

void FormMobilePayHybrid::OnResultCallBack(qint32 option, qint32 result, QString sJson)
{
    QString str = QString("移动支付终端支付返回:%1,%2,%3").arg(option).arg(result).arg(sJson);
    DebugLog(str);

    enum {
        Option_debitResp = 1,   // 扣费回馈
        Option_revocationResp   // 撤单回馈
    };

    enum {
        event_consumeOk = 0,     // 扣费成功
        event_inputtingPwd = 1,  // 正在输入密码
        event_noBalance = 2,     // 余额不足
        event_qrCodeError = 3,   // 二维码错误
        event_qrCodeOutTime = 4, // 超时
        event_userInvalid = 5,
        event_consumeTimeOut = 6,
        event_pwdError = 7,
        event_paramError = 8,
        event_lastTransNotOver = 9,
        event_enviromentNotInited = 10,
        event_enviromentNoAuth = 11,
        event_payNetError = 12,
        event_HaveTrans = 13     // 已获取支付凭证
    };

    switch (option) {
        case Option_debitResp: {
            if (result == event_consumeOk) {
                m_WaitPayTimer.stop();
                m_payStep = PS_PaySuccess;

                // 扣费成功，取扣费结果
                QxtJSON jsonParser;
                QVariant varData;
                if (sJson.length() > 0) varData = jsonParser.parse(sJson);
                m_payResult.Clear();
                bool bRlt = ProcessRespons_DebitResut(result, varData, m_payResult);
                OnPaySuccess();
                return;
            } else if (result == event_inputtingPwd) {
                // 等待输入密码
                UpdateHelpMsg(QString("正在处理支付...\n等待用户输入密码"));
                m_payStep = PS_InputPassword;
                return;
            } else {
                // 支付失败
                m_WaitPayTimer.stop();
                m_payStep = PS_PayFail;
                UpdateHelpMsg(QString("支付失败!\n按【银联键】重新处理"));
                // 取消订单
                ReverseOrderOnce();
            }
            break;
        }
        case Option_revocationResp: {
            if (0 == result) {
                DebugLog("撤单成功");
                UpdateHelpMsg(QString("支付失败，已撤单!\n按【银联键】重新处理"));
            } else {
                // 撤单失败处理
                DebugLog(QString("撤单失败,返回:%1").arg(result));
            }
            UpdateHelpMsg(QString("支付失败，已撤单!\n按【银联键】重新处理"));
            m_WaitPayTimer.stop();
            m_payStep = PS_PayFail;
            break;
        }
        default: {
            ErrorLog("返回异常");
            break;
        }
    }
}

bool FormMobilePayHybrid::ProcessRespons_DebitResut(qint32 nResult, QVariant &varData, CPayResult &payResult)
{
    enum { event_consumeOk = 0 };

    payResult.ExecuteCode = nResult;
    if (event_consumeOk == nResult) {
        payResult.ExecuteDesc = QString("交易成功");
    } else {
        payResult.ExecuteDesc = GetDebitFaildReason(nResult);
    }

    if (event_consumeOk == nResult) {
        payResult.ResultType = 1;
    } else {
        payResult.ResultType = 0;
    }

    // 解析JSON数据
    QVariantMap map = varData.toMap();
    if (map.contains("DebitOrder")) {
        payResult.DebitOrder = map.value("DebitOrder").toString();
    }

    if (map.contains("DebitTime")) {
        payResult.DebitTime = map.value("DebitTime").toString();
    }

    if (map.contains("ConsumeTime")) {
        payResult.ConsumeTime = map.value("ConsumeTime").toString().toUInt();
    }

    if (map.contains("PayPlatformType")) {
        payResult.PayPlatformType = map.value("PayPlatformType").toString().toInt();
    }

    if (map.contains("PayIdentifier")) {
        payResult.PayIdentifier = map.value("PayIdentifier").toString();
    }

    if (map.contains("TradeSecurityCode")) {
        payResult.TradeSecurityCode = map.value("TradeSecurityCode").toString();
    }

    return true;
}

QString FormMobilePayHybrid::GetDebitFaildReason(qint32 nResult)
{
    switch (nResult) {
        case 0: return QString("交易成功");
        case 1: return QString("正在输入密码");
        case 2: return QString("余额不足");
        case 3: return QString("二维码错误");
        case 4: return QString("超时");
        case 5: return QString("用户无效");
        case 6: return QString("支付超时");
        case 7: return QString("密码错误");
        case 8: return QString("参数错误");
        case 9: return QString("上笔交易未结束");
        case 10: return QString("环境未初始化");
        case 11: return QString("环境未授权");
        case 12: return QString("支付网络错误");
        case 13: return QString("已获取支付凭证");
        default: return QString("未知错误");
    }
}

void FormMobilePayHybrid::OnScanResultEvent(QString qrCode)
{
    if (qrCode.isEmpty())
        return;

    if (m_payStep == PS_PaySuccess || m_payStep == PS_PayFail)
        return;

    if (m_payStep == PS_Waiting) {
        m_payStep = PS_Paying;
        // 显示正在处理
        UpdateHelpMsg(QString("正在处理支付...\n等待支付结果"));

        // 由于有了扫码结果，可以直接发起支付
        BeginPay();
    }
}

void FormMobilePayHybrid::OnWaitPayTimer()
{
    m_nWaitPaySec--;
    if (m_payStep == PS_PaySuccess || m_payStep == PS_PayFail) {
        // 支付已完成，停止计时
        m_WaitPayTimer.stop();
        return;
    }

    if (m_nWaitPaySec <= 0) {
        // 超时处理
        m_WaitPayTimer.stop();
        m_payStep = PS_PayFail;
        ReverseOrder();
        UpdateHelpMsg(QString("支付失败，已超时!\n按【银联键】重新处理"));
        return;
    }

    if (m_payStep == PS_Paying) {
        UpdateHelpMsg(QString("正在处理支付...%1\n\n等待用户出示付款码").arg(m_nWaitPaySec));
    } else if (m_payStep == PS_InputPassword) {
        UpdateHelpMsg(QString("正在处理支付...%1\n\n等待用户输入密码").arg(m_nWaitPaySec));
    }
}

bool FormMobilePayHybrid::Pay(CTransPayType &payType, FormMobilePayBase::PayClass payClass, CTransInfo *pTransInfo)
{
    m_payClass = payClass;
    InitUI();

    if (!pTransInfo) return false;
    m_pCurTransInfo = pTransInfo;

    // 预处理支付信息
    PreparePayInfo(pTransInfo);

    // 连接扫码信号
    if (Ptr_Info->bHaveCardMgr()) {
        connect(CDeviceFactory::GetPayMentMgr(), SIGNAL(NotifyScanResultEvent(QString)), this,
               SLOT(OnScanResultEvent(QString)));
    }

    // 开始支付处理
    opBeginPayTimer(true, 2000);

    // 远程控制接口
    Ptr_RemoteCtrl->BeginPaying(pTransInfo->GetNeedPayMoney(), this, SLOT(OnRemoteStopPay()));

    // 显示界面，处理交互
    int nRlt = doModalShow();

    // 结束远程控制
    Ptr_RemoteCtrl->EndPaying(this, SLOT(OnRemoteStopPay()));

    // 结束计时器
    opBeginPayTimer(false);

    // 移动支付相关清理
    bool bMobilePay = payClass == PayClass_All || payClass == PayClass_Mobile;
    if (bMobilePay && Ptr_Info->bHaveCardMgr()) {
        DebugLog("关闭扫码头");
        CDeviceFactory::GetPayMentMgr()->CloseAllScan();
        disconnect(CDeviceFactory::GetPayMentMgr(), SIGNAL(NotifyScanResultEvent(QString)), this,
                 SLOT(OnScanResultEvent(QString)));
    }

    // 处理结果
    if (CBaseOpWidget::Rlt_OK == nRlt) {
        // 获取支付结果
        payType = m_payType;
        if (payType == TransPT_WeChat || payType == TransPT_AliPay || payType == TransPT_Union || payType == TransPT_Other) {
            pTransInfo->m_payCode = m_sPayId;
            pTransInfo->m_payOrderNum = m_payResult.DebitOrder;
            pTransInfo->m_nPayChannel = m_nChannelType;
        }
        return true;
    } else {
        return false;
    }
}

bool FormMobilePayHybrid::PreparePayInfo(CTransInfo *pTransInfo)
{
    if (!pTransInfo) return false;

    // 记录外屏显示信息
    m_sExvlp = QString("%1%2")
        .arg(GetVehPlateColorName((VP_COLOR)pTransInfo->VehInfo.nVehPlateColor))
        .arg(GB2312toUnicode(pTransInfo->VehInfo.szVehPlate));
    m_sExVehClassName = GetVehClassName(pTransInfo->VehInfo.VehClass);
    m_sExVehTypeName = GetUnionVehTypeName(pTransInfo->VehInfo.GBVehType);
    m_sEnStation = pTransInfo->vehEntryInfo.sEnStaionName;

    // 生成支付标识
    m_sPayId = GetUUID().toUpper();

    // 通知外屏显示费用
    if (Ptr_Info->bHaveCardMgr()) {
        CDeviceFactory::GetAutoExTollScreen()->ShowTollMoney(
            m_sExvlp, m_sExVehClassName, m_sExVehTypeName, m_sEnStation,
            pTransInfo->GetNeedPayMoney(), false, TransPT_AliPay);
    }

    // 准备扣款参数
    QDateTime debitTime = QDateTime::currentDateTime();

    // ---------- 扣款信息区开始-------
    m_debit.Money = pTransInfo->GetNeedPayMoney();
#ifdef QT_DEBUG
    m_debit.Money = 1;
#endif
    m_debit.SubTime = debitTime.toString("yyyy-MM-dd hh:mm:ss");
    m_debit.Type = 1; // 封闭路段出口扣款
    m_debit.PayIdentifier = m_sPayId;

    //--------车辆信息区开始--------
    m_vehicle.Type = pTransInfo->VehInfo.GBVehType;
    m_vehicle.Class = pTransInfo->VehInfo.VehClass;
    QString sPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
    m_vehicle.License = sPlate;
    m_vehicle.VLColor = pTransInfo->VehInfo.nVehPlateColor;
    m_vehicle.AxisCount = pTransInfo->GetVehAxisNum();
    m_vehicle.Weight = pTransInfo->vehEntryInfo.dwTotalWeight;
    m_vehicle.LimitWeight = pTransInfo->vehEntryInfo.dwWeightLimit;

    // ------机器识别车牌区
    QString sAutoPlate = GB2312toUnicode(pTransInfo->VehInfo.szAutoVehPlate);
    m_auPlate.AutoLicense = sAutoPlate;
    m_auPlate.AutoColor = pTransInfo->VehInfo.nAutoVehPlateColor;

    //-----业务信息区开始---------
    if (pTransInfo->mediaType == MediaType_CPC) {
        m_operation.PassCertificateType = 2;
        m_operation.CardID = QString::fromAscii(pTransInfo->cpcIccInfo.cpcBasicInfo.sCardID);
    } else if (pTransInfo->mediaType == MediaType_OBU) {
        m_operation.PassCertificateType = 3;
        m_operation.CardID = QString::fromAscii(pTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
    } else if (pTransInfo->mediaType == MediaType_Paper) {
        m_operation.PassCertificateType = 4;
        m_operation.CardID = pTransInfo->m_sPaperId;
    } else {
        m_operation.PassCertificateType = 2;
        m_operation.CardID = QString::fromAscii(pTransInfo->cpcIccInfo.cpcBasicInfo.sCardID);
    }

    m_operation.TollDate = Ptr_ETCCtrl->GetShiftMgr()->m_ShiftParam.tmWorkDate.toString("yyyyMMdd").toInt();
    m_operation.ShiftID = Ptr_ETCCtrl->GetShiftMgr()->m_ShiftParam.wShift;
    m_operation.TicketNo.clear();
    m_operation.OperatorID = QString::number(Ptr_ETCCtrl->GetOperInfo().dwOper);
    m_operation.OperatorName = Ptr_ETCCtrl->GetOperInfo().sOperName;

    // ----------- 入口信息区开始 ----------
    m_entry.AreaID = pTransInfo->vehEntryInfo.sEnNetWorkIdHex.left(2).toInt();
    m_entry.RoadID = convertRoadId(pTransInfo->vehEntryInfo.sEnNetWorkIdHex,
                              pTransInfo->vehEntryInfo.sEnStationHex);
    m_entry.StationID = convertStationId(pTransInfo->vehEntryInfo.sEnNetWorkIdHex,
                                    pTransInfo->vehEntryInfo.sEnStationHex);
    m_entry.StationName = pTransInfo->vehEntryInfo.sEnStaionName;
    m_entry.Time = pTransInfo->vehEntryInfo.EnTime.toString("yyyy-MM-dd hh:mm:ss");
    m_entry.LaneID = pTransInfo->vehEntryInfo.nEnLaneID;
    m_entry.License = GB2312toUnicode(pTransInfo->vehEntryInfo.szEnVLP);
    m_entry.VColor = pTransInfo->vehEntryInfo.bEnVLPC;
    m_entry.VClass = pTransInfo->vehEntryInfo.bEnVC;
    m_entry.VType = pTransInfo->vehEntryInfo.bEnVT;

    DebugLog(QString("准备支付信息-- 车牌:%1,车型:%2,金额:%3,时间:%4,入口站:%5(%6),入口时间:%7")
             .arg(m_vehicle.License)
             .arg(m_vehicle.Class)
             .arg(m_debit.Money)
             .arg(m_debit.SubTime)
             .arg(m_entry.StationName)
             .arg(m_entry.StationID)
             .arg(m_entry.Time));

    return true;
}

void FormMobilePayHybrid::OnPaySuccess()
{
    m_payStep = PS_PaySuccess;
    m_WaitPayTimer.stop();

    UpdateHelpMsg(QString("支付成功!\n按[确定/放行]返回"));
    GetMainDlg()->ShowPromptMsg("支付成功, 按[确定/放行]返回");

    // 根据支付平台类型设置支付方式
    QString sPlatFormType;
    if (m_payResult.PayPlatformType == 1) {
        m_payType = TransPT_WeChat;
        sPlatFormType = QString("微信");
    } else if (m_payResult.PayPlatformType == 2) {
        m_payType = TransPT_AliPay;
        sPlatFormType = QString("支付宝");
    } else if (m_payResult.PayPlatformType == 3) {
        sPlatFormType = QString("百度");
        m_payType = TransPT_Other;
    } else if (m_payResult.PayPlatformType == 4) {
        sPlatFormType = QString("京东");
        m_payType = TransPT_Other;
    } else if (m_payResult.PayPlatformType == 5) {
        sPlatFormType = QString("银联");
        m_payType = TransPT_Union;
    } else if (m_payResult.PayPlatformType == 6) {
        sPlatFormType = QString("银联闪付");
        m_payType = TransPT_Union;
    } else {
        sPlatFormType = QString("未知");
        m_payType = TransPT_Other;
    }
    m_nChannelType = m_payResult.PayPlatformType;

    // 记录支付流水
    DebugLogCategory(LOGMBPAY, QString("付款标识码:%1,订单号:%2,渠道:%3, 金额:%4")
                      .arg(m_payResult.PayIdentifier)
                      .arg(m_payResult.DebitOrder)
                      .arg(sPlatFormType)
                      .arg(m_debit.Money));
}

void FormMobilePayHybrid::OnPayFail(QString sHelpMsg)
{
    m_payStep = PS_PayFail;
    m_WaitPayTimer.stop();

    if (sHelpMsg.isEmpty())
        sHelpMsg = QString("支付失败!\n按【银联键】重新处理");

    UpdateHelpMsg(sHelpMsg);
    GetMainDlg()->ShowPromptMsg("支付失败!");
}

bool FormMobilePayHybrid::BeginPay()
{
    if (!m_pCurTransInfo || !m_bDriverLoaded || !m_bInited) {
        ErrorLog("支付条件不满足，无法开始支付");
        return false;
    }

    m_WaitPayTimer.stop();
    m_payStep = PS_Paying;
    UpdateHelpMsg(QString("正在处理支付...\n\n等待用户出示付款码"));

    // 通知外屏显示
    if (Ptr_Info->bHaveCardMgr()) {
        CDeviceFactory::GetAutoExTollScreen()->ShowHelp(
            AutoExTollScreen::HT_MobilePay,
            "请出示付款码");
    }

    // 准备JSON参数
    QVariantMap mapDebit;
    mapDebit["Money"] = m_debit.Money;
    mapDebit["PayIdentifier"] = m_debit.PayIdentifier;
    mapDebit["SubTime"] = m_debit.SubTime;
    mapDebit["Type"] = m_debit.Type;

    QVariantMap mapEntry;
    mapEntry["AreaID"] = m_entry.AreaID;
    mapEntry["RoadID"] = m_entry.RoadID;
    mapEntry["LaneID"] = m_entry.LaneID;
    mapEntry["License"] = m_entry.License;
    mapEntry["StationID"] = m_entry.StationID;
    mapEntry["StationName"] = m_entry.StationName;
    mapEntry["Time"] = m_entry.Time;
    mapEntry["VClass"] = m_entry.VClass;
    mapEntry["VColor"] = m_entry.VColor;
    mapEntry["VType"] = m_entry.VType;

    QVariantMap mapOperation;
    mapOperation["CardID"] = m_operation.CardID;
    mapOperation["PassCertificateType"] = m_operation.PassCertificateType;
    mapOperation["ShiftID"] = m_operation.ShiftID;
    mapOperation["TollDate"] = m_operation.TollDate;
    mapOperation["TicketNo"] = m_operation.TicketNo;
    mapOperation["OperatorID"] = m_operation.OperatorID;
    mapOperation["OperatorName"] = m_operation.OperatorName;

    QVariantMap mapVehicle;
    mapVehicle["AxisCount"] = m_vehicle.AxisCount;
    mapVehicle["Class"] = m_vehicle.Class;
    mapVehicle["License"] = m_vehicle.License;
    mapVehicle["Type"] = m_vehicle.Type;
    mapVehicle["VLColor"] = m_vehicle.VLColor;
    mapVehicle["Weight"] = m_vehicle.Weight;
    mapVehicle["LimitWeight"] = m_vehicle.LimitWeight;

    QVariantMap mapAutoPlate;
    mapAutoPlate["AutoLicense"] = m_auPlate.AutoLicense;
    mapAutoPlate["AutoColor"] = m_auPlate.AutoColor;

    QVariantMap mapAll;
    mapAll["Debit"] = mapDebit;
    mapAll["Entry"] = mapEntry;
    mapAll["Operation"] = mapOperation;
    mapAll["Vehicle"] = mapVehicle;
    mapAll["AutoPlate"] = mapAutoPlate;
    mapAll["OverTime"] = WAIT_PAY_SECOND * 1000; // 超时时间，毫秒

    QxtJSON jsonBuilder;
    QString sJson = jsonBuilder.stringify(mapAll);

    DebugLog(QString("开始调用支付-- 车牌:%1,车型:%2,金额:%3,时间:%4")
             .arg(m_vehicle.License)
             .arg(m_vehicle.Class)
             .arg(m_debit.Money)
             .arg(m_debit.SubTime));

    // 分配内存存储返回结果
    char szRetBuf[1024] = {0};
    int nRet = m_pIF_DebitMoney(sJson.toUtf8().data(), szRetBuf);

    if (0 != nRet) {
        ErrorLog(QString("启动移动支付失败，返回码:%1").arg(nRet));
        m_payStep = PS_PayFail;
        return false;
    } else {
        DebugLog("启动移动支付成功，等待结果");

        // 启动定时器
        m_WaitPayTimer.start(1000);
        m_nWaitPaySec = WAIT_PAY_SECOND;

        // 解析返回结果
        QString sRetJson = QString::fromUtf8(szRetBuf);
        if (!sRetJson.isEmpty()) {
            QxtJSON jsonParser;
            QVariant varData = jsonParser.parse(sRetJson);
            OnResultCallBack(1, 0, sRetJson); // 假设1表示扣费回馈，0表示成功
        }

        return true;
    }
}

bool FormMobilePayHybrid::ReverseOrder()
{
    m_nReverseTimes = 0;
    return ReverseOrderOnce();
}

bool FormMobilePayHybrid::ReverseOrderOnce()
{
    if (m_debit.PayIdentifier.isEmpty() || !m_bDriverLoaded || !m_bInited) {
        return false;
    }

    m_nReverseTimes++;
    UpdateHelpMsg(QString("正在退款，请稍候..."));

    // 准备JSON参数
    QVariantMap mapReverse;
    mapReverse["PayIdentifier"] = m_debit.PayIdentifier;

    QxtJSON jsonBuilder;
    QString sJson = jsonBuilder.stringify(mapReverse);

    DebugLog(QString("开始调用撤单-- 标识号:%1").arg(m_debit.PayIdentifier));

    // 调用撤单接口
    int nRet = m_pIF_DebitCancel(sJson.toUtf8().data());

    if (0 != nRet) {
        ErrorLog(QString("发起撤单失败，返回码:%1").arg(nRet));
        return false;
    } else {
        DebugLog("发起撤单成功，等待结果");
        return true;
    }
}

QString FormMobilePayHybrid::GetUUID()
{
    QUuid uuid = QUuid::createUuid();
    QString strUuid = uuid.toString();

    // 移除前后的花括号
    strUuid = strUuid.mid(1, strUuid.length() - 2);
    return strUuid;
}

int FormMobilePayHybrid::OnStopPay()
{
    if (m_payStep == PS_PaySuccess) {
        // 提示支付成功，无法退单
        UpdateHelpMsg("交易已成功，无法退单!\n\n按[确定]键返回");
        return 1;
    }

    if (!CMessageBox::Information_Help(QString("取消支付"), QString("是否取消支付"),
                                     QString("按[确认]取消支付,[ESC]返回"),
                                     CMessageBox::Style_OkCancel)) {
        return 1;
    }

    DebugLog("中断支付");

    // 中断支付过程
    if (m_payClass == PayClass_ETC) {
        // 取消支付过程,返回
        if (Ptr_Info->bHaveCardMgr()) {
            CDeviceFactory::GetPayMentMgr()->CloseAllScan();
            // 通知外屏
            CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_Waiting,
                                                         "正在人工处理，请稍侯...");
        }
        OnCancel();
        return 0;
    }

    if (m_payStep != PS_PaySuccess) {
        // 取消支付过程,返回
        if (Ptr_Info->bHaveCardMgr()) {
            CDeviceFactory::GetPayMentMgr()->CloseAllScan();
            // 通知外屏
            CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_Waiting,
                                                         "正在人工处理，请稍侯...");
        }

        // 退单处理
        if (ReverseOrderOnce()) {
            // 关闭窗口
            OnCancel();
            return 0;
        } else {
            // 提示退单失败
            if (!CMessageBox::Information_Help(QString("退单失败"), QString("是否继续退单"),
                                           QString("按[确认]再次退单,[ESC]取消支付"),
                                           CMessageBox::Style_OkCancel)) {
                OnCancel();
                return 0;
            }
            // 继续尝试退单
            ReverseOrderOnce();
        }
    }

    return 1;
}

void FormMobilePayHybrid::OnRemoteStopPay()
{
    OnStopPay();
}

int FormMobilePayHybrid::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    mtcKeyEvent->setKeyType(KC_Func);
    switch (mtcKeyEvent->func()) {
        case KeyReprint:  // 废票键,手动退款
            if (m_payStep != PS_PaySuccess) {
                ReverseOrderOnce();
            }
            break;

        case KeyEsc:
            OnStopPay();
            break;

        case KeyConfirm:  // 确定放行键
            if (m_payStep == PS_PaySuccess) {
                // 支付成功时, 关闭返回
                OnOk();
            }
            break;

        case KeyUniPayCard:  // 银联卡键，再次尝试支付
            if (m_payStep == PS_PayFail) {
                BeginPay();
            }
            break;

        default:
            break;
    }
    return 1;
}

void FormMobilePayHybrid::paintEvent(QPaintEvent *)
{
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);

    // 绘制背景色
    painter.setBrush(g_GlobalUI.m_ColorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1,
                   rectClient.height() - 1);

    // 绘制标题
    QFont fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    QRect rctTitle(0, 0, rect().width(), g_GlobalUI.optw_TitleHeight);
    painter.setFont(fontTitle);

    QString sTitle;
    if (m_pCurTransInfo)
        sTitle = QString("扣款[%1]元").arg(QString::number(m_pCurTransInfo->GetNeedPayMoney() / 100.0, 'f', 2));
    else
        sTitle = QString("移动支付");

    painter.drawText(rctTitle, Qt::AlignCenter, sTitle);

    QRect rctTip(0, g_GlobalUI.optw_TitleHeight, rect().width(),
               rect().height() - g_GlobalUI.optw_TitleHeight);

    painter.drawText(rctTip, Qt::AlignCenter, m_sProcTip);

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

bool FormMobilePayHybrid::OnOpenCardEvent(int nReadId, int nCardType)
{
    if (!m_pCurTransInfo)
        return false;

    CCardReader *pReader = CCardReader::GetCardReader(nReadId);
    if (!pReader) {
        GetMainDlg()->ShowPromptMsg(QString("读写器编号错误"));
        return false;
    }

    if (nCardType != TYPE_PRO) {
        GetMainDlg()->ShowPromptMsg(QString("请刷ETC卡"));
        pReader->CloseCard();
        return false;
    }

    int nErrorCode = 0;
    QString sError, sFDMsg;
    bool bRlt = ProcessProCardEvent(pReader, nErrorCode, sError, sFDMsg);
    pReader->CloseCard();

    if (bRlt) {
        OnOk();
    } else {
        DisplayErrorMsg(sError, sFDMsg);
    }
    return bRlt;
}

bool FormMobilePayHybrid::ProcessProCardEvent(CCardReader *pReader, int &nErrorCode, QString &sError, QString &sFDMsg)
{
    // 这里应该有ETC卡处理逻辑
    // 由于不在本次实现范围内，只返回成功
    sError = "ETC卡处理未实现";
    sFDMsg = "ETC卡处理未实现";
    return false;
}

void FormMobilePayHybrid::DisplayErrorMsg(const QString &sError, const QString &sFDMsg)
{
    UpdateHelpMsg(sError);
    GetMainDlg()->ShowPromptMsg(sError);

    if (!sFDMsg.isEmpty() && Ptr_Info->bHaveCardMgr()) {
        CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_Error, sFDMsg);
    }
}

void FormMobilePayHybrid::OnQryOrderTimer()
{
    // 如果支付中，查询支付结果
    if (m_payStep == PS_Paying || m_payStep == PS_InputPassword) {
        if (m_bDriverLoaded && m_bInited && m_pIF_GetDebitResult) {
            char szRetBuf[1024] = {0};
            int nRet = m_pIF_GetDebitResult(szRetBuf);

            if (0 == nRet) {
                QString sRetJson = QString::fromUtf8(szRetBuf);
                if (!sRetJson.isEmpty()) {
                    QxtJSON jsonParser;
                    QVariant varData = jsonParser.parse(sRetJson);
                    QVariantMap map = varData.toMap();

                    if (map.contains("Result")) {
                        int nResult = map.value("Result").toInt();
                        if (nResult == 0) { // 支付成功
                            OnResultCallBack(1, 0, sRetJson);
                        } else if (nResult > 0) { // 支付失败
                            OnResultCallBack(1, nResult, sRetJson);
                        }
                    }
                }
            }
        }
    }
}

void FormMobilePayHybrid::OnReverseTimer()
{
    // 此函数用于查询退款状态，可留空
}
