#ifndef VSCROLLLABEL_H
#define VSCROLLLABEL_H

#include <QLabel>

class VScrollLabel : public QLabel    // 继承 QLabel类
{
    Q_OBJECT
public:
    explicit VScrollLabel(QWidget *parent = 0, Qt::WindowFlags f=0);
    virtual ~VScrollLabel(){}

public slots:
    void setText(const QString &);

protected:
    void showEvent(QShowEvent *);
    void paintEvent(QPaintEvent *);
    void timerEvent(QTimerEvent *);
    void hideEvent(QHideEvent *);

private slots:
    void metrics();
    void startScroll();

private:
    void getHeight(const QString &);

public:
    void SetLabelAttrbute(qint32 nSize, qint32 nCharSet, qint32 nColor, qint32 nBKColor,
                          bool bBold, bool bUnderLine, bool bShowWay, QString sFont);

private:
    int m_nOffset;
    int m_nTimerID;
    int m_nTextHeight, m_nNewTextHeight, m_nCurHeight;

    QString m_sShowText, m_sNewText;

    bool m_bChanged;

private:
    qint32 m_nSize;        //字体大小
    qint32 m_nCharSet;     //字符集,  0 西方  134 汉字
    qint32 m_nColor;       //字体颜色
    qint32 m_nBKColor;     //背景颜色
    bool    m_bBold;       //字形 false-常规，true-粗体
    bool    m_bUnderLine;  //下划线
    bool    m_bRollShow;   //显示方式	显示方式 true-滚动，false-固定
    QString m_sFont;       //字体
};


#endif // VSCROLLLABEL_H
