#ifndef GLOBALUI_H
#define GLOBALUI_H
#include <QColor>
#include <QRect>
#include <QString>
/**
 * @brief 负责界面所有元素的布局
 */
class GlobalUI {
public:
    GlobalUI();

protected:
    //加载配置文件
    bool LoadCfg(const QString& name);

public:
    QString m_FontName;
    //主窗口位置
    QRect m_RectMainWnd;
    //全屏尺寸
    QRect m_screenRect;
    //标题栏
    QRect main_RectTitle;
    //标题文字
    QString main_AppTitle;
    //标题区域
    QRect main_TitleTextRect;
    //时间区域
    QRect main_TimeTextRect;
    // logo的大小
    QRect main_RectLogo;
    //状态栏
    QRect main_RectStatus;
    //标题字体
    int main_TitleFontSize;
    //标题字间距
    int main_TitleFontSpace;
    //状态栏字体
    int main_StatusFontSize;
    //版权文字
    QString main_CopyRight;
    //子窗口显示区域
    QRect m_RectSubForm;

    //子窗口边缘margin
    int sub_margin;

    ////Out Of Service界面
    //字体大小
    int oos_FontSize;
    //字间距
    int oos_FontSpace;
    //行间距（共两行）
    int oos_LineSpace;
    //图标区域
    QRect oos_IconRect;

    /********等待车辆界面**********/
    //圆形显示框
    QRect waitveh_roundRect;
    //字体大小
    int waitveh_FontSize;
    //字间距
    int waitveh_FontSpace;
    //行间距（共两行）
    int waitveh_LineSpace;

    /************提示界面*************/
    //图标显示
    QRect prompt_iconRect;
    //提示文字区域
    QRect prompt_textRect;
    //字体大小
    int prompt_FontSize;
    //字间距
    int prompt_FontSpace;

    /********插卡界面*********/
    //圆形显示框
    QRect incard_bigRoundRect;
    //小圆形显示框
    QRect incard_smallRoundRect;
    //大字提示显示框
    QRect incard_bigTextRect;
    //小字提示显示框
    QRect incard_smallTextRect;
    //字体大小
    int incard_BigFontSize;
    //字间距
    int incard_BigFontSpace;
    //大字体大小
    int incard_SmallFontSize;
    //大字体间距
    int incard_SmallFontSpace;

    /********显示金额界面*******/
    //显示收费信息的小矩形
    QRect money_smallRoundRect;
    //小矩形之前的间隔
    int money_verticalMargin;
    //收费字体大小
    int money_tollInfoFontSize;
    //金额字体大小
    int money_moneyFontSize;
    //标题说明字体大小
    int money_labelFontSize;
    //标题宽度
    int money_labelWidth;
    //右侧图示位置
    QRect money_imageRect;
    //提示文字位置
    QRect money_tipRect;
    //提示文字字体大小
    int money_tipFontSize;
    //提示文字间隔
    int money_tipFontSpace;
    //支付标识大小
    QRect money_alipaylogoRect;
    QRect money_wechatlogoRect;

public:
    void Init();
};

extern GlobalUI g_GlobalUI;
#endif  // GLOBALUI_H
