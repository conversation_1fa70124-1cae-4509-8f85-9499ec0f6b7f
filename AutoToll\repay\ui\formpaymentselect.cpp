#include "formpaymentselect.h"
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"
#include <QApplication>
#include <QKeyEvent>

FormPaymentSelect::FormPaymentSelect(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblHelpInfo(0)
    , m_selectedPayType(TransPT_Cash)
    , m_selectedIndex(0)
    , m_bSelectionMade(false)
    , m_pTimeoutTimer(0)
{
    // 初始化定时器
    m_pTimeoutTimer = new QTimer(this);
    connect(m_pTimeoutTimer, SIGNAL(timeout()), this, SLOT(OnSelectionTimeout()));
    
    // 初始化界面配置
    InitUIConfig();
    

    
    InfoLog("创建支付方式选择界面");
}

FormPaymentSelect::~FormPaymentSelect()
{
    InfoLog("销毁支付方式选择界面");
}

void FormPaymentSelect::InitUIConfig()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize, QFont::Bold);
    m_fontOption = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize + 2);
    m_fontHelp = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    
    // 设置颜色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    m_colorTitle = QColor(0, 0, 0);
    m_colorOption = QColor(50, 50, 50);
    m_colorSelected = QColor(0, 120, 215);
    m_colorHelp = QColor(100, 100, 100);
}

void FormPaymentSelect::InitUI(int iFlag)
{
    CBaseOpWidget::InitUI(iFlag);
    
    InitControls();
    SetupControlProperties();
    InitLayout();
    InitConnections();
    // 安装子控件键盘事件过滤器
    filterChildrenKeyEvent();
    InfoLog("初始化支付方式选择界面");
}

void FormPaymentSelect::InitControls()
{
    // 创建标题标签
    m_pLblTitle = new QLabel("选择支付方式", this);
    
    // 创建帮助信息标签
    m_pLblHelpInfo = new QLabel("请按对应数字键选择支付方式，按[确认]键确认选择\n按[ESC]键取消", this);
}

void FormPaymentSelect::SetupControlProperties()
{
    // 设置标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    m_pLblTitle->setText("选择支付方式");
    m_pLblTitle->setStyleSheet("color: rgb(0, 0, 0);");
    
    // 设置帮助信息
    m_pLblHelpInfo->setFont(m_fontHelp);
    m_pLblHelpInfo->setAlignment(Qt::AlignCenter);
    m_pLblHelpInfo->setWordWrap(true);
    m_pLblHelpInfo->setText("请按对应数字键选择支付方式，按[确认]键确认选择\n按[ESC]键取消");
    m_pLblHelpInfo->setStyleSheet("color: rgb(100, 100, 100);");
}

void FormPaymentSelect::InitLayout()
{
    QRect rectClient = this->rect();
    if (rectClient.width() <= 0 || rectClient.height() <= 0) {
        rectClient = QRect(0, 0, 800, 600); // 默认大小
    }
    
    int currentY = MARGIN;
    
    // 标题
    int titleHeight = 60;
    m_pLblTitle->setGeometry(0, currentY, rectClient.width(), titleHeight);
    currentY += titleHeight + MARGIN;
    
    // 帮助信息
    int helpHeight = 60;
    m_pLblHelpInfo->setGeometry(MARGIN, rectClient.height() - helpHeight - MARGIN, 
                                rectClient.width() - 2 * MARGIN, helpHeight);
}

void FormPaymentSelect::InitConnections()
{
    // 目前没有需要连接的信号槽
}

bool FormPaymentSelect::ShowPaymentSelect(CTransPayType &selectedPayType)
{
    InfoLog("开始支付方式选择");
    
    // 重置状态
    m_bSelectionMade = false;
    m_selectedIndex = 0;
    
    // 显示界面
    int result = doModalShow();
    
    if (result == CBaseOpWidget::Rlt_OK && m_bSelectionMade) {
        // 选择成功
        selectedPayType = m_selectedPayType;
        InfoLog(QString("支付方式选择成功 - 类型:%1").arg(static_cast<int>(selectedPayType)));
        return true;
    } else {
        // 选择失败或取消
        InfoLog("支付方式选择取消或失败");
        return false;
    }
}

void FormPaymentSelect::SetAvailablePayTypes(const QList<CTransPayType> &payTypes)
{
    m_availablePayTypes = payTypes;
    UpdatePaymentDisplay();
}

void FormPaymentSelect::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    
    // 绘制背景
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);
    
    // 绘制支付选项 - 移动到中央区域
    int totalOptionsHeight = m_availablePayTypes.size() * (OPTION_HEIGHT + 10) - 10; // 总选项高度
    int startY = (rectClient.height() - totalOptionsHeight) / 2; // 垂直居中
    int optionWidth = 300; // 选项宽度
    int startX = (rectClient.width() - optionWidth) / 2; // 水平居中

    int currentY = startY;
    for (int i = 0; i < m_availablePayTypes.size() && i < 5; ++i) {
        QRect optionRect(startX, currentY, optionWidth, OPTION_HEIGHT);

        // 设置颜色
        QColor textColor = (i == m_selectedIndex) ? m_colorSelected : m_colorOption;
        painter.setPen(textColor);
        painter.setFont(m_fontOption);

        // 绘制选项文本 - 居中对齐
        QString optionText = QString("%1. %2").arg(i + 1).arg(GetPayTypeName(m_availablePayTypes[i]));
        painter.drawText(optionRect, Qt::AlignCenter | Qt::AlignVCenter, optionText);

        currentY += OPTION_HEIGHT + 10;
    }
    
    painter.end();
    
    // 绘制到窗口
    QPainter windowPainter(this);
    windowPainter.drawPixmap(rectClient, pixmap);
}

int FormPaymentSelect::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    // 无条件日志，确认方法被调用
    InfoLog("FormPaymentSelect::mtcKeyPressed 被调用");

    if (!mtcKeyEvent) {
        InfoLog("mtcKeyPressed: mtcKeyEvent 为空");
        return 0;
    }

    InfoLog(QString("mtcKeyPressed: 收到按键事件，key=%1, keyName=%2, isNumKey=%3").arg(mtcKeyEvent->key()).arg(mtcKeyEvent->keyName()).arg(mtcKeyEvent->isNumKey()));

    if (mtcKeyEvent->isNumKey()) {
        // 处理数字键 - 使用ascii方法修复键值计算
        int keyValue = mtcKeyEvent->ascii() - '0'; // 得到 0-9
        InfoLog(QString("数字键处理：原始key=%1, ascii=%2, keyValue=%3").arg(mtcKeyEvent->key()).arg(mtcKeyEvent->ascii()).arg(keyValue));

        if (keyValue >= 1 && keyValue <= 9) {
            ProcessNumberSelection(keyValue);
        } else {
            InfoLog(QString("无效的数字键：%1").arg(keyValue));
        }
        return 1;
    } else if (mtcKeyEvent->func() == KeyEsc) {
        // 处理ESC键
        ProcessEscapeKey();
        return 1;
    } else if (mtcKeyEvent->func() == KeyConfirm) {
        // 处理确认键
        ProcessConfirmKey();
        return 1;
    }
    
    return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
}

void FormPaymentSelect::OnSelectionTimeout()
{
    InfoLog("支付方式选择超时");
    CancelSelection();
}

void FormPaymentSelect::UpdatePaymentDisplay()
{
    update(); // 重绘界面
}

void FormPaymentSelect::UpdateSelectionHighlight()
{
    update(); // 重绘界面
}

void FormPaymentSelect::RefreshDisplay()
{
    update(); // 重绘界面
}

void FormPaymentSelect::ProcessNumberSelection(int number)
{
    InfoLog(QString("ProcessNumberSelection 被调用，number=%1, 可用支付方式数量=%2").arg(number).arg(m_availablePayTypes.size()));

    if (number >= 1 && number <= m_availablePayTypes.size()) {
        m_selectedIndex = number - 1;
        m_selectedPayType = m_availablePayTypes[m_selectedIndex];
        InfoLog(QString("用户选择支付方式：%1 (索引:%2)").arg(GetPayTypeName(m_selectedPayType)).arg(m_selectedIndex));

        // 只更新选择状态和界面高亮，不直接完成选择
        UpdateSelectionHighlight();
    } else {
        // 无效选择
        InfoLog(QString("无效的支付方式选择：%1，有效范围：1-%2").arg(number).arg(m_availablePayTypes.size()));
    }
}

void FormPaymentSelect::ProcessEscapeKey()
{
    InfoLog("用户取消支付方式选择");
    CancelSelection();
}

void FormPaymentSelect::ProcessConfirmKey()
{
    if (IsValidSelection(m_selectedIndex)) {
        m_selectedPayType = m_availablePayTypes[m_selectedIndex];
        InfoLog(QString("确认选择支付方式：%1 (索引:%2)").arg(GetPayTypeName(m_selectedPayType)).arg(m_selectedIndex));
        CompleteSelection();
    } else {
        InfoLog("无效的选择索引，无法确认");
    }
}

void FormPaymentSelect::SelectPaymentType(int index)
{
    if (index >= 0 && index < m_availablePayTypes.size()) {
        m_selectedIndex = index;
        m_selectedPayType = m_availablePayTypes[index];
        UpdateSelectionHighlight();
    }
}

void FormPaymentSelect::CompleteSelection()
{
    InfoLog(QString("CompleteSelection 被调用，选择的支付方式：%1").arg(GetPayTypeName(m_selectedPayType)));
    m_bSelectionMade = true;
    OnOk(); // 关闭对话框并返回OK
}

void FormPaymentSelect::CancelSelection()
{
    m_bSelectionMade = false;
    OnCancel(); // 关闭对话框并返回Cancel
}

QString FormPaymentSelect::GetPayTypeName(CTransPayType payType)
{
    switch (payType) {
        case TransPT_Cash: return "现金";
        case TransPT_Union: return "银联卡";      // 银联卡代表移动支付（支付宝/微信）
        case TransPT_AliPay: return "银联卡";     // 支付宝归类为银联卡（移动支付）
        case TransPT_WeChat: return "银联卡";     // 微信归类为银联卡（移动支付）
        case TransPT_ETCCard: return "赣通卡";    // ETC显示为赣通卡
        default: return "未知";
    }
}

QString FormPaymentSelect::GetPayTypeDescription(CTransPayType payType)
{
    switch (payType) {
        case TransPT_Cash: return "现金支付";
        case TransPT_Union: return "银联卡移动支付";     // 银联卡代表移动支付
        case TransPT_AliPay: return "银联卡移动支付";    // 支付宝归类为银联卡移动支付
        case TransPT_WeChat: return "银联卡移动支付";    // 微信归类为银联卡移动支付
        case TransPT_ETCCard: return "赣通卡支付";       // ETC描述为赣通卡支付
        default: return "未知支付方式";
    }
}

bool FormPaymentSelect::IsValidSelection(int index)
{
    return index >= 0 && index < m_availablePayTypes.size();
}
