#ifndef FORMREPAYSUCCESS_H
#define FORMREPAYSUCCESS_H

#include <QWidget>
#include <QLabel>
#include <QTimer>
#include "baseopwidget.h"
#include "../../common/lanetype.h"

/**
 * @brief 补费支付成功确认界面
 * 显示补费操作的成功结果，包括补费方式、车牌信息、支付方式和金额等
 */
class FormRepaySuccess : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormRepaySuccess(QWidget *parent = 0);
    ~FormRepaySuccess();

    /**
     * @brief 显示补费成功信息
     * @param repayTypeName 补费方式名称（如"当趟补费"、"省内名单补费"）
     * @param vehPlate 车牌号
     * @param vehPlateColor 车牌颜色
     * @param payTypeName 支付方式名称（如"现金"、"支付宝"等）
     * @param amount 补费金额（分）
     * @param autoCloseSeconds 自动关闭秒数，0表示不自动关闭
     * @return true-用户确认，false-用户取消
     */
    bool ShowRepaySuccess(const QString &repayTypeName, 
                         const QString &vehPlate, 
                         int vehPlateColor,
                         const QString &payTypeName, 
                         int amount,
                         int autoCloseSeconds = 0);

protected:
    /**
     * @brief 初始化界面
     */
    void InitUI();
    
    /**
     * @brief 处理键盘输入
     * @param mtcKeyEvent 键盘事件
     * @return 处理结果
     */
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    
    /**
     * @brief 绘制界面
     * @param event 绘制事件
     */
    void paintEvent(QPaintEvent *event);

private slots:
    /**
     * @brief 自动关闭定时器响应
     */
    void OnAutoCloseTimer();

private:
    // 界面控件
    QLabel *m_pLblTitle;           // 标题标签（补费方式操作）
    QLabel *m_pLblVehInfo;         // 车辆信息标签
    QLabel *m_pLblPayResult;       // 支付结果标签
    QLabel *m_pLblHelpInfo;        // 操作提示标签
    
    // 显示数据
    QString m_repayTypeName;       // 补费方式名称
    QString m_vehPlate;            // 车牌号
    int m_vehPlateColor;           // 车牌颜色
    QString m_payTypeName;         // 支付方式名称
    int m_amount;                  // 补费金额（分）
    
    // 自动关闭功能
    QTimer *m_pAutoCloseTimer;     // 自动关闭定时器
    int m_autoCloseSeconds;        // 自动关闭剩余秒数
    int m_originalAutoCloseSeconds; // 原始自动关闭秒数
};

#endif // FORMREPAYSUCCESS_H