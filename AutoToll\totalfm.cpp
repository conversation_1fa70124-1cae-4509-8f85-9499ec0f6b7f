#include "totalfm.h"
#include "ui_totalfm.h"
#include <qdir.h>
#include <QPainter>
#include "laneinfo.h"
//cl  工班汇总

#define WND_WIDTH 150
#define WND_HIGHT 170

#ifdef Q_OS_WIN32
#define FONT_NAME "微软雅黑"
#else
#define FONT_NAME "微软雅黑"
#endif
TotalFm::TotalFm(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::TotalFm)
{
    ui->setupUi(this);
}

TotalFm::~TotalFm()
{
    delete ui;
}
void TotalFm::paintEvent(QPaintEvent *)
{

    QPainter painter(this);
    painter.drawPixmap(0,0,WND_WIDTH, WND_HIGHT,QPixmap(":/images/images/total.png"));
}


void TotalFm::InitUI()
{
    //this->move(525,485);
    this->move(407,50);
    this->resize(WND_WIDTH,WND_HIGHT);
    this->setFixedSize(WND_WIDTH,WND_HIGHT);
    setWindowFlags(Qt::FramelessWindowHint);


    /*
//    ui->lbwx->move(30,50);
//    ui->lbwxval->move(120,45);
//    ui->lbltk->move(30,85);
//    ui->lbltkval->move(120,80);
//    ui->lbqt->move(30,120);
//    ui->lbqtval->move(120,115);
*/
    //报警灯
    ui->lbltk->setStyleSheet("QLabel{background-image:url(:/images/images/dev/light.png);}");
    ui->lbltk->resize(30,40);//35);
    ui->lbltk->move(0,-5);
    //通行灯
    ui->lbwx->setStyleSheet("QLabel{background-image:url(:/images/images/dev/pass.png);}");
    ui->lbwx->resize(30,40);//35);
    ui->lbwx->move(40,-5);

    int offset = 0;
    if(Ptr_Info->IsEntryLane())
    {
        offset = 20;
    }


    if(Ptr_Info->IsExitLane())
    {
        ui->lbqt->move(30,120);
        ui->lbqtval->move(120,115);
    }

    ui->lbText->setText(QString::fromUtf8("前费显"));
    ui->lbText->resize(100,20);
    ui->lbText->move(90,10);
    QFont ftLaneInfo;
    ftLaneInfo.setFamily(FONT_NAME);
    ftLaneInfo.setPixelSize(12);
    ui->lbqt->setText(QString::fromUtf8("ETC维护\n请走其他车道"));
    //ui->lbqt->setText(QString::fromUtf8("欢迎使用ETC"));

    ui->lbqt->resize(120,100);
    ui->lbqt->move(20,50);//50,40);
    ui->lbqtval->hide();
}


void TotalFm::SetETCIsOpen(bool isOpen){
    if(isOpen){

        ui->lbqt->setText(QString::fromUtf8("欢迎使用ETC"));

    }else{
        ui->lbqt->setText(QString::fromUtf8("ETC关闭"));
    }
    ui->lbqt->resize(120,50);
    ui->lbqt->move(20,50);
}

void TotalFm::OnShowText(QString sText, int nRow)
{
    ui->lbqt->setText(sText);
}


void TotalFm::SetTolalPic(bool nTolalLight,bool nTolalPass)
{
    if(nTolalLight)//cl缺少判定依据以及另一种情况的图片
        ui->lbltk->setStyleSheet("QLabel{background-image:url(:/images/images/dev/light.png);}");
    else//保留
        ui->lbltk->setStyleSheet("QLabel{background-image:url(:/images/images/dev/light.png);}");
    if(nTolalPass)//cl缺少判定依据以及另一种情况的图片
        ui->lbwx->setStyleSheet("QLabel{background-image:url(:/images/images/dev/pass.png);}");
    else//保留
        ui->lbwx->setStyleSheet("QLabel{background-image:url(:/images/images/dev/pass.png);}");
    ui->lbltk->show();
    ui->lbwx->show();
}

