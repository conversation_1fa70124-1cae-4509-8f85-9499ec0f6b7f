#include "repaymanager.h"
#include "../database/repay_db_manager.h"
#include "../../log4qt/ilogmsg.h"
#include "../../laneinfo.h"
#include <QMutexLocker>
#include <QDateTime>
#include <QRegExp>
#include "etclanectrl.h"
#include "../../common/lanetype.h"

RepayManager* RepayManager::m_pInstance = 0;
QMutex RepayManager::m_mutex;

RepayManager::RepayManager(QObject *parent)
    : QObject(parent)
    , m_currentRepayType(RepayType_None)
    , m_currentStage(RepayStage_None)
    , m_currentVehPlateColor(0)
    , m_currentVehType(0)
    , m_currentAmount(0)
    , m_currentInterceptType((InterceptType)0)
    , m_pProDebPayment(0)
    , m_pAuthManager(0)
    , m_pRepayConfig(0)
    , m_bInitialized(false)
    // 移除 m_bRepayInProgress(false) 初始化
    , m_currentPayType(TransPT_Cash)
    , m_currentPayAmount(0)
{
}

RepayManager* RepayManager::GetInstance()
{
    if (m_pInstance == 0) {
        QMutexLocker locker(&m_mutex);
        if (m_pInstance == 0) {
            m_pInstance = new RepayManager();
        }
    }
    return m_pInstance;
}

bool RepayManager::Initialize()
{
    if (m_bInitialized) {
        return true;
    }
    
    InfoLog("初始化补费管理器...");
    
    // 获取组件实例
    m_pProDebPayment = ProDebPayment::GetProDebPayment();
    m_pAuthManager = AuthManager::GetInstance();
    m_pRepayConfig = RepayConfig::GetInstance();
    
    if (!m_pProDebPayment) {
        ErrorLog("省中心接口初始化失败");
        return false;
    }
    
    if (!m_pAuthManager) {
        ErrorLog("授权管理器初始化失败");
        return false;
    }
    
    if (!m_pRepayConfig) {
        ErrorLog("补费配置初始化失败");
        return false;
    }
    
    // 连接信号
    connect(m_pProDebPayment, SIGNAL(QueryDebtInfoFinished(bool,DebtQueryResult)),
            this, SLOT(OnDebtQueryFinished(bool,DebtQueryResult)));
    connect(m_pProDebPayment, SIGNAL(NotifyDebtCompleteFinished(bool,QString)),
            this, SLOT(OnNotifyCompleteFinished(bool,QString)));
    
    // 初始化状态
    ClearCurrentRepayInfo();
    
    m_bInitialized = true;
    InfoLog("补费管理器初始化完成");
    
    return true;
}

bool RepayManager::StartRepay(RepayType type, const CVehInfo &vehInfo)
{
    QMutexLocker locker(&m_repayMutex);

    if (!m_bInitialized) {
        ErrorLog("补费管理器未初始化");
        emit ErrorOccurred(RepayError_SystemError, "补费管理器未初始化");
        return false;
    }

    // 移除 m_bRepayInProgress 检查，使补费流程无状态
    // 每次补费都是独立的操作，不需要检查之前的状态

    // 验证补费条件
    if (!ValidateRepayConditions(type, QString(vehInfo.szVehPlate), vehInfo.nVehPlateColor)) {
        return false;
    }

    // 设置当前补费信息（仅用于日志记录，不作为状态控制）
    m_currentRepayType = type;
    m_currentVehPlate = QString(vehInfo.szVehPlate);
    m_currentVehPlateColor = vehInfo.nVehPlateColor;
    m_currentVehType = vehInfo.VehClass;
    // 移除 m_bRepayInProgress = true; 设置

    InfoLog(QString("开始补费流程 - 类型:%1, 车牌:%2, 车型:%3")
            .arg(GetRepayTypeName(type))
            .arg(QString(vehInfo.szVehPlate))
            .arg(m_currentVehType));

    // 设置初始阶段：进入授权验证
    SetRepayStage(RepayStage_Authorization);

    emit RepayStarted(type);

    return true;
}

bool RepayManager::ProcessCurrentRepay(const QString &vehPlate, int vehPlateColor, 
                                      int vehType, int amount)
{
    QMutexLocker locker(&m_repayMutex);
    
    InfoLog(QString("处理当趟补费 - 车牌:%1, 车型:%2, 金额:%3分")
            .arg(vehPlate).arg(vehType).arg(amount));
    
    // 验证车辆信息
    if (!ValidateVehicleInfo(vehPlate, vehPlateColor)) {
        emit ErrorOccurred(RepayError_VehicleInfoInvalid, "车辆信息无效");
        return false;
    }
    
    // 验证补费金额
    if (!ValidateRepayAmount(vehType, amount)) {
        emit ErrorOccurred(RepayError_AmountExceeded, "补费金额超出限制");
        return false;
    }
    
    // 更新当前补费信息
    SetCurrentRepayInfo(vehPlate, vehPlateColor, vehType, amount);
    
    // 设置阶段为金额确认
    SetRepayStage(RepayStage_AmountConfirm);
    
    InfoLog(QString("当趟补费验证通过 - 车牌:%1, 金额:%2分").arg(vehPlate).arg(amount));
    
    return true;
}

bool RepayManager::ProcessProvinceRepay(const QString &vehPlate, int vehPlateColor,
                                       const RepayDebtQueryResult &debtResult)
{
    QMutexLocker locker(&m_repayMutex);
    
    InfoLog(QString("处理省内名单补费 - 车牌:%1, 欠费总额:%2分")
            .arg(vehPlate).arg(debtResult.totalAmount));
    
    // 验证查询结果
    if (!ValidateProvinceRepayResult(debtResult)) {
        emit ErrorOccurred(RepayError_DebtQueryFailed, "省内欠费查询结果无效");
        return false;
    }
    
    // 若外层已预先选择拦截方式则直接使用；否则弹出选择界面
    InterceptType selectedInterceptType = m_currentInterceptType;
    if ((int)selectedInterceptType == 0) {
        FormInterceptSelect interceptForm;
        interceptForm.InitUI();
        interceptForm.SetPromptMessage(QString("车牌 %1 在省内追收名单中，请选择拦截方式").arg(vehPlate));
        interceptForm.SetDefaultSelection(Intercept_Exit);  // 默认选择出口拦截
        if (!interceptForm.ShowInterceptSelect(selectedInterceptType)) {
            InfoLog(QString("用户取消拦截方式选择 - 车牌:%1").arg(vehPlate));
            emit ErrorOccurred(RepayError_UserCancelled, "用户取消拦截方式选择");
            return false;
        }
    }
    
    InfoLog(QString("用户选择拦截方式 - 车牌:%1, 拦截类型:%2")
            .arg(vehPlate).arg(static_cast<int>(selectedInterceptType)));
    
    // 保存拦截方式和欠费信息
    m_currentInterceptType = selectedInterceptType;
    m_currentDebtResult = debtResult;
    m_currentListno = debtResult.listno;
    
    // 更新当前补费信息
    int totalAmount = debtResult.totalAmount;
    SetCurrentRepayInfo(vehPlate, vehPlateColor, 0, totalAmount);
    
    // 设置阶段为金额确认
    SetRepayStage(RepayStage_AmountConfirm);
    
    InfoLog(QString("省内名单补费验证通过 - 车牌:%1, 拦截方式:%2, 工单数:%3, 总金额:%4分")
            .arg(vehPlate)
            .arg(selectedInterceptType == Intercept_Entry ? "入口拦截" : "出口拦截")
            .arg(debtResult.debtItems.size())
            .arg(totalAmount));
    
    return true;
}

bool RepayManager::ValidateRepayAmount(int vehType, int amount)
{
    InfoLog(QString("开始校验补费金额 - 车型:%1(%2), 金额:%3分")
           .arg(vehType).arg(GetVehClassName(vehType)).arg(amount));
    
    if (amount <= 0) {
        WarnLog(QString("补费金额无效：%1分").arg(amount));
        return false;
    }
    
    // 检查车型限额（从参数文件查询）
    int maxFee = m_pRepayConfig->GetMaxFee(vehType);
    InfoLog(QString("【金额校验】车型限额查询结果 - 车型:%1, 限额:%2分").arg(vehType).arg(maxFee));
    
    if (amount > maxFee) {
        ErrorLog(QString("补费金额超限 - 车型:%1(%2), 金额:%3分, 限额:%4分")
                .arg(vehType).arg(GetVehClassName(vehType)).arg(amount).arg(maxFee));
        return false;
    }
    
    // 检查全局限额
    int globalMaxFee = m_pRepayConfig->GetGlobalMaxFee();
    if (amount > globalMaxFee) {
        ErrorLog(QString("补费金额超过全局限额 - 金额:%1分, 全局限额:%2分")
                .arg(amount).arg(globalMaxFee));
        return false;
    }
    
    InfoLog(QString("【金额校验】补费金额验证通过 - 车型:%1(%2), 金额:%3分, 限额:%4分")
           .arg(vehType).arg(GetVehClassName(vehType)).arg(amount).arg(maxFee));
    return true;
}

bool RepayManager::QueryDebtDetail(const QString &vehPlate, int vehPlateColor,
                                  RepayDebtQueryResult &result)
{
    if (!m_pProDebPayment) {
        ErrorLog("省中心接口未初始化");
        return false;
    }
    
    InfoLog(QString("查询省内欠费明细 - 车牌:%1").arg(vehPlate));
    
    // 设置查询阶段
    SetRepayStage(RepayStage_DebtQuery);
    
    // 改为同步查询明细，直接返回结果并触发回调
    DebtQueryResult debtResult;
    bool ok = m_pProDebPayment->QueryDebtDetail(vehPlate, vehPlateColor, debtResult);
    if (!ok) {
        ErrorLog("同步查询欠费失败");
        // 仅返回失败，让调用方决定提示与退出
        return false;
    }
    
    // 将ProDebPayment的结果转换为RepayDebtQueryResult
    RepayDebtQueryResult uiResult;
    uiResult.listno = debtResult.listno;
    uiResult.vehiclePlate = vehPlate;
    uiResult.vehiclePlateColor = vehPlateColor;

    uiResult.totalAmount = debtResult.oweFee.toInt();
    for (int i = 0; i < debtResult.debtlist.size(); ++i) {
        const DebtListItem &dl = debtResult.debtlist[i];
        RepayDebtItem item;
        item.orderIds = dl.orderid;
        item.debtDate = dl.extime.isEmpty() ? dl.entime : dl.extime;
        item.entryStation = dl.enTollStationName;
        item.exitStation = dl.extollStationName;
        item.debtAmount = dl.oweFee.toInt();
        item.passId = dl.passid;
        item.remark = dl.calculationBasis;
        uiResult.debtItems.append(item);
    }
    
    // 直接发射完成信号（保持与原异步回调一致的入口）
    emit DebtQueryCompleted(true, uiResult);
    return true;
}

bool RepayManager::ProcessPayment(CTransPayType payType, int amount, 
                                 const QString &vehPlate, int vehPlateColor)
{
    QMutexLocker locker(&m_repayMutex);
    
    InfoLog(QString("处理补费支付 - 车牌:%1, 车型:%2, 支付方式:%3, 金额:%4分")
            .arg(vehPlate).arg(m_currentVehType).arg(payType).arg(amount));
    
    // 专项作业车支付流程特别诊断
    if (m_currentVehType >= 21 && m_currentVehType <= 26) {
        InfoLog(QString("【专项作业车诊断】开始支付处理 - 车型:%1, 金额:%2分").arg(m_currentVehType).arg(amount));
    }
    
    // 设置支付阶段
    SetRepayStage(RepayStage_Payment);
    emit PaymentProcessing();
    
    // 生成流水ID
    m_currentWasteId = GenerateWasteId();
    
    // 存储支付参数
    m_currentPayType = payType;
    m_currentPayAmount = amount;
    
    // 根据支付方式调用相应的处理逻辑
    bool paymentResult = false;
    QString errorMessage;
    
    switch (payType) {
        case TransPT_Cash:
            paymentResult = ProcessCashPayment(amount, vehPlate, vehPlateColor, errorMessage);
            break;
            
        case TransPT_Union:
        case TransPT_AliPay:
        case TransPT_WeChat:
            paymentResult = ProcessMobilePayment(payType, amount, vehPlate, vehPlateColor, errorMessage);
            break;
            
        case TransPT_ETCCard:
            paymentResult = ProcessETCPayment(amount, vehPlate, vehPlateColor, errorMessage);
            break;
            
        default:
            errorMessage = QString("不支持的支付方式：%1").arg(payType);
            ErrorLog(errorMessage);
            paymentResult = false;
            break;
    }
    
    // 处理支付结果
    if (paymentResult) {
        HandlePaymentSuccess(payType, amount);
    } else {
        HandlePaymentFailure(errorMessage);
    }
    
    return paymentResult;
}

bool RepayManager::GenerateRepayRecord(const CTransInfo &transInfo, RepayType repayType,
                                      const QString &orderIds)
{
    InfoLog(QString("生成补费流水 - 类型:%1, 车牌:%2, 订单:%3")
            .arg(GetRepayTypeName(repayType))
            .arg(QString(transInfo.VehInfo.szVehPlate))
            .arg(orderIds));
    
    // 构造完整的交易信息
    CTransInfo completeTransInfo = transInfo;
    FillTransInfo(completeTransInfo, m_currentVehPlate, m_currentVehPlateColor, 
                 m_currentVehType, m_currentAmount, repayType, m_currentPayType);
    
    // 调用ETC控制器保存补费流水 - 这是关键的流水保存逻辑
    bool bRlt = Ptr_ETCCtrl->saveTransWaste_Repay(&completeTransInfo);
    
    if (bRlt) {
        InfoLog(QString("补费流水保存成功 - 车牌:%1, 金额:%2分").arg(m_currentVehPlate).arg(m_currentAmount));
        
        // 记录补费操作到本地日志
        LogRepayOperation(repayType, m_currentVehPlate, m_currentVehPlateColor,
                         m_currentAmount, m_currentPayType, true, "", orderIds, 
                         m_currentListno, m_currentWasteId);
    } else {
        ErrorLog(QString("补费流水保存失败 - 车牌:%1, 金额:%2分").arg(m_currentVehPlate).arg(m_currentAmount));
        
        // 记录失败操作
        LogRepayOperation(repayType, m_currentVehPlate, m_currentVehPlateColor,
                         m_currentAmount, m_currentPayType, false, "流水保存失败", orderIds, 
                         m_currentListno, m_currentWasteId);
    }
    
    return bRlt;
}

void RepayManager::FillTransInfo(CTransInfo &transInfo, const QString &vehPlate, int vehPlateColor, 
                                int vehType, int amount, RepayType repayType, CTransPayType payType)
{
    InfoLog(QString("构造交易信息 - 车牌:%1, 车型:%2, 金额:%3分, 支付方式:%4")
            .arg(vehPlate).arg(vehType).arg(amount).arg(payType));
    
    QDateTime curTime = QDateTime::currentDateTime();

    // 清空并初始化交易信息
    transInfo.ClearTransInfo();
    
    // 获取当前门架信息
    QString sGantryHex;
    bool bOpengantry = false;
    CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(sGantryHex, transInfo.m_curGantryInfo, bOpengantry);
    
    // 设置交易时间
    transInfo.TransTime = curTime;
    
    // 设置车辆信息
    CVehInfo vehInfo;
    memset(&vehInfo, 0, sizeof(vehInfo));
    qstrncpy(vehInfo.szVehPlate, vehPlate.toLocal8Bit().data(), sizeof(vehInfo.szVehPlate));
    vehInfo.nVehPlateColor = vehPlateColor;
    vehInfo.VehClass = static_cast<CVehClass>(vehType);
    // 根据车型类别设置国标车型
    CVehClass vehClass = static_cast<CVehClass>(vehType);
    if (vehClass >= VC_Truck1 && vehClass <= VC_Truck6) {
        vehInfo.GBVehType = UVT_TRUCK;      // 货车
    } else if (vehClass >= VC_YJ1 && vehClass <= VC_YJ6) {
        vehInfo.GBVehType = UVT_TRUCK;      // 专项作业车按货车处理
        InfoLog(QString("【专项作业车诊断】设置国标车型为货车 - 车型:%1").arg(vehType));
    } else {
        vehInfo.GBVehType = UVT_Normal;     // 客车默认为普通车
    }
    transInfo.SetVehInfo(&vehInfo);
    
    // 设置费用信息
    transInfo.m_nTotalFee = amount;
    transInfo.m_nTransFee = amount;
    transInfo.m_nProvFee = amount;
    transInfo.m_nProvinceDiscountFee = 0;
    transInfo.m_nOriginFee = 0;
    transInfo.m_nDiscountFee = 0;
    transInfo.m_nProvinceCount = 1;
    transInfo.m_nRepayType = static_cast<int>(repayType);

    // 设置门架费用信息
    CGantryFeeInfo gantryFeeInfo;
    gantryFeeInfo.Clear();
    gantryFeeInfo.payFee = amount;
    gantryFeeInfo.discountFee = 0;
    gantryFeeInfo.realFee = amount;
    gantryFeeInfo.cardFee = amount;
    gantryFeeInfo.feeProvSumLocal = amount; // 本省累计实收（含本门架）
    gantryFeeInfo.paramVersion.clear();
    gantryFeeInfo.sFeeSpecial.clear();
    gantryFeeInfo.fitResult = 0; // 本门架拟合结果,0-未拟合或拟合成功 1-拟合失败
    gantryFeeInfo.payFeeProvSumLocal = amount; // 计费模块保留输出字段1,本省累计应收金额

    // 处理收费区间信息
    QString sTollIntervals = transInfo.m_curGantryInfo.sTollIntervals;
    QStringList sTollIntervalList = sTollIntervals.split("|");
    gantryFeeInfo.tollIntervalsCount = sTollIntervalList.size();
    int nIndex = 0;
    foreach (QString sInterval, sTollIntervalList) {
        gantryFeeInfo.tollIntervalIDs_raw = gantryFeeInfo.tollIntervalIDs_raw + QString("|%1%2").arg(sInterval).arg(QString("0"));
        gantryFeeInfo.tollIntervalIDs = gantryFeeInfo.tollIntervalIDs + QString("|%1").arg(sInterval);
        gantryFeeInfo.tollIntervalSign = gantryFeeInfo.tollIntervalSign + QString("|0");
        if (0 == nIndex) {
            gantryFeeInfo.payFeeGroup = QString("%1").arg(amount);
            gantryFeeInfo.feeGroup = gantryFeeInfo.payFeeGroup;
            gantryFeeInfo.discountFeeGroup = QString("0");
        } else {
            gantryFeeInfo.payFeeGroup = gantryFeeInfo.payFeeGroup + QString("|0");
            gantryFeeInfo.feeGroup = gantryFeeInfo.payFeeGroup;
            gantryFeeInfo.discountFeeGroup = gantryFeeInfo.discountFeeGroup + QString("|0");
        }
        nIndex++;
    }
    
    // 移除开头的分隔符
    if (gantryFeeInfo.tollIntervalIDs_raw.length() > 0)
        gantryFeeInfo.tollIntervalIDs_raw.remove(0, 1);
    if (gantryFeeInfo.tollIntervalIDs.length() > 0) 
        gantryFeeInfo.tollIntervalIDs.remove(0, 1);
    if (gantryFeeInfo.tollIntervalSign.length() > 0) 
        gantryFeeInfo.tollIntervalSign.remove(0, 1);

    transInfo.gantryFeeInfo = gantryFeeInfo;

    // 设置车辆入口信息（补费时构造虚拟入口信息）
    CVehEntryInfo entryInfo;
    entryInfo.nEntryType = Entry_ByManual;
    entryInfo.dwEnStationID = Ptr_Info->GetStationID();
    entryInfo.sExStationName = Ptr_Info->GetStationName();
    entryInfo.nEnLaneID = Ptr_Info->GetLaneId();
    entryInfo.EnTime = curTime;
    entryInfo.bEnLaneType = 1;
    entryInfo.bEnVC = vehInfo.VehClass;
    entryInfo.bEnVT = vehInfo.GBVehType;
    entryInfo.bEnVLPC = vehInfo.nVehPlateColor;
    qstrncpy(entryInfo.szEnVLP, vehInfo.szVehPlate, sizeof(entryInfo.szEnVLP));
    entryInfo.sEnNetWorkIdHex = ORG_NETWORKID_HEX;
    entryInfo.sEnStaionName = Ptr_Info->GetStationName();
    entryInfo.sEnLaneHex = Ptr_Info->GetHexLaneID().right(2);
    entryInfo.sEnStationHex = Ptr_Info->GetHexStationID().right(4);
    entryInfo.sLastGantryId.clear();
    entryInfo.dwTotalWeight = 15000;
    entryInfo.dwWeightLimit = 49000;
    entryInfo.VehicalAxles = GetVehAxisNumByVC(vehInfo.VehClass);
    entryInfo.bVehState = 0xff;
    entryInfo.gantryPassTime = 0;
    entryInfo.sEnGBStationId = Ptr_Info->GetGBStationId();
    entryInfo.sEnGBLaneId = Ptr_Info->GetGBLaneId();

    transInfo.SetVehEntryInfo(entryInfo, curTime);
    
    // 设置其他交易属性
    transInfo.mediaType = MediaType_None;
    transInfo.actualFeeClass = FeeClass_Min;
    transInfo.m_bRepay = true;
    transInfo.m_bVehState = 0xff;
    transInfo.m_transPayType = payType;
    
    // 完成交易设置
    transInfo.CompleteTrans(0, payType, NULL, Tr_Successed);
    
    InfoLog(QString("交易信息构造完成 - 流水准备保存"));
}

bool RepayManager::NotifyProvinceComplete(const QString &vehPlate, int vehPlateColor,
                                         const QString &oweFee, const QString &wasteId,
                                         const QString &listno)
{
    if (!m_pProDebPayment) {
        ErrorLog("省中心接口未初始化");
        return false;
    }
    
    InfoLog(QString("通知省中心补费完成 - 车牌:%1, 金额:%2分, 流水:%3")
            .arg(vehPlate).arg(oweFee).arg(wasteId));
    
    // 异步通知省中心
    m_pProDebPayment->NotifyDebtCompleteAsync(vehPlate, vehPlateColor, 
                                             oweFee, wasteId, listno);
    
    return true;
}

void RepayManager::CancelRepay()
{
    QMutexLocker locker(&m_repayMutex);
    
    InfoLog(QString("取消补费流程 - 车牌:%1").arg(m_currentVehPlate));
    
    ClearCurrentRepayInfo();
    emit RepayCompleted(false, "用户取消操作");
}

void RepayManager::SetCurrentRepayInfo(const QString &vehPlate, int vehPlateColor, 
                                      int vehType, int amount)
{
    m_currentVehPlate = vehPlate;
    m_currentVehPlateColor = vehPlateColor;
    m_currentVehType = vehType;
    m_currentAmount = amount;
}

bool RepayManager::ValidateVehicleInfo(const QString &vehPlate, int vehPlateColor)
{
    if (vehPlate.isEmpty()) {
        WarnLog("车牌号为空");
        return false;
    }
    
    // 验证车牌号格式
    // QRegExp plateRegex("^[\\u4e00-\\u9fa5][A-Z][A-Z0-9]{5}$|^[A-Z0-9]{6,8}$");
    // if (!plateRegex.exactMatch(vehPlate)) {
    //     WarnLog(QString("车牌号格式无效：%1").arg(vehPlate));
    //     return false;
    // }
    
    // // 验证车牌颜色
    // if (vehPlateColor < 0 || vehPlateColor > 9) {
    //     WarnLog(QString("车牌颜色无效：%1").arg(vehPlateColor));
    //     return false;
    // }
    
    // DebugLog(QString("车牌验证通过：%1 (颜色:%2)").arg(vehPlate).arg(vehPlateColor));
    return true;
}

bool RepayManager::IsAuthorizationRequired() const
{
    return m_pRepayConfig ? m_pRepayConfig->IsRequireAuth() : true;
}

QList<CTransPayType> RepayManager::GetAvailablePaymentTypes() const
{
    QList<CTransPayType> paymentTypes;
    paymentTypes << TransPT_Cash;      // 现金
    paymentTypes << TransPT_Union;     // 银联卡
    paymentTypes << TransPT_AliPay;    // 支付宝
    paymentTypes << TransPT_WeChat;    // 微信
    return paymentTypes;
}

bool RepayManager::ValidateRepayConditions(RepayType type, const QString &vehPlate, int vehPlateColor)
{
    // 验证车辆信息
//    if (!ValidateVehicleInfo(vehPlate, vehPlateColor)) {
//        emit ErrorOccurred(RepayError_VehicleInfoInvalid, "车辆信息无效");
//        return false;
//    }
    
    // 检查是否需要授权
    if (IsAuthorizationRequired()) {
        if (!m_pAuthManager->IsAuthorizationValid()) {
            WarnLog("需要班长授权");
            emit ErrorOccurred(RepayError_AuthFailed, "需要班长授权");
            return false;
        }
    }
    
    return true;
}

bool RepayManager::CheckAmountLimit(int vehType, int amount)
{
    return ValidateRepayAmount(vehType, amount);
}

QString RepayManager::GenerateWasteId()
{
    // 生成格式：YYYYMMDDHHMMSS + 4位随机数
    QString timeStr = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    int randomNum = qrand() % 10000;
    return QString("%1%2").arg(timeStr).arg(randomNum, 4, 10, QChar('0'));
}

void RepayManager::LogRepayOperation(RepayType type, const QString &vehPlate, int vehPlateColor,
                                    int amount, CTransPayType payType, bool success, 
                                    const QString &errorMsg, const QString &orderIds,
                                    const QString &listno, const QString &wasteId)
{
    RepayOperationRecord record;
    record.operatorId = m_pAuthManager ? m_pAuthManager->GetCurrentAuthorizedOperator() : "";
    record.operationType = type;
    record.vehPlate = vehPlate;
    record.vehPlateColor = vehPlateColor;
    record.vehType = m_currentVehType;
    record.repayAmount = amount;
    record.paymentType = payType;
    record.orderIds = orderIds;
    record.operationTime = QDateTime::currentDateTime();
    record.result = success ? 1 : 0;
    record.errorMsg = errorMsg;
    record.wasteId = wasteId;
    record.remark = QString("补费类型:%1").arg(GetRepayTypeName(type));
    
    // 记录到数据库
    RepayDbManager::GetInstance()->LogRepayOperation(record);
    
    QString logMsg = QString("补费操作记录 - 车牌:%1, 类型:%2, 金额:%3分, 结果:%4")
                    .arg(vehPlate)
                    .arg(GetRepayTypeName(type))
                    .arg(amount)
                    .arg(success ? "成功" : "失败");
    
    if (success) {
        InfoLog(logMsg);
    } else {
        ErrorLog(logMsg + QString(", 错误:%1").arg(errorMsg));
    }
}

void RepayManager::SetRepayStage(RepayStage stage)
{
    if (m_currentStage != stage) {
        m_currentStage = stage;
        InfoLog(QString("补费阶段变更：%1").arg(GetRepayStageName(stage)));
        emit RepayStageChanged(stage);
    }
}

void RepayManager::ClearCurrentRepayInfo()
{
    m_currentRepayType = RepayType_None;
    m_currentStage = RepayStage_None;
    m_currentVehPlate.clear();
    m_currentVehPlateColor = 0;
    m_currentVehType = 0;
    m_currentAmount = 0;
    m_currentListno.clear();
    m_currentWasteId.clear();
    // 移除 m_bRepayInProgress = false; 因为不再使用状态控制

    // 清空欠费结果
    m_currentDebtResult = RepayDebtQueryResult();
}

void RepayManager::HandlePaymentSuccess(CTransPayType payType, int amount)
{
    InfoLog(QString("支付成功 - 车型:%1, 支付方式:%2, 金额:%3分").arg(m_currentVehType).arg(payType).arg(amount));
    
    // 专项作业车支付成功特别诊断
    if (m_currentVehType >= 21 && m_currentVehType <= 26) {
        InfoLog(QString("【专项作业车诊断】支付成功，准备生成流水 - 车型:%1").arg(m_currentVehType));
    }
    
    // 设置完成阶段
    SetRepayStage(RepayStage_Complete);
    
    // *** 关键修复：生成补费流水记录 ***
    CTransInfo transInfo;
    QString orderIds;
    if (m_currentRepayType == RepayType_Province) {
        // 省内名单补费：从欠费明细中提取订单ID
        for (int i = 0; i < m_currentDebtResult.debtItems.size(); i++) {
            const RepayDebtItem &item = m_currentDebtResult.debtItems[i];
            if (!orderIds.isEmpty()) {
                orderIds += "|";
            }
            orderIds += item.orderIds;
        }
    }
    
    // 生成并保存补费流水 - 这是缺失的关键步骤！
    bool wasteResult = GenerateRepayRecord(transInfo, m_currentRepayType, orderIds);
    if (!wasteResult) {
        ErrorLog("补费流水保存失败，但支付已成功");
    } else {
        InfoLog("补费流水保存成功");
    }
    
    // 如果是省内名单补费，需要通知省中心
    if (m_currentRepayType == RepayType_Province) {
        bool notifyResult = NotifyProvinceComplete(m_currentVehPlate, m_currentVehPlateColor,
                                                  QString::number(amount), m_currentWasteId,
                                                  m_currentListno);
        if (!notifyResult) {
            WarnLog("省中心通知启动失败，但支付已成功");
        }
    }
    
    emit PaymentCompleted(true, "支付成功");
    emit RepayCompleted(true, "补费完成");

    // 无状态设计：不需要清理状态信息
    // ClearCurrentRepayInfo(); // 移除状态清理
}

void RepayManager::HandlePaymentFailure(const QString &errorMsg)
{
    QString enhancedErrorMsg = GetEnhancedErrorMessage(errorMsg);
    ErrorLog(QString("支付失败：%1").arg(errorMsg));
    ErrorLog(QString("增强错误提示：%1").arg(enhancedErrorMsg));
    
    emit PaymentCompleted(false, enhancedErrorMsg);
    emit ErrorOccurred(RepayError_PaymentFailed, enhancedErrorMsg);
    
    // 从欠费明细中提取订单ID
    QString orderIds;
    for (int i = 0; i < m_currentDebtResult.debtItems.size(); i++) {
        const RepayDebtItem &item = m_currentDebtResult.debtItems[i];
        if (!orderIds.isEmpty()) {
            orderIds += "|";
        }
        orderIds += item.orderIds;
    }
    
    // 记录失败操作
    LogRepayOperation(m_currentRepayType, m_currentVehPlate, m_currentVehPlateColor,
                     m_currentAmount, TransPT_None, false, errorMsg, 
                     orderIds, m_currentListno, "");
}

QString RepayManager::GetEnhancedErrorMessage(const QString &originalError)
{
    // 在RepayManager层面提供错误信息增强
    QString enhanced = originalError;
    
    if (originalError.contains("支付金额超出限制") || originalError.contains("超限")) {
        enhanced = QString("补费金额超出限制！\n车型：%1\n当前限额：%2元\n请重新确认金额")
                  .arg(GetVehClassName(m_currentVehType))
                  .arg(m_pRepayConfig ? m_pRepayConfig->GetMaxFee(m_currentVehType) / 100.0 : 150.0, 0, 'f', 2);
    }
    else if (originalError.contains("网络") || originalError.contains("连接")) {
        enhanced = "网络通信失败！\n请检查网络连接状态\n或联系技术支持";
    }
    else if (originalError.contains("系统错误") || originalError.contains("内部错误")) {
        enhanced = "系统内部错误！\n请联系管理员处理\n错误代码：" + QString::number(QDateTime::currentMSecsSinceEpoch() % 10000);
    }
    
    return enhanced;
}

bool RepayManager::ValidateProvinceRepayResult(const RepayDebtQueryResult &result)
{
    if (result.listno.isEmpty()) {
        WarnLog("省内欠费查询结果无效 - 查询编号为空");
        return false;
    }
    
    if (result.totalAmount <= 0) {
        WarnLog("省内欠费金额无效");
        return false;
    }
    
    if (result.debtItems.isEmpty()) {
        WarnLog("省内欠费明细为空");
        return false;
    }
    
    return true;
}

void RepayManager::OnAuthorizationCompleted(bool success)
{
    if (success) {
        InfoLog("授权验证成功");
        SetRepayStage(RepayStage_VehicleInput);
    } else {
        ErrorLog("授权验证失败");
        emit ErrorOccurred(RepayError_AuthFailed, "授权验证失败");
        ClearCurrentRepayInfo();
    }
}

void RepayManager::OnDebtQueryFinished(bool success, const RepayDebtQueryResult &result)
{
    if (success) {
        InfoLog(QString("欠费查询成功 - 车牌:%1, 欠费:%2分")
                .arg(m_currentVehPlate)
                .arg(result.totalAmount));
        
        emit DebtQueryCompleted(true, result);
        
        if (!result.listno.isEmpty() && result.totalAmount > 0) {
            // 有欠费记录，处理省内名单补费
            ProcessProvinceRepay(m_currentVehPlate, m_currentVehPlateColor, result);
        } else {
            // 无欠费记录
            emit RepayCompleted(false, "该车辆无欠费记录");
            ClearCurrentRepayInfo();
        }
    } else {
        ErrorLog("欠费查询失败");
        emit DebtQueryCompleted(false, RepayDebtQueryResult());
        emit ErrorOccurred(RepayError_DebtQueryFailed, "欠费查询失败");
        ClearCurrentRepayInfo();
    }
}

void RepayManager::OnNotifyCompleteFinished(bool success, const QString &result)
{
    if (success) {
        InfoLog(QString("省中心通知成功：%1").arg(result));
    } else {
        WarnLog(QString("省中心通知失败：%1").arg(result));
        // 注意：即使通知失败，补费流程已经完成，不应影响整体结果
    }
}

bool RepayManager::ProcessCashPayment(int amount, const QString &vehPlate, int vehPlateColor, QString &errorMessage)
{
    InfoLog(QString("处理现金支付 - 车牌:%1, 金额:%2分").arg(vehPlate).arg(amount));
    
    try {
        // 现金支付直接处理，无需外部接口调用
        // 1. 验证金额
        if (amount <= 0) {
            errorMessage = "支付金额无效";
            return false;
        }
        
        // 2. 检查现金箱状态（如果有硬件接口）
        // TODO: 调用现金箱状态检查接口
        
        // 3. 生成电子发票
        QString invoiceNo = GenerateElectronicInvoice(amount, vehPlate, vehPlateColor);
        if (invoiceNo.isEmpty()) {
            WarnLog("电子发票生成失败，但现金支付继续处理");
        } else {
            InfoLog(QString("电子发票生成成功：%1").arg(invoiceNo));
        }
        
        // 4. 记录现金收款
        RecordCashCollection(amount, vehPlate, vehPlateColor, invoiceNo);
        
        InfoLog("现金支付处理成功");
        return true;
        
    } catch (const std::exception &e) {
        errorMessage = QString("现金支付处理异常：%1").arg(e.what());
        ErrorLog(errorMessage);
        return false;
    }
}

bool RepayManager::ProcessMobilePayment(CTransPayType payType, int amount, const QString &vehPlate, 
                                       int vehPlateColor, QString &errorMessage)
{
    InfoLog(QString("处理移动支付 - 车牌:%1, 支付方式:%2, 金额:%3分")
            .arg(vehPlate).arg(payType).arg(amount));
    
    try {
        // 1. 验证支付参数
        if (amount <= 0) {
            errorMessage = "支付金额无效";
            return false;
        }
        
        // 2. 检查网络连接
        if (!CheckNetworkConnection()) {
            errorMessage = "网络连接异常，无法进行移动支付";
            return false;
        }
        
        // 3. 根据支付类型调用相应接口
        QString paymentOrderId;
        bool paymentResult = false;
        
        switch (payType) {
            case TransPT_AliPay:
                paymentResult = ProcessAlipayPayment(amount, vehPlate, paymentOrderId, errorMessage);
                break;
                
            case TransPT_WeChat:
                paymentResult = ProcessWeChatPayment(amount, vehPlate, paymentOrderId, errorMessage);
                break;
                
            case TransPT_Union:
                paymentResult = ProcessUnionPayment(amount, vehPlate, paymentOrderId, errorMessage);
                break;
                
            default:
                errorMessage = QString("不支持的移动支付方式：%1").arg(payType);
                return false;
        }
        
        if (paymentResult) {
            // 4. 记录支付订单
            RecordMobilePaymentOrder(payType, amount, vehPlate, vehPlateColor, paymentOrderId);
            InfoLog(QString("移动支付处理成功，订单号：%1").arg(paymentOrderId));
        }
        
        return paymentResult;
        
    } catch (const std::exception &e) {
        errorMessage = QString("移动支付处理异常：%1").arg(e.what());
        ErrorLog(errorMessage);
        return false;
    }
}

bool RepayManager::ProcessETCPayment(int amount, const QString &vehPlate, int vehPlateColor, QString &errorMessage)
{
    InfoLog(QString("处理ETC卡支付 - 车牌:%1, 金额:%2分").arg(vehPlate).arg(amount));
    
    try {
        // 1. 验证支付参数
        if (amount <= 0) {
            errorMessage = "支付金额无效";
            return false;
        }
        
        // 2. 检查ETC卡机状态
        if (!CheckETCCardReaderStatus()) {
            errorMessage = "ETC卡机状态异常";
            return false;
        }
        
        // 3. 等待插卡
        if (!WaitForETCCardInsert()) {
            errorMessage = "等待插卡超时";
            return false;
        }
        
        // 4. 读取卡片信息
        CProCardBasicInfo cardInfo;
        quint32 dwBalance = 0;
        if (!ReadETCCardInfo(cardInfo, dwBalance, errorMessage)) {
            return false;
        }
        
        // 5. 验证卡片状态
        if (!ValidateETCCardStatus(cardInfo, dwBalance, errorMessage)) {
            return false;
        }
        
        // 6. 检查卡片余额
        if (dwBalance < (quint32)amount) {
            errorMessage = QString("卡片余额不足，当前余额：%1分，需要：%2分")
                          .arg(dwBalance).arg(amount);
            return false;
        }
        
        // 7. 执行扣费操作
        QString transactionId;
        if (!ExecuteETCDeduction(cardInfo, dwBalance, amount, transactionId, errorMessage)) {
            return false;
        }
        
        // 8. 记录ETC交易
        RecordETCTransaction(amount, vehPlate, vehPlateColor, cardInfo, transactionId);
        
        InfoLog(QString("ETC卡支付处理成功，交易流水：%1").arg(transactionId));
        return true;
        
    } catch (const std::exception &e) {
        errorMessage = QString("ETC卡支付处理异常：%1").arg(e.what());
        ErrorLog(errorMessage);
        return false;
    }
}

// 现金支付辅助方法实现
QString RepayManager::GenerateElectronicInvoice(int amount, const QString &vehPlate, int vehPlateColor)
{
    // 生成电子发票号码：日期时间 + 4位随机数
    QString invoiceNo = QDateTime::currentDateTime().toString("yyyyMMddhhmmss") + 
                       QString("%1").arg(qrand() % 10000, 4, 10, QChar('0'));
    
    InfoLog(QString("生成电子发票 - 发票号：%1, 金额：%2分, 车牌：%3")
            .arg(invoiceNo).arg(amount).arg(vehPlate));
    
    // TODO: 调用实际的电子发票生成接口
    // 这里可以集成税务系统的发票接口
    
    return invoiceNo;
}

void RepayManager::RecordCashCollection(int amount, const QString &vehPlate, int vehPlateColor, const QString &invoiceNo)
{
    InfoLog(QString("记录现金收款 - 金额：%1分, 车牌：%2, 发票号：%3")
            .arg(amount).arg(vehPlate).arg(invoiceNo));
    
    // TODO: 记录现金收款到数据库
    // 可以调用财务系统接口记录现金流水
}

// 移动支付辅助方法实现
bool RepayManager::CheckNetworkConnection()
{
    // TODO: 实现网络连接检查
    // 可以ping支付服务器或检查网络接口状态
    InfoLog("检查网络连接状态");
    return true; // 暂时返回true
}

bool RepayManager::ProcessAlipayPayment(int amount, const QString &vehPlate, QString &orderId, QString &errorMessage)
{
    InfoLog(QString("处理支付宝支付 - 金额：%1分, 车牌：%2").arg(amount).arg(vehPlate));
    
    try {
        // 生成订单号
        orderId = "ALI" + QDateTime::currentDateTime().toString("yyyyMMddhhmmss") + 
                 QString("%1").arg(qrand() % 10000, 4, 10, QChar('0'));
        
        // TODO: 调用支付宝支付接口
        // 1. 创建支付订单
        // 2. 调用支付宝SDK
        // 3. 等待支付结果回调
        
        InfoLog(QString("支付宝支付成功，订单号：%1").arg(orderId));
        return true;
        
    } catch (const std::exception &e) {
        errorMessage = QString("支付宝支付异常：%1").arg(e.what());
        ErrorLog(errorMessage);
        return false;
    }
}

bool RepayManager::ProcessWeChatPayment(int amount, const QString &vehPlate, QString &orderId, QString &errorMessage)
{
    InfoLog(QString("处理微信支付 - 金额：%1分, 车牌：%2").arg(amount).arg(vehPlate));
    
    try {
        // 生成订单号
        orderId = "WX" + QDateTime::currentDateTime().toString("yyyyMMddhhmmss") + 
                 QString("%1").arg(qrand() % 10000, 4, 10, QChar('0'));
        
        // TODO: 调用微信支付接口
        // 1. 创建支付订单
        // 2. 调用微信支付SDK
        // 3. 等待支付结果回调
        
        InfoLog(QString("微信支付成功，订单号：%1").arg(orderId));
        return true;
        
    } catch (const std::exception &e) {
        errorMessage = QString("微信支付异常：%1").arg(e.what());
        ErrorLog(errorMessage);
        return false;
    }
}

bool RepayManager::ProcessUnionPayment(int amount, const QString &vehPlate, QString &orderId, QString &errorMessage)
{
    InfoLog(QString("处理银联卡支付 - 金额：%1分, 车牌：%2").arg(amount).arg(vehPlate));
    
    try {
        // 生成订单号
        orderId = "UP" + QDateTime::currentDateTime().toString("yyyyMMddhhmmss") + 
                 QString("%1").arg(qrand() % 10000, 4, 10, QChar('0'));
        
        // TODO: 调用银联支付接口
        // 1. 创建支付订单
        // 2. 调用银联支付SDK
        // 3. 等待支付结果回调
        
        InfoLog(QString("银联卡支付成功，订单号：%1").arg(orderId));
        return true;
        
    } catch (const std::exception &e) {
        errorMessage = QString("银联卡支付异常：%1").arg(e.what());
        ErrorLog(errorMessage);
        return false;
    }
}

void RepayManager::RecordMobilePaymentOrder(CTransPayType payType, int amount, const QString &vehPlate, 
                                           int vehPlateColor, const QString &orderId)
{
    QString payTypeName;
    switch (payType) {
        case TransPT_AliPay: payTypeName = "支付宝"; break;
        case TransPT_WeChat: payTypeName = "微信"; break;
        case TransPT_Union: payTypeName = "银联卡"; break;
        default: payTypeName = "未知"; break;
    }
    
    InfoLog(QString("记录移动支付订单 - 支付方式：%1, 金额：%2分, 车牌：%3, 订单号：%4")
            .arg(payTypeName).arg(amount).arg(vehPlate).arg(orderId));
    
    // TODO: 记录支付订单到数据库
    // 可以调用支付系统接口记录订单信息
}

// ETC卡支付辅助方法实现
bool RepayManager::CheckETCCardReaderStatus()
{
    InfoLog("检查ETC卡机状态");
    
    // TODO: 调用ETC卡机状态检查接口
    // 检查卡机是否在线、是否正常工作
    
    return true; // 暂时返回true
}

bool RepayManager::WaitForETCCardInsert()
{
    InfoLog("等待ETC卡插入");
    
    // TODO: 实现等待插卡逻辑
    // 可以设置超时时间，等待用户插卡
    // 可以显示提示信息引导用户操作
    
    return true; // 暂时返回true
}

bool RepayManager::ReadETCCardInfo(CProCardBasicInfo &cardInfo, quint32 &dwBalance, QString &errorMessage)
{
    InfoLog("读取ETC卡信息");
    
    try {
        // TODO: 调用ETC卡机读卡接口
        // 读取卡片基本信息和余额
        
        // 暂时模拟卡片信息
        memset(&cardInfo, 0, sizeof(cardInfo));
        strcpy(cardInfo.szCardNo, "1234567890123456");
        strcpy(cardInfo.szNetworkId, "3601");
        cardInfo.bType = CARD_TYPE_STORE_CARD;
        cardInfo.wNetWorkId = 3601;
        dwBalance = 10000; // 100元
        
        InfoLog(QString("读取ETC卡成功 - 卡号：%1, 余额：%2分")
                .arg(QString::fromAscii(cardInfo.szCardNo)).arg(dwBalance));
        
        return true;
        
    } catch (const std::exception &e) {
        errorMessage = QString("读取ETC卡信息异常：%1").arg(e.what());
        ErrorLog(errorMessage);
        return false;
    }
}

bool RepayManager::ValidateETCCardStatus(const CProCardBasicInfo &cardInfo, quint32 dwBalance, QString &errorMessage)
{
    InfoLog("验证ETC卡状态");
    
    // 1. 检查卡片类型
    if (cardInfo.bType != CARD_TYPE_STORE_CARD && cardInfo.bType != CARD_TYPE_TALLY_CARD) {
        errorMessage = QString("不支持的卡片类型：%1").arg(cardInfo.bType);
        return false;
    }
    
    // 2. 检查网络号
    if (cardInfo.wNetWorkId == 0) {
        errorMessage = "无效的网络号";
        return false;
    }
    
    // 3. 检查卡号
    QString cardNo = QString::fromAscii(cardInfo.szCardNo);
    if (cardNo.isEmpty() || cardNo.length() < 16) {
        errorMessage = "无效的卡号";
        return false;
    }
    
    // TODO: 可以添加更多验证逻辑
    // 如检查卡片有效期、黑名单等
    
    InfoLog("ETC卡状态验证通过");
    return true;
}

bool RepayManager::ExecuteETCDeduction(const CProCardBasicInfo &cardInfo, quint32 dwBalance, int amount, 
                                      QString &transactionId, QString &errorMessage)
{
    InfoLog(QString("执行ETC卡扣费 - 金额：%1分, 卡内余额：%2分").arg(amount).arg(dwBalance));
    
    try {
        // 生成交易流水号
        transactionId = "ETC" + QDateTime::currentDateTime().toString("yyyyMMddhhmmss") + 
                       QString("%1").arg(qrand() % 10000, 4, 10, QChar('0'));
        
        // TODO: 调用ETC卡机扣费接口
        // 1. 验证卡片MAC
        // 2. 执行扣费操作
        // 3. 更新卡片余额
        // 4. 生成交易记录
        
        InfoLog(QString("ETC卡扣费成功 - 交易流水：%1, 扣费金额：%2分")
                .arg(transactionId).arg(amount));
        
        return true;
        
    } catch (const std::exception &e) {
        errorMessage = QString("ETC卡扣费异常：%1").arg(e.what());
        ErrorLog(errorMessage);
        return false;
    }
}

void RepayManager::RecordETCTransaction(int amount, const QString &vehPlate, int vehPlateColor, 
                                       const CProCardBasicInfo &cardInfo, const QString &transactionId)
{
    QString cardNo = QString::fromAscii(cardInfo.szCardNo);
    
    InfoLog(QString("记录ETC交易 - 金额：%1分, 车牌：%2, 卡号：%3, 交易流水：%4")
            .arg(amount).arg(vehPlate).arg(cardNo).arg(transactionId));
    
    // TODO: 记录ETC交易到数据库
    // 可以调用交易系统接口记录交易信息
}
