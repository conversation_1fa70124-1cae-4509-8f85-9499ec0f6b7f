#include "mobilepaydual.h"
#include <QDateTime>
#include <QApplication>
#include <QWidget>
#include <QMetaObject>
#include <QEventLoop>
#include <QTimer>
#include <QThread>
#include <QTextCodec>
#include "qxtjson.h" // 使用项目中已有的JSON处理库
#include "ilogmsg.h" // 包含日志消息接口
#include "jsonbuilder.h"


// 包含Windows API头文件或定义WM_USER
#ifdef Q_OS_WIN
#include <windows.h>
#else
// 在非Windows平台上定义WM_USER
#define WM_USER 0x0400
#endif

// 静态成员初始化
bool MobilePayDual::m_bDriverLoaded = false;
QLibrary MobilePayDual::m_hLibModule;

/**
 * @brief MobilePayDual构造函数
 * @details 初始化成员变量
 */
MobilePayDual::MobilePayDual() : MobilePayBase()
{
    m_bDevInited = false;

    // 初始化函数指针
    m_pFun_InitEnvironment = NULL;
    m_pFun_Destroy = NULL;
    m_pFun_DebitMoney = NULL;
    m_pFun_DebitCancel = NULL;
    m_pFun_GetDebitResult = NULL;
    
    // 初始化消息相关
    m_nNotifyMsgID = m_config.nNotifyMsgID;
    
    // 初始化同步等待机制
    m_pEventLoop = NULL;
    m_pTimeoutTimer = new QTimer(this);
    m_pTimeoutTimer->setSingleShot(true);
    m_bWaitingResult = false;
    m_currentPayIdentifier.clear();
    
    // 连接内部信号槽用于同步等待（不与MainWindow的连接冲突）
    connect(this, SIGNAL(internalResultSignal(QString)), this, SLOT(onAsyncPayResult(QString)));
    connect(this, SIGNAL(internalStatusSignal(int,int,QString)), this, SLOT(onAsyncDeviceStatus(int,int,QString)));
    connect(m_pTimeoutTimer, SIGNAL(timeout()), this, SLOT(onAsyncPayResult(QString)));
}

/**
 * @brief MobilePayDual析构函数
 * @details 释放资源
 */
MobilePayDual::~MobilePayDual()
{
    try 
    {
        DebugLog(QString::fromUtf8("MobilePayDual:析构函数开始"));
        CloseDev();
        DebugLog(QString::fromUtf8("MobilePayDual:析构函数完成"));
    }
    catch (...)
    {
        // 析构函数中不应该抛出异常，捕获所有异常
        // 这里无法使用DebugLog，因为对象可能已经部分销毁
        // 只能静默处理，避免程序异常终止
    }
}

/**
 * @brief 启动设备
 * @return 成功返回true，失败返回false
 */
bool MobilePayDual::StartDev()
{
    // 已初始化则直接返回
    if (m_bDevInited)
        return true;

    // 加载驱动
    if (!LoadDriver())
        return false;

    // 调用初始化环境函数
    if (m_pFun_InitEnvironment)
    {
        // 获取主窗口句柄
        QWidget* mainWindow = QApplication::activeWindow();
        HWND hWnd = NULL;
        if (mainWindow) {
            hWnd = (HWND)mainWindow->winId();
        }
        
        // 获取当前线程ID
        UINT nThreadID = 0;  // 如果使用窗口句柄，线程ID可以为0
        
        // 从配置中获取参数
        m_nNotifyMsgID = m_config.nNotifyMsgID;
        QByteArray areaInfoBytes = m_config.szAreaInfo.toLocal8Bit();
        QByteArray stationBytes = m_config.szLoaclStation.toLocal8Bit();
        QByteArray laneBytes = m_config.szLoaclLaneID.toLocal8Bit();
        QByteArray serverInfoBytes = m_config.szServerInfo.toLocal8Bit();
        int iProvinceID = m_config.iProvinceID;
        
        // 调用新的初始化环境函数
        bool bRet = m_pFun_InitEnvironment(
            nThreadID, 
            hWnd, 
            m_nNotifyMsgID, 
            areaInfoBytes.constData(), 
            stationBytes.constData(), 
            laneBytes.constData(), 
            serverInfoBytes.constData(), 
            iProvinceID
        );
        
        if (!bRet)
        {
            DebugLog(QString::fromUtf8("MobilePayDual:初始化环境失败"));
            return false;
        }
    }

    // Qt 4.8.5中通过MainWindow的winEvent处理消息，这里仅记录配置
    DebugLog(QString::fromUtf8("MobilePayDual:消息处理已准备就绪，消息ID=0x%1")
             .arg(m_nNotifyMsgID, 0, 16));

    m_bDevInited = true;
    return true;
}

/**
 * @brief 关闭设备
 */
void MobilePayDual::CloseDev()
{
    if (m_bDevInited)
    {
        DebugLog(QString::fromUtf8("MobilePayDual:开始清理设备资源"));
        
        // 调用资源释放函数，添加异常保护
        if (m_pFun_Destroy && m_bDriverLoaded)
        {
            try 
            {
                DebugLog(QString::fromUtf8("MobilePayDual:调用IF_Destroy释放设备资源"));
                bool result = m_pFun_Destroy();
                DebugLog(QString::fromUtf8("MobilePayDual:IF_Destroy调用完成，返回值=%1").arg(result ? "true" : "false"));
                
                if (!result)
                {
                    DebugLog(QString::fromUtf8("MobilePayDual:WARNING - IF_Destroy返回false，可能存在问题"));
                }
            }
            catch (...)
            {
                DebugLog(QString::fromUtf8("MobilePayDual:ERROR - 调用IF_Destroy时发生异常"));
            }
        }
        else
        {
            if (!m_pFun_Destroy)
            {
                DebugLog(QString::fromUtf8("MobilePayDual:WARNING - m_pFun_Destroy为空，无法调用释放函数"));
            }
            if (!m_bDriverLoaded)
            {
                DebugLog(QString::fromUtf8("MobilePayDual:WARNING - 驱动未加载，跳过释放函数调用"));
            }
        }
        
        m_bDevInited = false;
        DebugLog(QString::fromUtf8("MobilePayDual:设备资源清理完成"));
    }

    // 释放驱动（在设备资源清理之后）
    ReleaseDriver();
}

/**
 * @brief 加载驱动
 * @return 成功返回true，失败返回false
 */
bool MobilePayDual::LoadDriver()
{
    // 已加载则直接返回
    if (m_bDriverLoaded)
        return true;

    // 加载DLL文件
    m_hLibModule.setFileName("TWSDNetPay.dll");
    if (!m_hLibModule.load())
    {
        DebugLog(QString::fromUtf8("MobilePayDual:加载TWSDNetPay.dll失败，错误=%1").arg(m_hLibModule.errorString()));
        return false;
    }

    // 获取函数指针
    m_pFun_InitEnvironment = (FUN_InitEnvironment)m_hLibModule.resolve("IF_InitEnvironment");
    m_pFun_Destroy = (FUN_Destroy)m_hLibModule.resolve("IF_Destroy");
    m_pFun_DebitMoney = (FUN_DebitMoney)m_hLibModule.resolve("IF_DebitMoney");
    m_pFun_DebitCancel = (FUN_DebitCancel)m_hLibModule.resolve("IF_DebitCancel");
    m_pFun_GetDebitResult = (FUN_GetDebitResult)m_hLibModule.resolve("IF_GetDebitResult");

    // 检查函数是否全部获取成功
    if (!m_pFun_InitEnvironment || !m_pFun_Destroy || !m_pFun_DebitMoney ||
        !m_pFun_DebitCancel || !m_pFun_GetDebitResult)
    {
        DebugLog(QString::fromUtf8("MobilePayDual:获取DLL导出函数失败"));
        m_hLibModule.unload();
        return false;
    }

    m_bDriverLoaded = true;
    return true;
}

/**
 * @brief 释放驱动
 */
void MobilePayDual::ReleaseDriver()
{
    if (m_bDriverLoaded)
    {
        DebugLog(QString::fromUtf8("MobilePayDual:开始释放驱动"));
        
        // 首先清空函数指针，防止在卸载过程中被调用
        DebugLog(QString::fromUtf8("MobilePayDual:清空函数指针"));
        m_pFun_InitEnvironment = NULL;
        m_pFun_Destroy = NULL;
        m_pFun_DebitMoney = NULL;
        m_pFun_DebitCancel = NULL;
        m_pFun_GetDebitResult = NULL;

        // 标记驱动已卸载（在实际卸载之前设置，防止重入）
        m_bDriverLoaded = false;
        
        // 给DLL内部组件一些时间完成清理（IF_Destroy已经花了很长时间）
        DebugLog(QString::fromUtf8("MobilePayDual:等待DLL内部组件完成清理"));
        QThread::msleep(500);  // 等待500毫秒
        
        // 由于DLL内部可能存在组件析构顺序问题，提供两种处理方式：
        // 方案1：主动卸载DLL（可能导致异常）
        // 方案2：不主动卸载，让系统在程序退出时自动处理（更安全）
        
        bool forceUnloadDll = false;  // 设为true则强制卸载，设为false则跳过卸载
        
        if (forceUnloadDll)
        {
            // 卸载DLL，添加异常保护
            try 
            {
                DebugLog(QString::fromUtf8("MobilePayDual:卸载DLL"));
                if (m_hLibModule.isLoaded())
                {
                    bool unloadResult = m_hLibModule.unload();
                    if (unloadResult)
                    {
                        DebugLog(QString::fromUtf8("MobilePayDual:DLL卸载成功"));
                    }
                    else
                    {
                        DebugLog(QString::fromUtf8("MobilePayDual:WARNING - DLL卸载失败: %1").arg(m_hLibModule.errorString()));
                    }
                }
                else
                {
                    DebugLog(QString::fromUtf8("MobilePayDual:DLL已经处于未加载状态"));
                }
                
                // 卸载后再给一些时间，让系统完成内部清理
                DebugLog(QString::fromUtf8("MobilePayDual:等待系统完成DLL清理"));
                QThread::msleep(200);  // 等待200毫秒
            }
            catch (...)
            {
                DebugLog(QString::fromUtf8("MobilePayDual:ERROR - 卸载DLL时发生异常"));
            }
        }
        else
        {
            // 更安全的方式：不主动卸载DLL，让系统在程序退出时自动处理
            DebugLog(QString::fromUtf8("MobilePayDual:跳过DLL卸载，由系统自动处理（避免DLL内部组件析构问题）"));
            DebugLog(QString::fromUtf8("MobilePayDual:DLL将在程序退出时由系统自动卸载"));
        }
        
        DebugLog(QString::fromUtf8("MobilePayDual:驱动释放完成"));
    }
    else
    {
        DebugLog(QString::fromUtf8("MobilePayDual:驱动未加载，无需释放"));
    }
}

/**
 * @brief 转换支付请求为JSON
 * @param payRequest 支付请求参数
 * @return GBK编码的JSON字符串
 */
QByteArray MobilePayDual::convertPayJson(const MBPay_PayRequest& payRequest)
{
    // 获取当前时间
    QString currentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    
    // 确保金额有值，如果为0则默认设为1
    int money = payRequest.debitMoney > 0 ? payRequest.debitMoney : 1;
    
    // 确保渠道类型有值
    int channelType = payRequest.ChannelType > 0 ? payRequest.ChannelType : 1;
    
    // 确保车辆类型和类别有值
    int vClass = payRequest.vehClass > 0 ? payRequest.vehClass : 1;
    int vType = payRequest.vehType > 0 ? payRequest.vehType : 1;
    
    // 确保班次ID有效
    int shiftId = 1;
    if (!payRequest.squadId.isEmpty()) {
        bool ok;
        int convertedId = payRequest.squadId.toInt(&ok);
        if (ok) shiftId = convertedId;
    }
    
    // 确保收费日期有效
    int tollDate = QDate::currentDate().toString("yyyyMMdd").toInt();
    if (!payRequest.squadDate.isEmpty()) {
        bool ok;
        int convertedDate = payRequest.squadDate.toInt(&ok);
        if (ok) tollDate = convertedDate;
    }
    
    // 从区域信息中解析AreaID和RoadID
    QString areaID = "0";
    QString roadID = "0";
    QStringList areaInfo = m_config.szAreaInfo.split("#");
    if (areaInfo.size() >= 2) {
        areaID = areaInfo[0];
        roadID = areaInfo[1];
    }
    
    // 从站点信息中解析StationID和StationName
    QString stationID = "0";
    QString stationName = "";
    QStringList stationInfo = QString::fromLocal8Bit(m_config.szLoaclStation.toLocal8Bit()).split("#");
    if (stationInfo.size() >= 2) {
        stationID = stationInfo[0];
        stationName = stationInfo[1];
    }
    
    // 获取车道号
    QString laneID = m_config.szLoaclLaneID;
    
    // 入口时间，如果没有则使用当前时间
    QString entryTime = payRequest.enOpTime.isEmpty() ? currentTime : payRequest.enOpTime;
    
    // 使用CJsonBuilder构建JSON
    // 1. 构建Debit部分
    CJsonBuilder builder(true, false);
    builder.AddKeyValue_Int(QString("Money"), money);
    builder.AddKeyValue(QString("PayIdentifier"), payRequest.payIdentifier);
    builder.AddKeyValue(QString("SubTime"), currentTime);
    builder.AddKeyValue_Int(QString("Type"), channelType);
    builder.AddKeyValue(QString("QRCode"), payRequest.payPlatformQrCode);
    builder.AddKeyValue_Int(QString("PayCertificateType"), 0);
    QString sDebit = builder.CreateJsonStr();
    sDebit.remove(0, 1);
    sDebit = CJsonBuilder::CreateDataMaster(QString("Debit"), sDebit);
    
    // 2. 构建Entry部分
    builder.ResetBuilder();
    builder.AddKeyValue(QString("AreaID"), areaID);
    builder.AddKeyValue(QString("RoadID"), roadID);
    builder.AddKeyValue(QString("LaneID"), laneID);
    builder.AddKeyValue(QString("License"), payRequest.vehPlate);
    builder.AddKeyValue(QString("StationID"), stationID);
    builder.AddKeyValue(QString("StationName"), QString::fromUtf8(stationName.toUtf8()));
    builder.AddKeyValue(QString("Time"), entryTime);
    builder.AddKeyValue_Int(QString("VClass"), vClass);
    builder.AddKeyValue_Int(QString("VColor"), payRequest.vehPlateColor);
    builder.AddKeyValue_Int(QString("VType"), vType);
    QString sEntry = builder.CreateJsonStr().remove(0, 1);
    sEntry = CJsonBuilder::CreateDataMaster(QString("Entry"), sEntry);
    
    // 3. 构建Operation部分
    builder.ResetBuilder();
    builder.AddKeyValue(QString("CardID"), "999988887777");
    builder.AddKeyValue_Int(QString("PassCertificateType"), 1);
    builder.AddKeyValue_Int(QString("ShiftID"), shiftId);
    builder.AddKeyValue_Int(QString("TollDate"), tollDate);
    builder.AddKeyValue(QString("TicketNo"), "");
    builder.AddKeyValue(QString("OperatorID"), payRequest.operatorNo);
    builder.AddKeyValue(QString("OperatorName"), payRequest.operatorName.isEmpty() ? QString::fromUtf8("未知") : payRequest.operatorName);
    builder.AddKeyValue_Int(QString("AutoPrint"), 1);
    builder.AddKeyValue_Int(QString("Distance"), 16);
    QString sOperation = builder.CreateJsonStr().remove(0, 1);
    sOperation = CJsonBuilder::CreateDataMaster(QString("Operation"), sOperation);
    
    // 4. 构建AutoPlate部分
    builder.ResetBuilder();
    builder.AddKeyValue(QString("AutoLicense"), payRequest.vehPlate);
    builder.AddKeyValue_Int(QString("AutoColor"), payRequest.vehPlateColor);
    QString sAutoPlate = builder.CreateJsonStr().remove(0, 1);
    sAutoPlate = CJsonBuilder::CreateDataMaster(QString("AutoPlate"), sAutoPlate);
    
    // 5. 构建Vehicle部分
    builder.ResetBuilder();
    builder.AddKeyValue_Int(QString("AxisCount"), payRequest.nAxisCount > 0 ? payRequest.nAxisCount : 2);
    builder.AddKeyValue_Int(QString("Class"), vClass);
    builder.AddKeyValue(QString("License"), payRequest.vehPlate);
    builder.AddKeyValue_Int(QString("Type"), vType);
    builder.AddKeyValue_Int(QString("VLColor"), payRequest.vehPlateColor);
    builder.AddKeyValue_Int(QString("Weight"), payRequest.totalWeight > 0 ? payRequest.totalWeight : 1000);
    QString sVehicle = builder.CreateJsonStr().remove(0, 1);
    sVehicle = QString("%1,%2").arg(sAutoPlate).arg(sVehicle);
    sVehicle = CJsonBuilder::CreateDataMaster(QString("Vehicle"), sVehicle);
    
    // 6. 构建基本参数
    builder.ResetBuilder();
    builder.AddKeyValue_Int(QString("PayModel"), 1);
    builder.AddKeyValue_Int(QString("OperationMode"), 1);
    builder.AddKeyValue_Int(QString("OverTime"), 30000);
    QString sBaseParams = builder.CreateJsonStr().remove(0, 1);
    
    // 7. 构建Data部分
    QString sData = QString("%1,%2,%3,%4,%5")
                        .arg(sBaseParams)
                        .arg(sDebit, sEntry, sOperation, sVehicle);
    sData = CJsonBuilder::CreateDataMaster(QString("Data"), sData);
    
    // 8. 构建最终JSON
    QString jsonString = QString("{\"DebitParamContext\":{\"Version\":\"1.0\",%1}}").arg(sData);
    
    // 转换为GBK编码
    QTextCodec *codec = QTextCodec::codecForName("GBK");
    return codec->fromUnicode(jsonString);
}
/**
 * @brief 转换查询请求为JSON
 * @param payIdentifier 支付标识符
 * @param submitCount 提交次数
 * @return JSON字符串
 */
QString MobilePayDual::convertQueryJson(QString payIdentifier, quint32 submitCount)
{
    // 获取当前时间
    QString currentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    
    // 使用CJsonBuilder构建JSON
    // 1. 构建Query部分
    CJsonBuilder builder(true, false);
    builder.AddKeyValue(QString("PayIdentifier"), payIdentifier);
    builder.AddKeyValue(QString("SubTime"), currentTime);
    builder.AddKeyValue_Int(QString("Type"), 4);
    QString sQuery = builder.CreateJsonStr();
    sQuery.remove(0, 1);
    sQuery = CJsonBuilder::CreateDataMaster(QString("Query"), sQuery);
    
    // 2. 构建版本部分
    builder.ResetBuilder();
    builder.AddKeyValue(QString("Version"), "1.0");
    QString sVersion = builder.CreateJsonStr().remove(0, 1);
    
    // 3. 构建Data部分
    QString sData = QString("%1,%2").arg(sVersion).arg(sQuery);
    sData = CJsonBuilder::CreateDataMaster(QString("Data"), sData);
    
    // 4. 构建最终JSON
    QString jsonString = QString("{\"QueryParamContext\":%1}").arg(sData);
    
    return jsonString;
}

/**
 * @brief 转换撤单请求为JSON
 * @param payIdentifier 支付标识符
 * @param submitCount 提交次数
 * @return JSON字符串
 */
QString MobilePayDual::convertReverseJson(QString payIdentifier, quint32 submitCount)
{
    // 使用CJsonBuilder构建JSON
    // 1. 构建TradeKeyItem部分
    CJsonBuilder builder(true, false);
    builder.AddKeyValue(QString("PayIdentifier"), payIdentifier);
    QString sTradeKeyItem = builder.CreateJsonStr();
    sTradeKeyItem.remove(0, 1); // 移除开头的 '{'
    sTradeKeyItem = CJsonBuilder::CreateDataMaster(QString("TradeKeyItem"), sTradeKeyItem);

    // 2. 构建Data部分（只包含TradeKeyItem）
    QString sData = CJsonBuilder::CreateDataMaster(QString("Data"), sTradeKeyItem);

    // 3. 构建版本部分
    builder.ResetBuilder();
    builder.AddKeyValue(QString("Version"), "1.0");
    QString sVersion = builder.CreateJsonStr().remove(0, 1); // 移除开头的 '{'

    // 4. 构建DebitCancelParamContext部分（包含Data和Version）
    QString sDebitCancel = QString("%1,%2").arg(sData).arg(sVersion);

    // 5. 构建最终JSON
    QString jsonString = QString("{\"DebitCancelParamContext\":{%1}}").arg(sDebitCancel);

    return jsonString;
}


/**
 * @brief 处理Windows原生消息（Qt 4.8.5兼容版本）
 * @param message 消息指针
 * @param result 处理结果
 * @return 是否已处理该消息
 */
bool MobilePayDual::processNativeMessage(void *message, long *result)
{
#ifdef Q_OS_WIN
    // 处理Windows消息
    MSG *msg = static_cast<MSG *>(message);
    
    // 检查是否是我们关注的消息
    if (msg->message == m_nNotifyMsgID) {
        // 解析WPARAM，其中包含了功能代码和状态代码
        WPARAM wParam = msg->wParam;
        LPARAM lParam = msg->lParam;
        
        // 提取功能代码（第1个字节）
        int funcCode = wParam & 0xFF;
        
        // 提取状态代码（第2个字节）
        int statusCode = (wParam >> 8) & 0xFF;
        
        // 从 LPARAM 获取数据指针
        const char* pData = reinterpret_cast<const char*>(lParam);
        QByteArray data;
        
        // 如果有数据，复制数据
        if (pData) {
            data = QByteArray(pData);
        }
        
        DebugLog(QString::fromUtf8("MobilePayDual:收到消息 - 功能代码=%1, 状态代码=%2")
            .arg(funcCode).arg(statusCode));
        
        // 打印接收到的数据内容
        if (data.size() > 0) {
            // 使用GBK编码将数据转换为QString并打印
            QTextCodec *codec = QTextCodec::codecForName("UTF-8");
            QString dataStr = codec->toUnicode(data);
            DebugLog(QString::fromUtf8("MobilePayDual:接收到数据内容 - 长度=%1, 数据=%2")
                .arg(data.size()).arg(dataStr));
        } else {
            DebugLog(QString::fromUtf8("MobilePayDual:接收到数据内容 - 无数据"));
        }
        
        // 异步处理消息，不阻塞事件循环
        QMetaObject::invokeMethod(this, "handleDeviceMessage", 
                                Qt::QueuedConnection,
                                Q_ARG(int, funcCode),
                                Q_ARG(int, statusCode),
                                Q_ARG(QByteArray, data));
        
        // 返回true表示消息已处理
        *result = 0;
        return true;
    }
#endif
    
    // 对于不处理的消息，返回false让Qt继续处理
    return false;
}

/**
 * @brief 处理设备消息
 * @param funcCode 功能代码
 * @param statusCode 状态代码
 * @param data 消息数据
 */
void MobilePayDual::handleDeviceMessage(int funcCode, int statusCode, QByteArray data)
{
    // 获取状态描述
    QString statusDesc = statusToString(funcCode, statusCode);
    
    // 根据功能代码处理
    switch(funcCode) {
        case MSGFUNC_DEBIT:  // 扣款回馈
            {
                QString logPrefix = QString::fromUtf8("MobilePayDual:扣款回馈(%1) - ").arg(statusDesc);
                
                if (data.size() > 0) {
                    // 转换为UTF-8字符串
                    QTextCodec *codec = QTextCodec::codecForName("UTF-8");
                    QString resultJson = codec->toUnicode(data);
                    
                    // 清理可能的空字符和空白字符
                    resultJson = resultJson.trimmed();
                    int nullPos = resultJson.indexOf(QChar('\0'));
                    if (nullPos != -1) {
                        resultJson = resultJson.left(nullPos);
                        DebugLog(QString::fromUtf8("MobilePayDual:清理扣款回馈消息中的空字符"));
                    }
                    
                    DebugLog(logPrefix + resultJson);
                    
                    // 发送状态信号
                    emit deviceStatusSignal(funcCode, statusCode, statusDesc);
                    
                    // 发送结果信号（只有当有JSON数据时）
                    emit debitResultSignal(resultJson);
                    
                    // 发送内部信号用于同步等待
                    emit internalStatusSignal(funcCode, statusCode, statusDesc);
                    emit internalResultSignal(resultJson);
                    
                    // 如果需要进一步解析结果，可以在这里添加代码
                    if (statusCode == DEBIT_SUCCESS || statusCode == DEBIT_GOT_CERTIFICATE) {
                        TW_RESULT result;
                        if (parseResultJson(resultJson, result)) {
                            DebugLog(QString::fromUtf8("MobilePayDual:解析支付结果成功，响应码=%1").arg(result.code));
                        }
                    }
                } else {
                    DebugLog(logPrefix + QString::fromUtf8("无数据"));
                    emit deviceStatusSignal(funcCode, statusCode, statusDesc);
                    
                    // 发送内部状态信号用于同步等待
                    emit internalStatusSignal(funcCode, statusCode, statusDesc);
                }
            }
            break;
            
        case MSGFUNC_CANCEL:  // 撤单回馈
            {
                QString logPrefix = QString::fromUtf8("MobilePayDual:撤单回馈(%1) - ").arg(statusDesc);
                
                if (data.size() > 0) {
                    // 转换为UTF-8字符串
                    QTextCodec *codec = QTextCodec::codecForName("UTF-8");
                    QString resultJson = codec->toUnicode(data);
                    
                    // 清理可能的空字符和空白字符
                    resultJson = resultJson.trimmed();
                    int nullPos = resultJson.indexOf(QChar('\0'));
                    if (nullPos != -1) {
                        resultJson = resultJson.left(nullPos);
                        DebugLog(QString::fromUtf8("MobilePayDual:清理撤单回馈消息中的空字符"));
                    }
                    
                    DebugLog(logPrefix + resultJson);
                    
                    // 发送状态信号
                    emit deviceStatusSignal(funcCode, statusCode, statusDesc);
                    
                    // 发送结果信号（撤单结果也通过支付结果信号发送）
                    emit debitResultSignal(resultJson);
                    
                    // 发送内部信号用于同步等待
                    emit internalStatusSignal(funcCode, statusCode, statusDesc);
                    emit internalResultSignal(resultJson);
                } else {
                    DebugLog(logPrefix + QString::fromUtf8("无数据"));
                    emit deviceStatusSignal(funcCode, statusCode, statusDesc);
                    
                    // 发送内部状态信号用于同步等待
                    emit internalStatusSignal(funcCode, statusCode, statusDesc);
                }
            }
            break;
            
        case MSGFUNC_UNATTENDED: // 无人值守
        case MSGFUNC_NONSTOP:    // 不停车
        case MSGFUNC_PAY_STATUS:  // 支付中间状态
        case MSGFUNC_ETC_SPECIAL: // ETC特情
        case MSGFUNC_CASH_RECEIPT: // 现金发票
            {
                QString logPrefix = QString::fromUtf8("MobilePayDual:消息类型=%1, 状态=%2 - ").arg(funcCode).arg(statusCode);
                
                if (data.size() > 0) {
                    // 转换为UTF-8字符串
                    QTextCodec *codec = QTextCodec::codecForName("UTF-8");
                    QString resultStr = codec->toUnicode(data);
                    
                    // 清理可能的空字符和空白字符
                    resultStr = resultStr.trimmed();
                    int nullPos = resultStr.indexOf(QChar('\0'));
                    if (nullPos != -1) {
                        resultStr = resultStr.left(nullPos);
                        DebugLog(QString::fromUtf8("MobilePayDual:清理其他消息类型中的空字符"));
                    }
                    
                    DebugLog(logPrefix + resultStr);
                    emit deviceStatusSignal(funcCode, statusCode, statusDesc);
                    
                    // 发送内部状态信号用于同步等待
                    emit internalStatusSignal(funcCode, statusCode, statusDesc);
                } else {
                    DebugLog(logPrefix + QString::fromUtf8("无数据"));
                    emit deviceStatusSignal(funcCode, statusCode, statusDesc);
                    
                    // 发送内部状态信号用于同步等待
                    emit internalStatusSignal(funcCode, statusCode, statusDesc);
                }
            }
            break;
            
        default:
            {
                QString logMsg = QString::fromUtf8("MobilePayDual:未知功能代码=%1, 状态代码=%2")
                    .arg(funcCode).arg(statusCode);
                DebugLog(logMsg);
                
                if (data.size() > 0) {
                    QTextCodec *codec = QTextCodec::codecForName("UTF-8");
                    QString resultStr = codec->toUnicode(data);
                    
                    // 清理可能的空字符和空白字符
                    resultStr = resultStr.trimmed();
                    int nullPos = resultStr.indexOf(QChar('\0'));
                    if (nullPos != -1) {
                        resultStr = resultStr.left(nullPos);
                        DebugLog(QString::fromUtf8("MobilePayDual:清理未知消息类型中的空字符"));
                    }
                    
                    DebugLog(logMsg + QString::fromUtf8(" - 数据: ") + resultStr);
                }
                
                emit deviceStatusSignal(funcCode, statusCode, statusDesc);
                
                // 发送内部状态信号用于同步等待
                emit internalStatusSignal(funcCode, statusCode, statusDesc);
            }
            break;
    }
}
/**
 * @brief 解析JSON结果
 * @param jsonResult JSON结果字符串
 * @param result 结果对象
 * @return 成功返回true，失败返回false
 */
bool MobilePayDual::parseResultJson(const QString& jsonResult, TW_RESULT& result)
{
    // 清理JSON字符串，去除可能的空字符和空白字符
    QString cleanedJson = jsonResult.trimmed();
    
    // 如果JSON字符串包含空字符，截取到第一个空字符
    int nullPos = cleanedJson.indexOf(QChar('\0'));
    if (nullPos != -1) {
        cleanedJson = cleanedJson.left(nullPos);
        DebugLog(QString::fromUtf8("MobilePayDual:检测到JSON中包含空字符，已清理，原长度=%1，清理后长度=%2")
                 .arg(jsonResult.length()).arg(cleanedJson.length()));
    }
    
    // 使用QxtJSON解析清理后的JSON字符串
    QVariant var = QxtJSON::parse(cleanedJson);
    if (!var.isValid() || !var.canConvert(QVariant::Map))
    {
        DebugLog(QString::fromUtf8("MobilePayDual:解析JSON结果失败，非JSON对象"));
        return false;
    }

    QVariantMap rootObj = var.toMap();
    
    // 检查是否包含结果上下文
    if (rootObj.contains("DebitResultContext")) {
        // 扣款结果
        QVariantMap resultContext = rootObj["DebitResultContext"].toMap();
        if (!resultContext.contains("Data")) {
            DebugLog(QString::fromUtf8("MobilePayDual:解析JSON结果失败，缺少Data字段"));
            return false;
        }
        
        QVariantMap data = resultContext["Data"].toMap();
        QVariantMap resultObj;
        
        // 根据包含的结果类型进行处理
        if (data.contains("Result")) {
            // 标准Result格式
            resultObj = data["Result"].toMap();
            
            // 解析响应码和消息
            result.code = resultObj["ResultCode"].toString();
            result.msg = resultObj["ResultMsg"].toString();
            
            // 解析其他字段
            if (resultObj.contains("PayPlatformType")) {
                result.channelType = resultObj["PayPlatformType"].toInt();
            }
            
            if (resultObj.contains("DebitOrderNumber")) {
                result.orderNo = resultObj["DebitOrderNumber"].toString();
            }
            
            if (resultObj.contains("PayIdentifier")) {
                result.PayIdentifier = resultObj["PayIdentifier"].toString();
            }
        } else if (data.contains("DebitResult")) {
            // DebitResult格式
            resultObj = data["DebitResult"].toMap();
            
            // 解析响应码和消息
            result.code = resultObj["ResultCode"].toString();
            result.msg = resultObj["ResultMsg"].toString();
            
            // 解析其他字段
            if (resultObj.contains("PayPlatformType")) {
                result.channelType = resultObj["PayPlatformType"].toInt();
            }
            
            if (resultObj.contains("DebitOrderNumber")) {
                result.orderNo = resultObj["DebitOrderNumber"].toString();
            }
            
            if (resultObj.contains("PayIdentifier")) {
                result.PayIdentifier = resultObj["PayIdentifier"].toString();
            }
        } else if (data.contains("TradeKeyItem")) {
            // TradeKeyItem格式 - 查询结果格式
            QVariantMap tradeKeyItem = data["TradeKeyItem"].toMap();
            
            // 从ExecuteCode判断结果状态
            int executeCode = data["ExecuteCode"].toInt();
            QString executeDesc = data["ExecuteDesc"].toString();
            
            if (executeCode == 0) {
                result.code = "10000";  // 成功
                result.msg = executeDesc.isEmpty() ? QString::fromUtf8("查询成功") : executeDesc;
            } else {
                result.code = QString::number(executeCode);
                result.msg = executeDesc.isEmpty() ? QString::fromUtf8("查询失败") : executeDesc;
            }
            
            // 解析TradeKeyItem中的字段
            if (tradeKeyItem.contains("PayPlatformType")) {
                result.channelType = tradeKeyItem["PayPlatformType"].toInt();
            }
            
            if (tradeKeyItem.contains("DebitOrder")) {
                result.orderNo = tradeKeyItem["DebitOrder"].toString();
            }
            
            if (tradeKeyItem.contains("PayIdentifier")) {
                result.PayIdentifier = tradeKeyItem["PayIdentifier"].toString();
            }
            
            DebugLog(QString::fromUtf8("MobilePayDual:解析TradeKeyItem格式JSON，执行码=%1，订单号=%2，支付标识=%3")
                     .arg(executeCode).arg(result.orderNo).arg(result.PayIdentifier));
        } else {
            DebugLog(QString::fromUtf8("MobilePayDual:解析JSON结果失败，缺少Result/DebitResult/TradeKeyItem字段"));
            return false;
        }
        
        // 响应码为0表示成功，转换为标准格式10000
        if (result.code == "0") {
            result.code = "10000";
        }
    } 
    else if (rootObj.contains("QueryResultContext")) {
        // 查询结果
        QVariantMap resultContext = rootObj["QueryResultContext"].toMap();
        QVariantMap data = resultContext["Data"].toMap();
        QVariantMap resultObj = data["Result"].toMap();
        
        // 解析响应码和消息
        result.code = resultObj["ResultCode"].toString();
        result.msg = resultObj["ResultMsg"].toString();
        
        // 解析其他字段
        if (resultObj.contains("PayPlatformType")) {
            result.channelType = resultObj["PayPlatformType"].toInt();
        }
        
        if (resultObj.contains("DebitOrderNumber")) {
            result.orderNo = resultObj["DebitOrderNumber"].toString();
        }
        
        if (resultObj.contains("PayIdentifier")) {
            result.PayIdentifier = resultObj["PayIdentifier"].toString();
        }
        
        // 响应码为0表示成功，转换为标准格式10000
        if (result.code == "0") {
            result.code = "10000";
        }
    }
    else if (rootObj.contains("CancelResultContext")) {
        // 撤单结果
        QVariantMap resultContext = rootObj["CancelResultContext"].toMap();
        QVariantMap data = resultContext["Data"].toMap();
        QVariantMap resultObj = data["Result"].toMap();
        
        // 解析响应码和消息
        result.code = resultObj["ResultCode"].toString();
        result.msg = resultObj["ResultMsg"].toString();
        
        // 解析其他字段
        if (resultObj.contains("PayPlatformType")) {
            result.channelType = resultObj["PayPlatformType"].toInt();
        }
        
        if (resultObj.contains("DebitOrderNumber")) {
            result.orderNo = resultObj["DebitOrderNumber"].toString();
        }
        
        if (resultObj.contains("PayIdentifier")) {
            result.PayIdentifier = resultObj["PayIdentifier"].toString();
        }
        
        // 响应码为0表示成功，转换为标准格式10000
        if (result.code == "0") {
            result.code = "10000";
        }
    }
    else {
        // 旧格式兼容处理
        result.code = rootObj["code"].toString();
        result.msg = rootObj["msg"].toString();
        result.sub_code = rootObj["sub_code"].toString();
        result.sub_msg = rootObj["sub_msg"].toString();
        result.timestamp = rootObj["timestamp"].toString();
        result.orderNo = rootObj["order_no"].toString();
        result.channelType = rootObj["channel_type"].toInt();
        result.PayIdentifier = rootObj["pay_identifier"].toString();
    }

    return true;
}

/**
 * @brief 执行支付（同步等待版本）
 * @param payRequest 支付请求参数
 * @param resp 支付结果
 * @return 成功返回true，失败返回false
 * @details 发送支付请求后，同步等待异步结果，直到收到支付结果或超时
 */
bool MobilePayDual::Pay(const MBPay_PayRequest& payRequest, TW_RESULT& resp)
{
    if (!m_bDevInited || !m_pFun_DebitMoney)
        return false;

    // 检查是否已经在等待其他结果
    if (m_bWaitingResult) {
        DebugLog(QString::fromUtf8("MobilePayDual:正在等待其他支付结果，无法发起新的支付"));
        resp.code = "-1";
        resp.msg = QString::fromUtf8("设备忙，请稍后重试");
        return false;
    }

    // 转换为JSON请求参数
    QByteArray jsonBytes = convertPayJson(payRequest);
    
    // 调用支付接口
    bool bRet = m_pFun_DebitMoney(jsonBytes.constData(), jsonBytes.size(), 2);
    if (!bRet)
    {
        DebugLog(QString::fromUtf8("MobilePayDual:支付请求失败"));
        resp.code = "-1";
        resp.msg = QString::fromUtf8("支付请求失败");
        return false;
    }

    DebugLog(QString::fromUtf8("MobilePayDual:支付请求已发送，开始同步等待结果"));
    
    // 准备同步等待
    m_bWaitingResult = true;
    m_currentPayIdentifier = payRequest.payIdentifier;
    m_asyncResult.code.clear(); // 重置结果
    
    // 创建事件循环用于同步等待
    QEventLoop eventLoop;
    m_pEventLoop = &eventLoop;
    
    // 启动超时定时器（60秒超时）
    m_pTimeoutTimer->start(60000);
    
    // 进入事件循环等待结果
    eventLoop.exec();
    
    // 清理
    m_pEventLoop = NULL;
    m_bWaitingResult = false;
    
    // 返回异步结果
    resp = m_asyncResult;
    
    DebugLog(QString::fromUtf8("MobilePayDual:同步等待结束，最终结果码=%1，消息=%2")
             .arg(resp.code).arg(resp.msg));
    
    return resp.bSuccess() || resp.bWaitInputPass();
}

/**
 * @brief 查询订单
 * @param payIdentifier 支付标识符
 * @param submitCount 提交次数
 * @param resp 查询结果
 * @return 成功返回true，失败返回false
 */
bool MobilePayDual::QueryOrder(QString payIdentifier, quint32 submitCount, TW_RESULT& resp)
{
    if (!m_bDevInited || !m_pFun_GetDebitResult)
        return false;

    // 转换为JSON请求参数
    QString jsonParam = convertQueryJson(payIdentifier, submitCount);
    QByteArray jsonBytes = jsonParam.toUtf8();

    // 准备数据缓冲区
    // 首先将请求参数复制到缓冲区
    char resultBuffer[4096] = {0};
    memcpy(resultBuffer, jsonBytes.constData(), jsonBytes.size());
    int resultLen = 4096;  // 初始化为缓冲区大小，函数返回后会更新为实际数据大小

    // 调用查询接口
    // 参数1：请求/结果缓冲区 - resultBuffer（输入查询请求参数，输出查询结果）
    // 参数2：缓冲区大小/结果大小 - resultLen（输入缓冲区大小，输出实际结果大小）
    // 参数3：格式类型 - 2（表示JSON格式）
    bool bGetResult = m_pFun_GetDebitResult(resultBuffer, resultLen, 2);
    if (!bGetResult)
    {
        DebugLog(QString::fromUtf8("MobilePayDual:查询订单失败"));
        resp.code = "-1";
        resp.msg = QString::fromUtf8("查询订单失败");
        return false;
    }

    // 解析JSON结果 - 处理多余的\000字符
    // 方法1：使用QString::fromUtf8()自动检测字符串结束位置（推荐）
    QString jsonResult = QString::fromUtf8(resultBuffer);
    
    // 方法2：手动查找实际数据长度（备选方案）
    // int actualLen = strlen(resultBuffer);
    // QString jsonResult = QString::fromUtf8(resultBuffer, actualLen);
    
    // 去除可能残留的空字符和空白字符
    jsonResult = jsonResult.trimmed();
    
    // 添加调试日志，显示清理后的JSON长度
    DebugLog(QString::fromUtf8("MobilePayDual:查询结果JSON长度=%1，内容=%2")
             .arg(jsonResult.length()).arg(jsonResult));
    
    if (!parseResultJson(jsonResult, resp))
    {
        return false;
    }

    return resp.bSuccess();
}

/**
 * @brief 撤单（同步等待版本）
 * @param payIdentifier 支付标识符
 * @param submitCount 提交次数
 * @param payResult 撤单结果
 * @return 成功返回true，失败返回false
 * @details 发送撤单请求后，同步等待异步结果，直到收到撤单结果或超时
 */
bool MobilePayDual::Reverse(QString payIdentifier, quint32 submitCount, TW_RESULT& payResult)
{
    if (!m_bDevInited || !m_pFun_DebitCancel)
        return false;

    // 检查是否已经在等待其他结果
    if (m_bWaitingResult) {
        DebugLog(QString::fromUtf8("MobilePayDual:正在等待其他操作结果，无法发起撤单"));
        payResult.code = "-1";
        payResult.msg = QString::fromUtf8("设备忙，请稍后重试");
        return false;
    }

    // 转换为JSON请求参数
    QString jsonParam = convertReverseJson(payIdentifier, submitCount);
    QByteArray jsonBytes = jsonParam.toUtf8();

    // 调用撤单接口
    // 参数1：参数内容 - jsonBytes.constData()
    // 参数2：参数大小 - jsonBytes.size()
    // 参数3：格式类型 - 2（表示JSON格式）
    bool bRet = m_pFun_DebitCancel(jsonBytes.constData(), jsonBytes.size(), 2);
    if (!bRet)
    {
        DebugLog(QString::fromUtf8("MobilePayDual:撤单请求失败"));
        payResult.code = "-1";
        payResult.msg = QString::fromUtf8("撤单请求失败");
        return false;
    }

    DebugLog(QString::fromUtf8("MobilePayDual:撤单请求已发送，开始同步等待结果"));
    
    // 准备同步等待
    m_bWaitingResult = true;
    m_currentPayIdentifier = payIdentifier;
    m_asyncResult.code.clear(); // 重置结果
    
    // 创建事件循环用于同步等待
    QEventLoop eventLoop;
    m_pEventLoop = &eventLoop;
    
    // 启动超时定时器（30秒超时，撤单通常比支付快）
    m_pTimeoutTimer->start(30000);
    
    // 进入事件循环等待结果
    eventLoop.exec();
    
    // 清理
    m_pEventLoop = NULL;
    m_bWaitingResult = false;
    
    // 返回异步结果
    payResult = m_asyncResult;
    
    DebugLog(QString::fromUtf8("MobilePayDual:撤单同步等待结束，最终结果码=%1，消息=%2")
             .arg(payResult.code).arg(payResult.msg));
    
    return payResult.bSuccess();
}

/**
 * @brief 处理异步支付结果（用于同步等待）
 * @param result 支付结果JSON字符串
 * @details 当接收到异步支付结果时，解析结果并退出事件循环
 */
void MobilePayDual::onAsyncPayResult(QString result)
{
    if (!m_bWaitingResult || !m_pEventLoop) {
        return; // 不在等待状态或事件循环不存在
    }
    
    // 停止超时定时器
    if (m_pTimeoutTimer->isActive()) {
        m_pTimeoutTimer->stop();
    }
    
    if (result.isEmpty()) {
        // 超时情况
        m_asyncResult.code = "-1";
        m_asyncResult.msg = QString::fromUtf8("支付超时");
        DebugLog(QString::fromUtf8("MobilePayDual:支付等待超时"));
    } else {
        // 解析异步结果
        if (!parseResultJson(result, m_asyncResult)) {
            m_asyncResult.code = "-1";
            m_asyncResult.msg = QString::fromUtf8("解析支付结果失败");
            DebugLog(QString::fromUtf8("MobilePayDual:解析异步支付结果失败"));
        } else {
            DebugLog(QString::fromUtf8("MobilePayDual:接收到异步支付结果，响应码=%1，订单号=%2，支付渠道=%3")
                     .arg(m_asyncResult.code).arg(m_asyncResult.orderNo).arg(m_asyncResult.channelType));
        }
    }
    
    // 退出事件循环
    m_bWaitingResult = false;
    if (m_pEventLoop) {
        m_pEventLoop->quit();
    }
}

/**
 * @brief 处理设备状态消息（用于同步等待）
 * @param funcCode 功能代码
 * @param statusCode 状态代码 
 * @param message 状态描述
 * @details 当接收到特定的设备状态时，可能需要结束同步等待
 */
void MobilePayDual::onAsyncDeviceStatus(int funcCode, int statusCode, QString message)
{
    if (!m_bWaitingResult || !m_pEventLoop) {
        return; // 不在等待状态或事件循环不存在
    }
    
    DebugLog(QString::fromUtf8("MobilePayDual:同步等待期间收到状态消息，功能码=%1, 状态码=%2, 消息=%3")
             .arg(funcCode).arg(statusCode).arg(message));
    
    // 根据功能代码和状态代码决定是否结束等待
    bool shouldEndWait = false;
    
    switch(funcCode) {
        case MSGFUNC_DEBIT:  // 扣款回馈
            {
                // 对于某些明确的错误状态，直接结束等待
                switch(statusCode) {
                    case DEBIT_ERR_NO_MONEY:      // 余额不足
                    case DEBIT_ERR_INVALID_QR:    // 二维码非法
                    case DEBIT_ERR_EXPIRED_QR:    // 二维码有效期非法
                    case DEBIT_ERR_NO_USER:       // 未获取有效用户信息
                    case DEBIT_ERR_TIMEOUT:       // 扣款超时
                    case DEBIT_ERR_WRONG_PWD:     // 密码输入错误
                    case DEBIT_ERR_PARAM:         // 参数异常
                    case DEBIT_ERR_LAST_TRADE:    // 上次交易未完成
                    case DEBIT_ERR_NO_INIT:       // 组件未初始化
                    case DEBIT_ERR_NO_AUTH:       // 组件未经授权
                    case DEBIT_ERR_NETWORK:       // 支付网络故障
                    case DEBIT_ERR_INTERNAL:      // 内部错误
                        shouldEndWait = true;
                        DebugLog(QString::fromUtf8("MobilePayDual:收到扣款错误状态，结束同步等待，状态码=%1").arg(statusCode));
                        break;
                    
                    case DEBIT_SUCCESS:           // 扣费成功 - 主动查询订单信息
                        {
                            shouldEndWait = true;
                            DebugLog(QString::fromUtf8("MobilePayDual:收到扣款成功状态，准备查询订单信息，状态码=%1").arg(statusCode));
                            
                            // 主动查询订单信息获取详细结果
                            TW_RESULT queryResult;
                            if (QueryOrder(m_currentPayIdentifier, 1, queryResult)) {
                                // 查询成功，使用查询结果
                                m_asyncResult = queryResult;
                                DebugLog(QString::fromUtf8("MobilePayDual:查询订单成功，响应码=%1，订单号=%2，支付渠道=%3")
                                         .arg(m_asyncResult.code).arg(m_asyncResult.orderNo).arg(m_asyncResult.channelType));
                            } else {
                                // 查询失败，使用基本成功状态
                                m_asyncResult.code = "10000";
                                m_asyncResult.msg = message;
                                DebugLog(QString::fromUtf8("MobilePayDual:查询订单失败，使用基本成功状态"));
                            }
                        }
                        break;
                    case DEBIT_INPUTTING_PWD:     // 用户正在输入密码 - 继续等待
                    case DEBIT_GOT_CERTIFICATE:  // 已获取支付凭证 - 继续等待最终结果
                        // 这些状态继续等待，不结束
                        DebugLog(QString::fromUtf8("MobilePayDual:收到扣款中间状态，继续等待，状态码=%1").arg(statusCode));
                        break;
                    
                    default:
                        // 未知状态码，为安全起见结束等待
                        shouldEndWait = true;
                        DebugLog(QString::fromUtf8("MobilePayDual:收到未知扣款状态码，结束同步等待，状态码=%1").arg(statusCode));
                        break;
                }
            }
            break;
            
        case MSGFUNC_CANCEL:  // 撤单回馈
            {
                // 撤单消息通常都应该结束等待（成功或失败都是最终结果）
                shouldEndWait = true;
                if (statusCode == 0) {
                    // 撤单成功
                    m_asyncResult.code = "10000";
                    m_asyncResult.msg = message;
                    DebugLog(QString::fromUtf8("MobilePayDual:收到撤单成功状态消息，结束同步等待，状态码=%1").arg(statusCode));
                } else {
                    // 撤单失败 - 立即结束等待
                    shouldEndWait = true;
                    DebugLog(QString::fromUtf8("MobilePayDual:收到撤单失败状态消息，结束同步等待，状态码=%1").arg(statusCode));
                }
            }
            break;
            
        case MSGFUNC_PAY_STATUS:  // 支付中间状态
            {
                // 支付中间状态通常不结束等待，继续等待最终结果
                DebugLog(QString::fromUtf8("MobilePayDual:收到支付中间状态，继续等待，状态码=%1").arg(statusCode));
            }
            break;
            
        default:
            // 其他类型的消息通常不影响支付/撤单的等待
            DebugLog(QString::fromUtf8("MobilePayDual:收到其他类型状态消息，继续等待，功能码=%1").arg(funcCode));
            break;
    }
    
    // 如果需要结束等待
    if (shouldEndWait) {
        // 停止超时定时器
        if (m_pTimeoutTimer->isActive()) {
            m_pTimeoutTimer->stop();
        }
        
        // 对于失败情况或未设置结果的情况，设置基本结果
        if (m_asyncResult.code.isEmpty()) {
            m_asyncResult.code = QString::number(statusCode);
            m_asyncResult.msg = message;
        }
        
        DebugLog(QString::fromUtf8("MobilePayDual:设备状态触发同步等待结束，最终结果码=%1，消息=%2")
                 .arg(m_asyncResult.code).arg(m_asyncResult.msg));
        
        // 退出事件循环
        m_bWaitingResult = false;
        if (m_pEventLoop) {
            m_pEventLoop->quit();
        }
    }
}
