#include "uiserver.h"

#include <QHostAddress>
#include <QTextCodec>
#include <QtEndian>

#include "ilogmsg.h"
#include "parammgr.h"

UIServer::UIServer()
{
    m_pServer = new QTcpServer();
    connect(m_pServer, SIGNAL(newConnection()), this, SLOT(OnNewConnection()));
}

UIServer::~UIServer()
{
    StopServer();
    if (m_pServer) {
        delete m_pServer;
    }
}

void UIServer::OnNewConnection()
{
    while (m_pServer->hasPendingConnections()) {
        QTcpSocket* pSocket = m_pServer->nextPendingConnection();
        if (!pSocket) {
            continue;
        }
        QHostAddress hostAdr = pSocket->peerAddress();
        DebugLog(QString("UIServer::new Connection,IP:%1").arg(hostAdr.toString()));

        connect(pSocket, SIGNAL(readyRead()), this, SLOT(OnClientReadReady()));
        connect(pSocket, SIGNAL(disconnected()), this, SLOT(OnClientDisconnect()));
        emit NotifyNewConnection(hostAdr.toString());

        m_lstClients.push_back(pSocket);
        if (pSocket->bytesAvailable()) {
            ProcessData(pSocket);
        }
    }
}

void UIServer::OnClientReadReady()
{
   // DebugLog(QString("On socket ReadyRead"));
    QTcpSocket* pSocket = qobject_cast<QTcpSocket*>(sender());
    ProcessData(pSocket);
}

void UIServer::OnClientDisconnect()
{
    QTcpSocket* pSocket = qobject_cast<QTcpSocket*>(sender());
    QHostAddress hostAdr = pSocket->peerAddress();
    DebugLog(QString("CRemoteServer %1 disconnect").arg(hostAdr.toString()));
    disconnect(pSocket, SIGNAL(readyRead()), this, SLOT(OnClientReadReady()));
    pSocket->deleteLater();
    emit NotifyDisConnected(hostAdr.toString());
    m_lstClients.removeOne(pSocket);
}

void UIServer::ProcessData(QTcpSocket* pSocket)
{
    int nHeadLen = sizeof(struct UIM_MsgHead);
    QByteArray msgData = pSocket->readAll();
    if (msgData.length() <= nHeadLen) {
        return;
    }
    //分析报文头
    UIM_MsgHead* pHead = (struct UIM_MsgHead*)msgData.data();
    if (pHead->msgFlag != 0x8E) {
        ErrorLog(QString("报文头标识(%1)出错,应为0x8E").arg(pHead->msgFlag, 2, 16));
        return;
    }
    pHead->msgLen = qFromBigEndian(pHead->msgLen);
    //判断报文总长度
    if (pHead->msgLen > MAX_UIMSG_LENGTH || pHead->msgLen + 5 < msgData.length()) {
        ErrorLog(QString("实际报文长度(%1)出错, 接收总长度(%2)，最大长度(%3)")
                     .arg(pHead->msgLen)
                     .arg(msgData.length())
                     .arg(MAX_UIMSG_LENGTH));
        return;
    }
    // TODO 判断CRC
    //根据报文类型
    int bRet = -1;
    switch (pHead->msgType) {
        case UMSG_TYPE_LANEINFO:
            bRet = handleMsgLaneInfo(pSocket, pHead, msgData);
            break;
        case UMSG_TYPE_HELP:
            bRet = handleMsgHelp(pSocket, pHead, msgData);
            break;
        case UMSG_TYPE_TOLLINFO:
            bRet = handleMsgTollInfo(pSocket, pHead, msgData);
            break;
        case UMSG_TYPE_TICKET:
            bRet = handleMsgTicket(pSocket, pHead, msgData);
            break;
        default:
            ErrorLog(QString("非法报文类型:%1").arg(pHead->msgType, 2, 16));
            break;
    }
    if (bRet == -1) {
        //需要断链
        ErrorLog("报文处理错误，需要断链");
        pSocket->disconnectFromHost();
    }
}

//处理车道显示
int UIServer::handleMsgLaneInfo(QTcpSocket* pSocket, const struct UIM_MsgHead* pMsgHead,
                                QByteArray msg)
{
    char* pBegin = msg.data() + sizeof(struct UIM_MsgHead);
    QString staName, laneName;
    quint16 staNameLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (staNameLen > 0) {
        staName = QString::fromLocal8Bit(pBegin, staNameLen);
        pBegin += staNameLen;
    }
    quint16 laneNameLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (laneNameLen > 0) {
        laneName = QString::fromLocal8Bit(pBegin, laneNameLen);
        pBegin += laneNameLen;
    }
    DebugLog(QString("收到车道信息报文：Station=%1, Lane=%2").arg(staName).arg(laneName));
    //发送信号
    emit NotifyLaneInfo(staName, laneName);
    return 0;
}

int UIServer::handleMsgHelp(QTcpSocket* pSocket, const struct UIM_MsgHead* pMsgHead, QByteArray msg)
{
    char* pBegin = msg.data() + sizeof(struct UIM_MsgHead);

    QString staName, laneName, helpMsg;
    quint16 staNameLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (staNameLen > 0) {
        staName = QString::fromLocal8Bit(pBegin, staNameLen);
        pBegin += staNameLen;
    }
    quint16 laneNameLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (laneNameLen > 0) {
        laneName = QString::fromLocal8Bit(pBegin, laneNameLen);
        pBegin += laneNameLen;
    }
    quint16 helpType = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    quint16 helpMsgLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (helpMsgLen > 0) {
        helpMsg = QString::fromLocal8Bit(pBegin, helpMsgLen);
        pBegin += helpMsgLen;
    }

    DebugLog(QString("收到车道信息报文：Station=%1, Lane=%2").arg(staName).arg(laneName));
    DebugLog(QString("收到帮助报文：helpType=%1, helpMsg='%2'").arg(helpType).arg(helpMsg));
    //发送信号
    emit NotifyHelpMsg(staName, laneName, helpType, helpMsg);
    return 0;
}

//处理发票信息
int UIServer::handleMsgTicket(QTcpSocket* pSocket, const struct UIM_MsgHead* pMsgHead,
                              QByteArray msg)
{
    char* pBegin = msg.data() + sizeof(struct UIM_MsgHead);
    QString vlp, vehClassName, vehTypeName, enStaName, qrCode;
    quint16 tempLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (tempLen > 0) {
        vlp = QString::fromLocal8Bit(pBegin, tempLen);
        pBegin += tempLen;
    }
    tempLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (tempLen > 0) {
        vehClassName = QString::fromLocal8Bit(pBegin, tempLen);
        pBegin += tempLen;
    }
    tempLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (tempLen > 0) {
        vehTypeName = QString::fromLocal8Bit(pBegin, tempLen);
        pBegin += tempLen;
    }
    tempLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (tempLen > 0) {
        enStaName = QString::fromLocal8Bit(pBegin, tempLen);
        pBegin += tempLen;
    }
    quint32 tollMoney = qFromBigEndian<quint32>((const uchar*)pBegin);
    pBegin += sizeof(quint32);
    quint8 channelType = *pBegin;
    pBegin += 1;
    tempLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (tempLen > 0) {
        qrCode = QString::fromLocal8Bit(pBegin, tempLen);
        pBegin += tempLen;
    }
    DebugLog(QString("收到发票信息报文：%1,%2, %3,%4,%5")
                 .arg(vlp)
                 .arg(vehClassName)
                 .arg(vehTypeName)
                 .arg(enStaName)
                 .arg(tollMoney));

    //发送信号
    emit NotifyTicketInfo(vlp, vehClassName, vehTypeName, enStaName, tollMoney, channelType,
                          qrCode);
    return 0;
}

int UIServer::handleMsgTollInfo(QTcpSocket* pSocket, const struct UIM_MsgHead* pMsgHead,
                                QByteArray msg)
{
    char* pBegin = msg.data() + sizeof(struct UIM_MsgHead);
    QString vlp, vehClassName, vehTypeName, enStaName;
    quint16 tempLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (tempLen > 0) {
        vlp = QString::fromLocal8Bit(pBegin, tempLen);
        pBegin += tempLen;
    }
    tempLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (tempLen > 0) {
        vehClassName = QString::fromLocal8Bit(pBegin, tempLen);
        pBegin += tempLen;
    }
    tempLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (tempLen > 0) {
        vehTypeName = QString::fromLocal8Bit(pBegin, tempLen);
        pBegin += tempLen;
    }
    tempLen = qFromBigEndian<quint16>((const uchar*)pBegin);
    pBegin += sizeof(quint16);
    if (tempLen > 0) {
        enStaName = QString::fromLocal8Bit(pBegin, tempLen);
        pBegin += tempLen;
    }
    quint32 tollMoney = qFromBigEndian<quint32>((const uchar*)pBegin);
    pBegin += sizeof(quint32);
    DebugLog(QString("收到收费信息报文：%1,%2, %3,%4,%5")
                 .arg(vlp)
                 .arg(vehClassName)
                 .arg(vehTypeName)
                 .arg(enStaName)
                 .arg(tollMoney));
    quint8 beforePay = *pBegin;
    pBegin += 1;
    quint8 channelType = *pBegin;
    //发送信号
    emit NotifyTollInfo(vlp, vehClassName, vehTypeName, enStaName, tollMoney, beforePay == 1,
                        channelType);
    return 0;
}

bool UIServer::StartServer()
{
    if (!m_pServer->listen(QHostAddress::Any, g_ParamMgr.m_UIServerPort)) {
        ErrorLog(QString("监听端口%1失败, %2")
                     .arg(g_ParamMgr.m_UIServerPort)
                     .arg(m_pServer->errorString()));
        return false;
    }

    return true;
}

bool UIServer::StopServer()
{
    if (m_pServer->isListening()) {
        m_pServer->close();
    }
    for (int i = 0; i < m_lstClients.size(); i++) {
        QTcpSocket* pSkt = m_lstClients.at(i);
        pSkt->disconnectFromHost();
        if (pSkt->state() != QAbstractSocket::UnconnectedState) {
            pSkt->abort();
        }
    }

    return true;
}
