#include "stdlog.h"

#include "devicefactory.h"
#include "etclanectrl.h"

CStdLog::CStdLog(QObject *parent) : QObject(parent) {}

void CStdLog::StdLogAppStart(bool bStart)
{
    QString sKeyFlag = QTime::currentTime().toString("hhmmsszzz");
    QString sLog = bStart ? QString("启动") : QString("退出");
    StdInfoLog(LogKey::OneKey_Other, LogKey::Other_ProgramStatus, sKeyFlag, QString(), sLog);
    return;
}

void CStdLog::StdLogLoginInfo(bool bLogin, const QString &sOperatorName, const QString &sShiftName)
{
    QString sKeyFlag = QTime::currentTime().toString("hhmmsszzz");

    CShiftMgr *pMgr = Ptr_ETCCtrl->GetShiftMgr();
    if (bLogin) {
        QString sLog = pMgr->m_tmLoginTime.toString(
            "yyyy-MM-dd hh:mm:ss");  // QString("上班,收费员:%1,班次:%2").arg(sOperatorName).arg(sShiftName);
        StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_Login, sKeyFlag, QString(), sLog);
        StdInfoLog(LogKey::OneKey_Other, LogKey::Other_SwitchOperate, sKeyFlag, QString(),
                   QString("已上班"));
    } else {
        //        sLog = QString("下班,收费员:%1").arg(sOperatorName);
        QString sLog = pMgr->m_tmLoginTime.toString(
            "yyyy-MM-dd hh:mm:ss");  // QString("上班,收费员:%1,班次:%2").arg(sOperatorName).arg(sShiftName);
        StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_Login, sKeyFlag, QString(), sLog);
        sLog = pMgr->m_tmUnLoginTime.toString("yyyy-MM-dd hh:mm:ss");
        StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_Logout, sKeyFlag, QString(), sLog);
        StdInfoLog(LogKey::OneKey_Other, LogKey::Other_SwitchOperate, sKeyFlag, QString(),
                   QString("下班成功"));
    }
    return;
}

void CStdLog::StdLogDevInfo_ProductAndVersion()
{
    QString str = QDateTime::currentDateTime().toString("hhmmsszzz");
    for (int i = 0; i < MAX_VPR_NUM; ++i) {
        CVPRDev *pVpr = CDeviceFactory::GetVPRDev(i);
        if (!pVpr) continue;
        StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_RegPlateProduct, str, pVpr->GetDevId(),
                   QString("1"));
        StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_RegPlateVersion, str, pVpr->GetDevId(),
                   QString(""));
    }
    StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_IOProduct, str,
               CDeviceFactory::GetIOCard()->GetDevId(), QString("2"));
    StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_IOVersion, str,
               CDeviceFactory::GetIOCard()->GetDevId(), QString(""));

    for (int i = 0; i < MAX_FAREDISPLAYER_NUM; ++i) {
        CFareDisplayer_GB *pFD = CDeviceFactory::GetETCFareDisPlayer(i);
        if (!pFD) continue;
        StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_FeeboardProduct, str, pFD->GetDevId(),
                   QString("3"));
        StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_FeeboardVersion, str, pFD->GetDevId(),
                   QString(""));
    }

    StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_WeightProduct, str, QString("weight"),
               QString("1"));
    StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_WeightVersion, str, QString("weight"),
               QString(""));
    return;
}

void CStdLog::StdLogDevInfo_DevStatus(qint32 nDevId, qint32 nStatus)
{
    QString sKeyFlag = QDateTime::currentDateTime().toString("hhmmsszzz");
    QString sStatus = 0 == nStatus ? QString("1") : QString("0");
    switch (nDevId) {
        case DEV_VPR: {
            CVPRDev *pVpr = CDeviceFactory::GetVPRDev(DevIndex_Second);
            if (pVpr) {
                StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_RegPlateStatus, sKeyFlag,
                           pVpr->GetDevId(), sStatus);
            }
            break;
        }
        case DEV_RSU: {
            CRsuDev *pRsuDev = CDeviceFactory::GetRsuDev(0);
            if (!pRsuDev) return;
            StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_RsuStatus, sKeyFlag,
                       pRsuDev->GetDevId(), sStatus);
            break;
        }
        case DEV_RSU1: {
            CRsuDev *pRsuDev = CDeviceFactory::GetRsuDev(1);
            if (!pRsuDev) return;
            StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_RsuStatus, sKeyFlag,
                       pRsuDev->GetDevId(), sStatus);
            break;
        }

        case DEV_NETWORK: {
            StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_NetWorkStatus, sKeyFlag,
                       QString("NetWork"), sStatus);
            break;
        }
        case DEV_CardReader:
        case DEV_CardReader1:
        case DEV_CardReader2: {
            StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_ICReaderStatus, sKeyFlag,
                       QString("Reader%1").arg(nDevId), sStatus);
            break;
        }
        case DEV_Weight: {
            StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_WeightStatus, sKeyFlag,
                       QString("weight"), sStatus);
            break;
        }
        default:
            break;
    }
}

void CStdLog::StdLogDevInfo_LoopStatus()
{
    QString sKeyFlag = QDateTime::currentDateTime().toString("hhmmsszzz");
    for (int i = 1; i <= 6; ++i) {
        QString sLoopId = QString("Loop%1").arg(i, 2, 10, QLatin1Char('0'));
        StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_LoopStatus, sKeyFlag, sLoopId,
                   QString("1"));
    }
}

void CStdLog::StdLogDevInfo_Loop(int nDI_LoopId, quint8 bStatus)
{
    QString sKeyFlag = QDateTime::currentDateTime().toString("hhmmsszzz");
    quint32 nId = LogKey::DevInfo_Loop1;
    switch (nDI_LoopId) {
        case DI_LoopTrig1:
            nId = LogKey::DevInfo_Loop1;
            break;
        case DI_LoopTrig2:
            nId = LogKey::DevInfo_Loop2;
            break;
        case DI_LoopTrig3:
            nId = LogKey::DevInfo_Loop3;
            break;
        case DI_LoopDetect:
            nId = LogKey::DevInfo_Loop4;
            break;
        case DI_LoopFront:
            nId = LogKey::DevInfo_Loop5;
            break;
        case DI_LoopBack:
            nId = LogKey::DevInfo_Loop6;
            break;
        default:
            return;
            break;
    }
    StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_Loop1, sKeyFlag, sKeyFlag,
               QString::number(bStatus));
    return;
}

void CStdLog::StdLogOBUInfo_BaseInfo(quint32 OBUId, const COBUBaseInfo *pOBUBaseInfo)
{
    if (!pOBUBaseInfo) return;

    QString str = QTime::currentTime().toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_OBUInfo, LogKey::OBUInfo_OBUMAC, str,
               QString::fromAscii(pOBUBaseInfo->szTime),
               QString("%1").arg(OBUId, 8, 16, QLatin1Char('0')));
    StdInfoLog(LogKey::OneKey_OBUInfo, LogKey::OBUInfo_OBUID, str,
               QString::fromAscii(pOBUBaseInfo->szTime),
               Raw2HexStr((quint8 *)pOBUBaseInfo->ContractProvider, 8));
    StdInfoLog(LogKey::OneKey_OBUInfo, LogKey::OBUInfo_OBUVender, str,
               QString::fromAscii(pOBUBaseInfo->szTime),
               QString("%1").arg(*((char *)&OBUId + 3), 2, 16, QLatin1Char('0')));
    StdInfoLog(LogKey::OneKey_OBUInfo, LogKey::OBUInfo_OBUNum, str,
               QString::fromAscii(pOBUBaseInfo->szTime),
               QString::fromLocal8Bit(pOBUBaseInfo->szContractSerialNumber));
    StdInfoLog(LogKey::OneKey_OBUInfo, LogKey::OBUInfo_OBUStartTime, str,
               QString::fromAscii(pOBUBaseInfo->szTime),
               QString::fromLocal8Bit(pOBUBaseInfo->szContractSignedDate));
    StdInfoLog(LogKey::OneKey_OBUInfo, LogKey::OBUInfo_OBUEndTime, str,
               QString::fromAscii(pOBUBaseInfo->szTime),
               QString::fromLocal8Bit(pOBUBaseInfo->szContractExpiredDate));
    StdInfoLog(LogKey::OneKey_OBUInfo, LogKey::OBUInfo_OBUVersion, str,
               QString::fromAscii(pOBUBaseInfo->szTime),
               QString("%1").arg(pOBUBaseInfo->EquitmentStatus, 2, 16, QLatin1Char('0')));
    StdInfoLog(LogKey::OneKey_OBUInfo, LogKey::OBUInfo_OBUState, str,
               QString::fromAscii(pOBUBaseInfo->szTime),
               QString("%1").arg(pOBUBaseInfo->bOBUState, 2, 16, QLatin1Char('0')));
    return;
}

void CStdLog::StdLogVehInfo_OBUVehInfo(const QString &sSubKey, const COBUVehInfo *pOBUVehInfo)
{
    if (!pOBUVehInfo) return;
    QString str = QTime::currentTime().toString("hhmmsszzz");

    StdInfoLog(LogKey::OneKey_VehicleInfo, LogKey::VehicleInfo_OBUPlate, str, sSubKey,
               QString::fromLocal8Bit(pOBUVehInfo->szVehPlate));
    StdInfoLog(LogKey::OneKey_VehicleInfo, LogKey::VehicleInfo_OBUPlateColor, str, sSubKey,
               QString("%1").arg(pOBUVehInfo->nPlateColor));
    StdInfoLog(LogKey::OneKey_VehicleInfo, LogKey::VehicleInfo_OBUVehClass, str, sSubKey,
               QString("%1").arg(pOBUVehInfo->bVehClass));
    StdInfoLog(LogKey::OneKey_VehicleInfo, LogKey::VehicleInfo_OBUUserType, str, sSubKey,
               QString("%1").arg(pOBUVehInfo->bUserType));
    return;
}

void CStdLog::StdLogTollCardInfo_CardTollInfo(const QString &sSubKey,
                                              const CCardTollInfo *pCardTollInfo)
{
    if (!pCardTollInfo) return;
    QTime time = QTime::currentTime();
    QString str = time.toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnNetID, str, sSubKey,
               pCardTollInfo->sNetworkHex);
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnStationID, str, sSubKey,
               pCardTollInfo->sEnStationHex);
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnLaneID, str, sSubKey,
               QString::number(pCardTollInfo->bLaneId));
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnTime, str, sSubKey,
               pCardTollInfo->szPassTime.toString("yyyy-MM-dd HH:mm:ss"));
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnVehicleClass, str, sSubKey,
               QString::number(pCardTollInfo->bVehClass));
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnPassStatus, str, sSubKey,
               QString::number(pCardTollInfo->bPassStatus));
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnPlate, str, sSubKey,
               QString::fromLocal8Bit(pCardTollInfo->szVehPlate));
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnPlateColor, str, sSubKey,
               QString::number(pCardTollInfo->bVehPlateColor));
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnAxleCount, str, sSubKey,
               QString::number(pCardTollInfo->VehicalAxles));
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnWeight, str, sSubKey,
               QString::number(pCardTollInfo->dwToltalWeight));
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnVehStatusFlag, str, sSubKey,
               QString::number(pCardTollInfo->bVehState));
    StdInfoLog(LogKey::OneKey_TollCardInfo, LogKey::TollCardInfo_EnVehClass, str, sSubKey,
               QString::number(pCardTollInfo->bVehTollType));
    return;
}

void CStdLog::StdLogIccInfo_ProCardBasicInfo(const QString &sSubKey,
                                             const CProCardBasicInfo *pProCardBasicInfo,
                                             quint32 dwBalance)
{
    if (!pProCardBasicInfo) return;
    QTime time = QTime::currentTime();
    QString str = time.toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUIssuer, str, sSubKey,
               QString(GB2312toUnicode(pProCardBasicInfo->IssueOrgId, 4)));
    StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUCardType, str, sSubKey,
               QString("%1").arg(pProCardBasicInfo->bType));
    StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUVersion, str, sSubKey,
               QString("%1").arg(pProCardBasicInfo->bVersion));
    StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUNetID, str, sSubKey,
               QString::number(pProCardBasicInfo->wNetWorkId));
    StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUCardSnNo, str, sSubKey,
               QString::fromLocal8Bit(pProCardBasicInfo->szCardNo));
    StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUCardID, str, sSubKey,
               QString::fromLocal8Bit(pProCardBasicInfo->szCardNo));
    QDateTime useTime;
    if (ConvertChar14ToDateTime(useTime, pProCardBasicInfo->szStartTime)) {
        StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUStartTime, str, sSubKey,
                   QString(useTime.toString("yyyy-MM-dd")));
    }
    if (ConvertChar14ToDateTime(useTime, pProCardBasicInfo->szExpireTime)) {
        StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUEndTime, str, sSubKey,
                   QString(useTime.toString("yyyy-MM-dd")));
    }
    StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUCardPlate, str, sSubKey,
               GB2312toUnicode(pProCardBasicInfo->szVehPlate));
    StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUCardUserType, str, sSubKey,
               QString::number(pProCardBasicInfo->bUserType));
    StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUCardPlateColor, str, sSubKey,
               QString::number(pProCardBasicInfo->bVehPlateColor));
    StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUCardVehClass, str, sSubKey,
               QString::number(pProCardBasicInfo->bVehClass));
    StdInfoLog(LogKey::OneKey_IccInfo, LogKey::IccInfo_CPUBalanceBefore, str, sSubKey,
               QString::number(dwBalance));
}

void CStdLog::StdLogConsumeInfo(const QString &sSubKey, quint32 dwLastMoney, quint32 dwConsumeMoney,
                                quint32 dwFeeMileage, quint32 vehClass, QString sCardNo)
{
    QString str = QTime::currentTime().toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_ConsumeInfo, LogKey::ConsumeInfo_TollMoney, str, sSubKey,
               QString::number(dwLastMoney));
    StdInfoLog(LogKey::OneKey_ConsumeInfo, LogKey::ConsumeInfo_ETCMoney, str, sSubKey,
               QString::number(dwConsumeMoney));
    StdInfoLog(LogKey::OneKey_ConsumeInfo, LogKey::ConsumeInfo_TollDistance, str, sSubKey,
               QString::number(dwFeeMileage));
    StdInfoLog(LogKey::OneKey_ConsumeInfo, LogKey::ConsumeInfo_FeeType, str, sSubKey,
               QString::number(vehClass));
    StdInfoLog(LogKey::OneKey_ConsumeInfo, LogKey::ConsumeInfo_PayType, str, sSubKey, QString("4"));
    StdInfoLog(LogKey::OneKey_ConsumeInfo, LogKey::ConsumeInfo_Special, str, sSubKey, QString(""));
    StdInfoLog(LogKey::OneKey_ConsumeInfo, LogKey::ConsumeInfo_PayCarInfo, str, sSubKey, sCardNo);
    StdInfoLog(LogKey::OneKey_ConsumeInfo, LogKey::ConsumeInfo_MediaType, str, sSubKey,
               QString("1"));
    if (dwLastMoney > 0) {
        StdInfoLog(
            LogKey::OneKey_ConsumeInfo, LogKey::ConsumeInfo_PayRebate, str, sSubKey,
            QString("%1‰").arg(dwConsumeMoney * 1000.0 / dwLastMoney, 0, 'f', 2, QLatin1Char('0')));
    } else {
        StdInfoLog(LogKey::OneKey_ConsumeInfo, LogKey::ConsumeInfo_PayRebate, str, sSubKey,
                   QString("1000‰"));
    }
    return;
}

void CStdLog::StdLog_EF04(const QString &sSubKey, const CEF04Info *pEF04Info)
{
    if (!pEF04Info) return;
    QTime time = QTime::currentTime();
    QString str = time.toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_File0019, str, sSubKey,
               Raw2HexStr((quint8 *)&pEF04Info->raw0019, sizeof(pEF04Info->raw0019)));
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_File0015, str, sSubKey,
               Raw2HexStr((quint8 *)&pEF04Info->raw0015, sizeof(pEF04Info->raw0015)));
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_ProvCnt, str, sSubKey,
               QString::number(pEF04Info->bProvinceCount));
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_Payfee, str, sSubKey,
               QString::number(pEF04Info->totalFee));
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_Realfee, str, sSubKey,
               QString::number(pEF04Info->totalLastFee));
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_TransSucCnt, str, sSubKey,
               QString::number(pEF04Info->totalTransOkTimes));
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_Mileage, str, sSubKey,
               QString::number(pEF04Info->totalTollMiles));
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_NoCardCnt, str, sSubKey,
               QString::number(pEF04Info->bTotalNoCardTimes));
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_CurProvStartPoint, str, sSubKey,
               pEF04Info->sLocalEntryIdHex);
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_CurProvPayfee, str, sSubKey,
               QString::number(pEF04Info->localTotalFee));
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_CurProvfee, str, sSubKey,
               QString::number(pEF04Info->localLastFee));
    StdInfoLog(LogKey::OneKey_OBUEF04Info, LogKey::OBUEF04Info_CurProvTransSucCnt, str, sSubKey,
               QString::number(pEF04Info->bLocalTransOkTimes));
    return;
}

void CStdLog::StdLog_CPCBaseInfo(const QString &sSubKey, const CCPCBasicInfo &cpcBasicInfo)
{
    QTime time = QTime::currentTime();
    QString str = time.toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_CPCBaseInfo, LogKey::CPCBaseInfo_CPCVender, str, sSubKey,
               GB2312toUnicode(cpcBasicInfo.sNetCode, 4));
    StdInfoLog(LogKey::OneKey_CPCBaseInfo, LogKey::CPCBaseInfo_CPCID, str, sSubKey,
               QString::fromAscii(cpcBasicInfo.sCardID));

    QString sStartTime = QString::fromAscii(cpcBasicInfo.sStartTime, 8);

    QString sTime = QString("%1-%2-%3")
                        .arg(sStartTime.left(4).arg(sStartTime.mid(4, 2).arg(sStartTime.right(2))));
    StdInfoLog(LogKey::OneKey_CPCBaseInfo, LogKey::CPCBaseInfo_CPCStartTime, str, sSubKey, sTime);
    QString sEndTime = QString::fromAscii(cpcBasicInfo.sEndTime, 8);
    sTime =
        QString("%1-%2-%3").arg(sEndTime.left(4).arg(sEndTime.mid(4, 2).arg(sEndTime.right(2))));

    StdInfoLog(LogKey::OneKey_CPCBaseInfo, LogKey::CPCBaseInfo_CPCEndTime, str, sSubKey, sTime);
    StdInfoLog(LogKey::OneKey_CPCBaseInfo, LogKey::CPCBaseInfo_CPCVersion, str, sSubKey,
               QString::number(cpcBasicInfo.nVersion));
    return;
}

void CStdLog::StdLog_CPCStationInfo(const QString &sSubKey, const CCPCRoadInfo_New &cpcRoadInfo)
{
    QTime time = QTime::currentTime();
    QString str = time.toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_CPCStationInfo, LogKey::CPCStationInfo_ProvCnt, str, sSubKey,
               QString::number(cpcRoadInfo.nProvCnt_After));
    StdInfoLog(LogKey::OneKey_CPCStationInfo, LogKey::CPCStationInfo_ProvGantryCnt, str, sSubKey,
               QString::number(cpcRoadInfo.bPassFlagCnt_After));
    StdInfoLog(LogKey::OneKey_CPCStationInfo, LogKey::CPCStationInfo_ProvFee, str, sSubKey,
               QString::number(cpcRoadInfo.nProvTollMoney_After));
    StdInfoLog(LogKey::OneKey_CPCStationInfo, LogKey::CPCStationInfo_ProvMileage, str, sSubKey,
               QString::number(cpcRoadInfo.nProvTradeMeter_After));
    StdInfoLog(LogKey::OneKey_CPCStationInfo, LogKey::CPCStationInfo_ProvStartPoint, str, sSubKey,
               cpcRoadInfo.sProvEnFlag);

    QDateTime sEnTime = QDateTime::fromTime_t(cpcRoadInfo.nProvEnFlagPassTime);
    QString sTime = sEnTime.toString("yyyy-MM-dd hh:mm:ss");
    StdInfoLog(LogKey::OneKey_CPCStationInfo, LogKey::CPCStationInfo_ProvStartTime, str, sSubKey,
               sTime);
    StdInfoLog(LogKey::OneKey_CPCStationInfo, LogKey::CPCStationInfo_LastGantryInfo, str, sSubKey,
               cpcRoadInfo.sNewFlag_After);
    StdInfoLog(LogKey::OneKey_CPCStationInfo, LogKey::CPCStationInfo_GantryCnt, str, sSubKey,
               QString::number(cpcRoadInfo.bPassFlagCnt_After));
    StdInfoLog(LogKey::OneKey_CPCStationInfo, LogKey::CPCStationInfo_GantryInfo, str, sSubKey,
               cpcRoadInfo.FlagInfoList_After);
    return;
}

void CStdLog::StdLog_CPCFeeInfo(const QString &sSubKey, const CCPCTollCellInfo_New &cpcTollCellInfo)
{
    QTime time = QTime::currentTime();
    QString str = time.toString("hhmmsszzz");
    QList<CProvCellInfo>::const_iterator it = cpcTollCellInfo.ProCellInfoList.begin();

    for (; it != cpcTollCellInfo.ProCellInfoList.end(); ++it) {
        StdInfoLog(LogKey::OneKey_CPCFeeInfo, LogKey::CPCFeeInfo_ProvID, str, sSubKey,
                   QString::number(it->bProv));
        StdInfoLog(LogKey::OneKey_CPCFeeInfo, LogKey::CPCFeeInfo_ProvGantryCnt, str, sSubKey,
                   QString::number(it->bProvFlagCnt));
        StdInfoLog(LogKey::OneKey_CPCFeeInfo, LogKey::CPCFeeInfo_ProvFee, str, sSubKey,
                   QString::number(it->nProvTollMoney));
        StdInfoLog(LogKey::OneKey_CPCFeeInfo, LogKey::CPCFeeInfo_ProvMileage, str, sSubKey,
                   QString::number(it->nProvTradeMeter));
        StdInfoLog(LogKey::OneKey_CPCFeeInfo, LogKey::CPCFeeInfo_ProvStartPoint, str, sSubKey,
                   it->sEnFlag);
        QDateTime enTime = QDateTime::fromTime_t(it->nPassTime);

        StdInfoLog(LogKey::OneKey_CPCFeeInfo, LogKey::CPCFeeInfo_ProvStartTime, str, sSubKey,
                   enTime.toString("yyyy-MM-dd hh:mm:ss"));

        StdInfoLog(LogKey::OneKey_CPCFeeInfo, LogKey::CPCFeeInfo_LastGantryInfo, str, sSubKey,
                   it->sNewFlag);
        StdInfoLog(LogKey::OneKey_CPCFeeInfo, LogKey::CPCFeeInfo_FitStatus, str, sSubKey,
                   QString::number(it->bSign));
    }
    return;
}

void CStdLog::StdLogLaneInfo(const QDateTime &workDate)
{
    QString KeyFlag1 = QTime::currentTime().toString("hhmmsszzz");
    //    const QDate lDate = ShiftParam.tmWorkDate.date();
    //  if(bAppStart)
    //    StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_AppStart, str, QString(),
    //    QString("班次切换"));

    StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_StationID, KeyFlag1, KeyFlag1,
               QString("%1").arg(Ptr_Info->GetStationID()));
    StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_LaneID, KeyFlag1, KeyFlag1,
               QString("%1").arg(Ptr_Info->GetLaneId()));
    StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_LaneYGZ, KeyFlag1, KeyFlag1,
               Ptr_Info->GetGBLaneId());
    StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_LaneType, KeyFlag1, KeyFlag1,
               QString("%1").arg(Ptr_Info->GetLaneType()));
    StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_RSUComm, KeyFlag1, KeyFlag1,
               Ptr_Info->GetRSUComm());
    StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_RSUPower, KeyFlag1, KeyFlag1,
               Ptr_Info->GetRSUPower());
    StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_RSUChannelID, KeyFlag1, KeyFlag1,
               Ptr_Info->GetRSUChannelID());
    StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_LaneVersion, KeyFlag1, KeyFlag1,
               Ptr_Info->GetTransVer());
    StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_Squaddate, KeyFlag1, KeyFlag1,
               workDate.toString("yyyyMMdd"));
    return;
}
