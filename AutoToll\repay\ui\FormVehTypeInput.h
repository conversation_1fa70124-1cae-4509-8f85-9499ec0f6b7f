#ifndef FORMVEHTYPEINPUT_H
#define FORMVEHTYPEINPUT_H

#include <QWidget>
#include <QLineEdit>
#include <QLabel>
#include "baseopwidget.h"
#include "lanetype.h"

/**
 * @brief 车型输入界面类
 * 用于补费流程中的车型选择，支持字母键快速选择车型
 * A:客一, B:客二, C:客三, D:客四, E:货一, F:货二, G:货三, H:货四, J:货五, K:货六
 * L:专一, M:专二, N:专三, P:专四, Q:专五, R:专六
 */
class FormVehTypeInput : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormVehTypeInput(QWidget *parent = 0);
    ~FormVehTypeInput();

    /**
     * @brief 显示车型输入界面
     * @param defaultVehType 默认车型（可选）
     * @return true-用户选择了车型，false-用户取消
     */
    bool InputVehType(CVehClass defaultVehType = VC_None);
    
    /**
     * @brief 获取用户选择的车型
     * @return 选择的车型编号
     */
    CVehClass GetSelectedVehType() const { return m_selectedVehType; }
    
    /**
     * @brief 获取车型名称
     * @return 车型名称（如"客一"、"货二"等）
     */
    QString GetSelectedVehTypeName() const;

protected:
    /**
     * @brief 初始化界面
     */
    void InitUI();
    
    /**
     * @brief 处理键盘输入
     * @param mtcKeyEvent 键盘事件
     * @return 处理结果
     */
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    
    /**
     * @brief 绘制界面
     * @param event 绘制事件
     */
    void paintEvent(QPaintEvent *event);
    
    /**
     * @brief 处理字母键输入
     * @param letter 输入的字母（A-Z）
     */
    void OnLetterInput(char letter);
    
    /**
     * @brief 更新车型显示
     */
    void UpdateVehTypeDisplay();
    
    /**
     * @brief 获取字母对应的车型
     * @param letter 字母键
     * @return 对应的车型，如果无效返回VC_None
     */
    CVehClass GetVehTypeByLetter(char letter);
    
    /**
     * @brief 获取车型对应的字母
     * @param vehType 车型
     * @return 对应的字母，如果无效返回0
     */
    char GetLetterByVehType(CVehClass vehType);

private:
    // 界面控件
    QLabel *m_pLblVehTypeTitle;     // "车型"标题标签
    QLabel *m_pLblVehTypeName;      // 车型名称显示标签
    
    // 数据成员
    CVehClass m_selectedVehType;    // 选择的车型
    QString m_inputBuffer;          // 输入缓冲区
    
    // 车型映射表
    static const struct VehTypeMapping {
        char letter;
        CVehClass vehType;
        const char* name;
    } s_vehTypeMappings[];
};

#endif // FORMVEHTYPEINPUT_H