#ifndef BASEOPWIDGET_H
#define BASEOPWIDGET_H

#include <QEventLoop>
#include <QWidget>
#include <QtGui/QLabel>

#include "MtcKey/MtcKeyDef.h"
#include "cardreader.h"
#include "showmessage.h"
#include "cpaymentmachine.h"
class CBaseOpWidget : public QWidget
{
    Q_OBJECT
public:
    explicit CBaseOpWidget(QWidget *parent = 0);
    enum
    {
        Rlt_Cancel,
        Rlt_OK
    };

    virtual void InitUI(int iFlag = 0);

    //按键处理函数，处理成功返回1 失败返回 0
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    //卡片事件处理函数 -需要在函数内关闭卡片 。返回 true 停止卡片检测线程 false -会继续检测卡片
    virtual bool OnOpenCardEvent(int nReadId, int nCardType);
    virtual void OnQrCodeEvent(int nPos, const QString &sQrCode);
    void closeEvent(QCloseEvent *event);
    int doModalShow(bool bReadCard = false, quint32 mSeconds = 0);
    void SetTitle(const QString &sTitle);
    void SetMessage(const QString &sMessage, bool bRefresh = true);
    void ShowErrorMsg(const QString &sErrorMsg);
    int ModalResult() { return m_nModalResult; }
    //刷新显示
    void RefreshMessage();
    void OnOk();
    void OnCancel();
signals:

public slots:
    void OnReaderTimerOut();

protected:
    void resizeEvent(QResizeEvent *event);
    void paintEvent(QPaintEvent *event);

    bool eventFilter(QObject *obj, QEvent *event)
    {
        if (event->type() == MtcKeyPressedEvent::getEventType()) {
            MtcKeyPressedEvent *mtcKeyEvent = static_cast<MtcKeyPressedEvent *>(event);
            mtcKeyPressed(mtcKeyEvent);
            RecordKeyEvent(mtcKeyEvent);
            return true;
        } else {
            // standard event processing
            return QObject::eventFilter(obj, event);
        }
    }

    virtual void customEvent(QEvent *event)
    {
        if (event->type() == MtcKeyPressedEvent::getEventType()) {
            MtcKeyPressedEvent *mtcKeyEvent = static_cast<MtcKeyPressedEvent *>(event);
            mtcKeyPressed(mtcKeyEvent);
            RecordKeyEvent(mtcKeyEvent);

        } else if (event->type() == CCardOpenEvent::getEventType()) {
            CCardOpenEvent *pCardOpenEvent = static_cast<CCardOpenEvent *>(event);
            if (!OnOpenCardEvent(pCardOpenEvent->GetReaderId(), pCardOpenEvent->GetCardType())) {
                CCardReader::ContinueCardDetection();
            } else {
            }
        } else if (event->type() == CScanQrCodeEvent::getEventType()) {
            CScanQrCodeEvent *pEvent = static_cast<CScanQrCodeEvent *>(event);
            OnQrCodeEvent(pEvent->GetIndex(), pEvent->GetQrCode());
        }
    }

    void filterChildKeyEvent(QWidget *child) { child->installEventFilter(this); }

    void filterChildrenKeyEvent()
    {
        QList<QWidget *> widgets = this->findChildren<QWidget *>();

        for (int i = 0; i < widgets.size(); i++) {
            widgets[i]->installEventFilter(this);
        }
    }
    //当界面显示之后的操作
    virtual void OnModalShowed();

protected:
    void setModalResult(int nResult);
    void RecordKeyEvent(MtcKeyPressedEvent *mtcKeyEvent);

protected:
    QString m_sTitle;
    Cmessagelabel *m_lblMessage;
    QEventLoop *m_pEventLoop;
    int m_nModalResult;
    bool m_bReadingCard;
    QTimer *m_pReadCardTimer;

    static CBaseOpWidget *pTopWidget;
};

#endif  // CLANEOPFRM_H
