#include"update.h"
#include"globalutils.h"

Update::Update()
{

}
bool Update::needUpdate=false;
bool Update::readyToUpdate()
{

    QDir dir("./update");
    dir.setFilter(QDir::Files);
    if(dir.entryInfoList().length()!=0)
    {
        needUpdate=true;
        MsgLog("存在升级文件，开始升级...");
        qApp->closeAllWindows();
        SleeperThread::msleep(1000);
        QProcess::startDetached("AutoTollUpgrade.exe", QStringList());
        return true;
    }
    return false;

}
