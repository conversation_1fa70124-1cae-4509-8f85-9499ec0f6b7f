#include "vipumgr.h"
#include "etclanectrl.h"
#include "ilogmsg.h"

CVIPUMgr::CVIPUMgr(QObject *parent) : CPausableThread(parent)
{
    setObjectName(QString("viputhread"));
    m_waitTime = 500;
}

void CVIPUMgr::AddVehInfo(const CAutoRegInfo &regInfo, VcrResult *pVcrResult)
{
    CAutoVehInfo vehInfo;
    vehInfo.vprInfo = regInfo;
    if (pVcrResult) {
        if (pVcrResult->bigImage[VehImg_Tail].sImgFile.length() > 0)
            DebugLog(QString("保存车辆自动识别图片,车尾图片:%1")
                         .arg(pVcrResult->bigImage[VehImg_Tail].sImgFile));
        vehInfo.vcrResult = *pVcrResult;
    }
    {
        QMutexLocker locker(&m_vehMt);
        m_vehList.push_back(vehInfo);
    }
    ResumeThread();
}

bool CVIPUMgr::RunOnce()
{
    // QMutexLocker locker(&m_vehMt);
    m_vehMt.lock();
    if (m_vehList.isEmpty()) {
        m_vehMt.unlock();
        this->PauseThread();
        return true;
    }

    CAutoVehInfo vehInfo;
    QList<CAutoVehInfo>::iterator it = m_vehList.begin();
    vehInfo = *it;
    m_vehMt.unlock();

    QDateTime curTime = QDateTime::currentDateTime();
    CAutoRegInfo regInfo = vehInfo.vprInfo;

    qint64 nPastMSeconds = regInfo.AuRegTime.msecsTo(curTime);
    QString sTailFileName = vehInfo.vcrResult.bigImage[VehImg_Tail].sImgFile;
    bool bSend = false;
    if (sTailFileName.length() > 0) {
        DebugLog(QString("保存VIPU,车尾文件:%1,video:%2 直接生成")
                     .arg(sTailFileName)
                     .arg(vehInfo.vcrResult.sVideoFileName));
        Ptr_ETCCtrl->SaveVIPUMsg(regInfo, &vehInfo.vcrResult);
        bSend = true;
    } else {
        qint64 nMaxSeconds = 2000 * (vehInfo.nCheckTime + 1);  //每两秒检查一次，最多三次
        if (nPastMSeconds > nMaxSeconds) {
            //
            it->nCheckTime++;
            VcrResult *pVcrResult = NULL;
            VcrResult vcrResult;
            VCRDev *pVcrDev = CDeviceFactory::GetVCRDev();
            if (pVcrDev) {
                bool bHaveVcr =
                    pVcrDev->GetVcrResult(regInfo.nAutoVLPColor, regInfo.sAutoVehPlate, vcrResult);
                if (bHaveVcr) {
                    if (pVcrDev->bSendVideo()) {
                        if (vcrResult.sVideoFileName.length() == 0)
                            vcrResult.sVideoFileName = pVcrDev->GetVideoFileName(vcrResult.dwCarID);
                    }
                    pVcrResult = &vcrResult;
                    DebugLog(QString("regetvcr,vc:%1,videoFileName:%2,tailfileName:%3,cardid:%4")
                                 .arg((int)pVcrResult->vehclass)
                                 .arg(pVcrResult->sVideoFileName)
                                 .arg(pVcrResult->bigImage[VehImg_Tail].sImgFile)
                                 .arg(pVcrResult->dwCarID));
                }
            }

            if (pVcrResult) {
                QString sTailFileName = pVcrResult->bigImage[VehImg_Tail].sImgFile;
                if (sTailFileName.length() > 0) {
                    DebugLog(QString("保存vipu,%1,tailfile:%2,videofile:%3,延迟%4处理")
                                 .arg(regInfo.sAutoVehPlate)
                                 .arg(sTailFileName)
                                 .arg(pVcrResult->sVideoFileName)
                                 .arg(nPastMSeconds));
                    Ptr_ETCCtrl->SaveVIPUMsg(regInfo, pVcrResult);
                    bSend = true;
                }
            }
            if (!bSend) {
                if (it->nCheckTime >= 3) {
                    DebugLog(QString("最终保存vipu,%1,延迟%2处理")
                                 .arg(regInfo.sAutoVehPlate)
                                 .arg(nPastMSeconds));
                    Ptr_ETCCtrl->SaveVIPUMsg(regInfo, pVcrResult);
                    bSend = true;
                }
            }
        }
    }
    if (bSend) {
        QMutexLocker locker(&m_vehMt);
        m_vehList.pop_front();
    }
    return true;
}

void CVIPUMgr::AfterRun()
{
    while (m_vehList.size() > 0) {
        CAutoVehInfo vehInfo = m_vehList.first();
        Ptr_ETCCtrl->SaveVIPUMsg(vehInfo.vprInfo, &vehInfo.vcrResult);
        m_vehList.pop_front();
    }
}

bool CVIPUMgr::InitVIPUMgr()
{
    // this->ResumeThread();
    return true;
}

void CVIPUMgr::FreeVIPUMgr() { this->StopThread(); }
