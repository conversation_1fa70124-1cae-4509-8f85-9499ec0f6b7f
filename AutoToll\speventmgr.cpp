#include "speventmgr.h"
#include "lanetype.h"
#include "lanetype.h"

CSpEventMgr::CSpEventMgr() {}

CSpEvent CSpEventMgr::GetSpEvent(qint32 nSpId, bool bIsExit)
{
    CSpEvent spEvent;
    spEvent.nSpId = nSpId;
    switch (nSpId) {
    case CSpEventMgr::SpEvent_None:
        spEvent.bAlarm = false;
        spEvent.bStop = false;
        spEvent.nFaileCause = FaileCause_None;
        spEvent.FareDisplayInfo = QString("");
        spEvent.DisplayInfo = QString("");
        break;
    case CSpEventMgr::SPEvent_Violate:  //不发送事件报文
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("车辆闯关");
        spEvent.DisplayInfo = QString("车辆闯关");
        break;

    case CSpEventMgr::SpEvent_NoOBU:  //无电子标签47
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("未检测到标签");
        spEvent.DisplayInfo = QString("无标签");
        break;

    case CSpEventMgr::SpEvent_UnUseOBU: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_OBUNotInUse;
        spEvent.FareDisplayInfo = QString("标签未启用");
        spEvent.DisplayInfo = QString("标签未启用");
        break;
    }

    case CSpEventMgr::SpEvent_OutTimeOBU:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_OBUOutTime;
        spEvent.FareDisplayInfo = QString("标签已过期");
        spEvent.DisplayInfo = QString("标签过期");
        break;

    case CSpEventMgr::SpEvent_BlackOBU:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_OBUStop;
        spEvent.FareDisplayInfo = QString("标签禁用");
        spEvent.DisplayInfo = QString("标签黑名单");
        break;

    case CSpEventMgr::SpEvent_DissambleOBU:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Dissamble;
        spEvent.FareDisplayInfo = QString("标签已拆卸");
        spEvent.DisplayInfo = QString("标签非法拆卸");
        break;

    case CSpEventMgr::SpEvent_OBULowPower: {
        spEvent.bAlarm = false;
        spEvent.bStop = false;
        spEvent.nFaileCause = FaileCause_None;
        spEvent.FareDisplayInfo = QString("标签电量低");
        spEvent.DisplayInfo = QString("标签电量低");
        break;
    }

    case CSpEventMgr::SpEvent_NotInsertCard:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_NoCard;
        spEvent.FareDisplayInfo = QString("卡未插好");
        spEvent.DisplayInfo = QString("标签无卡");
        break;

    case SpEvent_OBUVehClassErr: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_OBUStop;
        spEvent.FareDisplayInfo = QString("标签车型无效");
        spEvent.DisplayInfo = QString("标签无效车型");
        break;
    }

    case CSpEventMgr::SpEvent_NotLocalOBU:  //非本地电子标签61
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_OBUStop;
        spEvent.FareDisplayInfo = QString("标签无效");
        spEvent.DisplayInfo = QString("未联网标签");
        break;

    case CSpEventMgr::SpEvent_DisturbCar:  //跟车干扰72
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;  //
        spEvent.FareDisplayInfo = QString("前方有异常车");
        spEvent.DisplayInfo = QString("前车干扰");
        break;

    case CSpEventMgr::SpEvent_UnUseCard: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_CardNotInUse;
        spEvent.FareDisplayInfo = QString("卡未启用");
        spEvent.DisplayInfo = QString("卡未启用");
        break;
    }

    case CSpEventMgr::SpEvent_OutTimeCard:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_CardOutTime;
        spEvent.FareDisplayInfo = QString("卡已过期");
        spEvent.DisplayInfo = QString("过期卡");
        break;

    case CSpEventMgr::SpEvent_NotLocalCard:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_CardIssueErr;
        spEvent.FareDisplayInfo = QString("卡无效");
        spEvent.DisplayInfo = QString("未联网卡");
        break;

    case CSpEventMgr::SpEvent_AbnormalCard:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_CardStop;
        spEvent.FareDisplayInfo = QString("卡无效");
        spEvent.DisplayInfo = QString("无效卡类型");
        break;

    case CSpEventMgr::SpEvent_LostCard:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_CardLost;
        spEvent.FareDisplayInfo = QString("卡已挂失");
        spEvent.DisplayInfo.clear();  // =QString("黑名单挂失卡");
        break;

    case CSpEventMgr::SpEvent_CancleCard:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_CardStop;
        spEvent.FareDisplayInfo = QString("卡已注销");
        spEvent.DisplayInfo.clear();  // =QString("黑名单注销卡");
        break;
    case CSpEventMgr::SpEvent_OverDraftCard: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_CardStop;
        spEvent.FareDisplayInfo = QString("卡已透支");
        spEvent.DisplayInfo.clear();  // =QString("黑名单透支卡");
        break;
    }

    case CSpEventMgr::SpEvent_BlackCard:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_CardStop;
        spEvent.FareDisplayInfo = QString("卡禁用");
        spEvent.DisplayInfo.clear();  // =QString("黑名单禁用卡");
        break;

    case CSpEventMgr::SpEvent_WriteCardFailed:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("写卡失败");
        spEvent.DisplayInfo = QString("写卡失败");
        break;

    case CSpEventMgr::SpEvent_WillLockCard:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("卡校验异常,交易失败");
        spEvent.DisplayInfo = QString("将锁卡");
        break;
    case CSpEventMgr::SpEvent_HaveLockCar:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("ETC卡已被锁,交易失败");
        spEvent.DisplayInfo = QString("已锁卡");
        break;

    case CSpEventMgr::SpEvent_NotSameOBUCard: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_OBUCardIssueDiff;
        spEvent.FareDisplayInfo = QString("标签和卡不配套");
        spEvent.DisplayInfo = QString("卡签发行属地不一致");
        break;
    }

    case CSpEventMgr::SpEvent_InvalidEntry:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_NoEntry;
        spEvent.FareDisplayInfo = QString("无效入口");
        spEvent.DisplayInfo = QString("出入口不匹配");
        break;

    case CSpEventMgr::SpEvent_VehClassDiff:  //入出口车型不符，且超时时触发改事件
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("出入口车型不一致");
        spEvent.DisplayInfo = QString("出入口车型不一致");
        break;

    case CSpEventMgr::SpEvent_VehPlateDiff:  //同上
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("出入口车牌不一致");
        spEvent.DisplayInfo = QString("出入口车牌不一致");
        break;

    case CSpEventMgr::SpEvent_NoEntry:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_NoEntry;
        spEvent.FareDisplayInfo = QString("无入口信息");
        spEvent.DisplayInfo = QString("无入口信息");
        break;

    case CSpEventMgr::SpEvent_U:  // U型车，通行费大于0
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_NoEntry;
        spEvent.FareDisplayInfo = QString("入口无效");
        spEvent.DisplayInfo = QString("非法U转车");
        break;

    case CSpEventMgr::SpEvent_NotSelfCard:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_VehCardNotSame;
        spEvent.FareDisplayInfo = QString("标签和卡不配套");
        spEvent.DisplayInfo = QString("车牌绑定不符");
        break;
        /*
                case CSpEventMgr::SpEvent_BlackCard:
                    spEvent.bAlarm =true;
                    spEvent.bStop =true;
                    if(bIsExit)
                        spEvent.nLaneEventId = nSpId;
                    else
                        spEvent.nLaneEventId = nSpId;
                    spEvent.FareDisplayInfo = QString("黑名单卡\n请走人工车道");
                    spEvent.DisplayInfo=QString("黑名单卡，限制通行");
                    break;

            */

    case CSpEventMgr::SpEvent_OverTime:  //车辆超时
        spEvent.bAlarm = false;
        spEvent.bStop = false;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("行驶超时");
        spEvent.DisplayInfo = QString("行驶超时");
        break;

    case CSpEventMgr::SpEvent_OutTimeStay:  //等待超时
        spEvent.bAlarm = true;
        spEvent.bStop = false;
        spEvent.nFaileCause = FaileCause_None;
        spEvent.FareDisplayInfo = QString("超时,请注意落杆");
        spEvent.DisplayInfo = QString("超时停留");
        break;

    case CSpEventMgr::SpEvent_NoBalance:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_NoBalance;
        spEvent.FareDisplayInfo = QString("余额不足");
        spEvent.DisplayInfo = QString("余额不足");
        break;
    case CSpEventMgr::SpEvent_PSamLock:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_None;
        spEvent.FareDisplayInfo = QString("系统故障");
        spEvent.DisplayInfo = QString("PSAM锁");
        break;

    case CSpEventMgr::SpEvent_PsamError:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_None;
        spEvent.FareDisplayInfo = QString("系统故障");
        spEvent.DisplayInfo = QString("PSAM卡异常");
        break;

    case CSpEventMgr::SpEvent_BlackCar:
        spEvent.bAlarm = true;
        spEvent.bStop = true;

        spEvent.nFaileCause = FaileCause_OweBlackList;

        spEvent.FareDisplayInfo = QString("车辆禁用");
        spEvent.DisplayInfo = QString("车辆黑名单");
        break;

    case CSpEventMgr::SpEvent_TransFailed:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_None;
        spEvent.FareDisplayInfo = QString("交易失败");
        spEvent.DisplayInfo = QString("扣款失败");
        break;

    case CSpEventMgr::SpEvent_EntryNoFee:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_NoFare;
        spEvent.FareDisplayInfo = QString("无效入口");
        spEvent.DisplayInfo = QString("路径不可达/无费率");
        break;
    case CSpEventMgr::SpEvent_MutlPath:
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_NoEntry;
        spEvent.FareDisplayInfo = QString("路径标识信息不完整");
        spEvent.DisplayInfo = QString("路径不唯一");
        break;

    case CSpEventMgr::SpEvent_LogOut:
        spEvent.bAlarm = false;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("车道关闭");
        spEvent.DisplayInfo = QString("下班");
        break;

    case CSpEventMgr::SpEvent_FrameError: {
        spEvent.bAlarm = false;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_None;
        spEvent.FareDisplayInfo = QString("天线交易失败");
        spEvent.DisplayInfo.clear();
        break;
    }

    case SPEVENT_EF04_VALID: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_NoEntry;
        spEvent.FareDisplayInfo = QString("EF04信息无效");
        spEvent.DisplayInfo.clear();
        break;
    }

    case SPEVENT_EF04_ABNORMAL: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_FeeCalcFailed;
        spEvent.FareDisplayInfo = QString("标签计费异常");
        spEvent.DisplayInfo.clear();
        break;
    }
    case SPEVENT_EF04_ReadFaild: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        if (bIsExit) {
            spEvent.FareDisplayInfo = QString("入口信息无效");
        } else {
            spEvent.FareDisplayInfo = QString("读标签失败");
        }

        spEvent.DisplayInfo.clear();
        break;
    }
    case SPEVENT_EF04_OBUCARDDIF: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_NoEntry;
        spEvent.FareDisplayInfo = QString("EF04与卡信息不符");
        spEvent.DisplayInfo.clear();
        break;
    }
    case SPEVENT_TransStop: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("交易异常");
        spEvent.DisplayInfo.clear();
        break;
    }
    case SPEvent_TolalFeeOver: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_OBUFeeErr;
        spEvent.FareDisplayInfo = QString("通行费异常");
        spEvent.DisplayInfo.clear();
        break;
    }
    case SPEvent_StopPassByTime: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_StopPassByTime;
        spEvent.FareDisplayInfo = QString("客车限时通行");
        spEvent.DisplayInfo = QString("客2以上车辆限时通行");
        break;
    }

    case SpEvent_TruckStopPass: {
        spEvent.bAlarm = true;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("");
        spEvent.DisplayInfo = QString("");
        break;
    }
    case SpEvent_Ignore: {
        spEvent.bAlarm = false;
        spEvent.bStop = false;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo.clear();
        spEvent.DisplayInfo.clear();
        break;
    }
    case SpEvent_Truck_OBU: {
        spEvent.bAlarm = true;
        spEvent.bStop = false;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("OBU非普通货车");
        spEvent.DisplayInfo = QString("OBU非普通货车");
    }
    default: {
        spEvent.nSpId = SpEvent_None;
        spEvent.bAlarm = false;
        spEvent.bStop = true;
        spEvent.nFaileCause = FaileCause_Other;
        spEvent.FareDisplayInfo = QString("");
        spEvent.DisplayInfo = QString("");
        break;
    }
    }
    return spEvent;
}

qint32 CSpEventMgr::GetEventIdByCardBListType(qint32 bType)
{
    switch (bType) {
    case 1:
        return SpEvent_LostCard;
        break;
    case 3:
        return SpEvent_CancleCard;
        break;
    case 4:
        return SpEvent_OverDraftCard;
    default:
        return SpEvent_BlackCard;
        break;
    }
}
