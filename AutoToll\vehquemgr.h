#ifndef CVEHQUEMGR_H
#define CVEHQUEMGR_H

#include <QObject>
#include <QMutex>
/**
 * @brief 车辆队列管理
 */
class CVehQueMgr : public QObject
{
    Q_OBJECT
public:
    explicit CVehQueMgr(QObject *parent = 0);

public:
    void SetAllowPass(qint32 nAllowCount=1);
    void SetRefusePass(bool bResetVehCount=false);
    bool ProcessBackLoopEvent(bool bStatus);
    //获取未过车数量
    int GetAllowPass();
signals:

public slots:

private:
    QMutex m_Mutex;
    volatile qint32 m_nAllowPassCount;//正常离开车辆数
    volatile qint32 m_nDetectCount;   //系统检测车辆数
};

#endif // CVEHQUEMGR_H
