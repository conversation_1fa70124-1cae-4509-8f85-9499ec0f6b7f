#ifndef TOLLMONEYFM_H
#define TOLLMONEYFM_H

#include "baseopwidget.h"
#include "lineedit.h"
#include "lanetype.h"


#define MAX_LABEL_NUM  5
class CTollMoneyFm : public CBaseOpWidget
{
    Q_OBJECT
public:
    explicit CTollMoneyFm(CTollMoneyInfo TMInfo, QWidget *parent = 0);
    ~CTollMoneyFm();
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);

private:
    void Init();
    bool VerifyActualMoney(QString &sError);

private:
    QLabel *m_pLabel[MAX_LABEL_NUM];
    CNumLineEdit *m_pLineEdit;
    quint32 m_lCardCost;
    quint32 m_lTollMoney;

public:
    void SetActualMoney(QString sActualMoney);
    quint32 GetActualMoney();
};




#endif // TOLLMONEYFM_H
