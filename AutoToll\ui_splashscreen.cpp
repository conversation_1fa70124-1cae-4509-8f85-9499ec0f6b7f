#include "ui_splashscreen.h"

Ui_SplashScreen::Ui_SplashScreen(QWidget *parent)
    :QSplashScreen(parent)
    ,lbl_text(this)
{
    lbl_text.setStyleSheet("QLabel{font: 12pt \"微软雅黑\"; color: rgb(0, 0, 0);}");
    lbl_text.setGeometry(0,200,400,30);
}

void Ui_SplashScreen::showMessage(const QString message)
{
    lbl_text.setText(message);
    repaint();
}

void Ui_SplashScreen::showTime(QDateTime &dt)
{
    Q_UNUSED(dt)
    //dlg_time.showWindow(dt);
}
