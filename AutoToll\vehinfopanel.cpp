#include "vehinfopanel.h"
#include "ui_vehinfopanel.h"
#include "lanetype.h"
#include "globalutils.h"
#include "cardfileconverter.h"
#include "../log4qt/ilogmsg.h"
#include "vehplatefunc.h"
#include "VCRType.h"

#define WND_WIDTH 606
#define WND_HEIGHT 422

#ifdef Q_OS_WIN32
#define FONT_NAME "微软雅黑"
#else
#define FONT_NAME "微软雅黑"
#endif

VehInfoPanel::VehInfoPanel(QWidget *parent) :
    QWidget(parent),CTransInfoUI(),
    ui(new Ui::VehInfoPanel)
{
    ui->setupUi(this);
}

VehInfoPanel::~VehInfoPanel()
{
    delete ui;
}

//初始化界面显示
void VehInfoPanel::InitUIControls(qint32 nLaneType)
{
    p_bluePlateColor = new QPixmap(":vehInfoPanel/blue");
    p_blackPlateColor = new QPixmap(":vehInfoPanel/black");
    p_yellowPlateColor = new QPixmap(":vehInfoPanel/yellow");
    p_whitePlateColor = new QPixmap(":vehInfoPanel/white");

    //褐色字体
    QPalette paGray;
    paGray.setColor(QPalette::WindowText, QColor(111, 113, 128));
    //橙色字体
    QPalette paOrange;
    paOrange.setColor(QPalette::WindowText, QColor(255, 108, 0));
    //红色字体
    QPalette paRed;
    paRed.setColor(QPalette::WindowText, QColor(255, 0, 0));
    //白色字体
    QPalette paWhite;
    paWhite.setColor(QPalette::WindowText, QColor(255, 255, 255));
    //黑色字体
    QPalette paBlack;
    paBlack.setColor(QPalette::WindowText, QColor(46, 46, 56));
    //车辆信息
    QFont ftVehmisc;
    ftVehmisc.setFamily(FONT_NAME);
    ftVehmisc.setPixelSize(16);
    ftVehmisc.setStyleStrategy(QFont::PreferAntialias);

    //OBU号
    ui->lblOBU->setFont(ftVehmisc);
    ui->lblOBU->setPalette(paGray);
    ui->lblOBU->resize(270, 16);
    ui->lblOBU->move(25, 30);
    //卡号
    ui->lblCardID->setFont(ftVehmisc);
    ui->lblCardID->setPalette(paGray);
    ui->lblCardID->resize(270, 16);
    ui->lblCardID->move(25, 53);
    //入口站
    ui->lblEnStation->setFont(ftVehmisc);
    ui->lblEnStation->setPalette(paGray);
    ui->lblEnStation->resize(270, 16);
    ui->lblEnStation->move(25, 76);
    //业务时间
    ui->lblWasteTime->setFont(ftVehmisc);
    ui->lblWasteTime->setPalette(paGray);
    ui->lblWasteTime->resize(270, 16);
    ui->lblWasteTime->move(341, 30);
    //卡类型
    ui->lblCardType->setFont(ftVehmisc);
    ui->lblCardType->setPalette(paGray);
    ui->lblCardType->resize(270, 16);
    ui->lblCardType->move(341, 53);
    //入口时间
    ui->lblEnTime->setFont(ftVehmisc);
    ui->lblEnTime->setPalette(paGray);
    ui->lblEnTime->resize(270, 16);
    ui->lblEnTime->move(341, 76);

    //业务信息的标题
    QFont ftVehInfoName;
    ftVehInfoName.setFamily(FONT_NAME);
    ftVehInfoName.setPixelSize(20);
    ftVehInfoName.setStyleStrategy(QFont::PreferAntialias);
    //车型
    ui->lblVehClassName->setFont(ftVehInfoName);
    ui->lblVehClassName->setPalette(paGray);
    ui->lblVehClassName->resize(45, 21);
    ui->lblVehClassName->move(25, 150);
    ui->lblVehClassName->setText("车型");
    //车牌
    ui->lblPlateName->setFont(ftVehInfoName);
    ui->lblPlateName->setPalette(paGray);
    ui->lblPlateName->resize(45, 21);
    ui->lblPlateName->move(25, 205);
    ui->lblPlateName->setText("车牌");
    //车种
    ui->lblVehTypeName->setFont(ftVehInfoName);
    ui->lblVehTypeName->setPalette(paGray);
    ui->lblVehTypeName->resize(45,21);
    ui->lblVehTypeName->move(25,260);
    ui->lblVehTypeName->setText("车种");
    //金额
    ui->lblMoneyName->setFont(ftVehInfoName);
    ui->lblMoneyName->setPalette(paGray);
    ui->lblMoneyName->resize(45, 21);
    ui->lblMoneyName->move(25, 353);
    ui->lblMoneyName->setText("金额");
    //元
    ui->lblYuan1->setFont(ftVehInfoName);
    ui->lblYuan1->setPalette(paOrange);
    ui->lblYuan1->resize(20, 21);
    ui->lblYuan1->move(240, 355);

    //入口不显示金额
    if(LaneType_EtcEntry== nLaneType)
    {
        ui->lblMoneyName->setVisible(false);
        ui->lblYuan1->setVisible(false);
        ui->lblMoney->setVisible(false);
    }

    //识别车型
    ui->lblRecgVehClassName->setFont(ftVehInfoName);
    ui->lblRecgVehClassName->setPalette(paGray);
    ui->lblRecgVehClassName->resize(81, 21);
    ui->lblRecgVehClassName->move(338, 171-10);
    ui->lblRecgVehClassName->setText("识别车型");
    //识别车牌
    ui->lblRecgPlateName->setFont(ftVehInfoName);
    ui->lblRecgPlateName->setPalette(paGray);
    ui->lblRecgPlateName->resize(81, 21);
    ui->lblRecgPlateName->move(338, 231-15);
    ui->lblRecgPlateName->setText("识别车牌");
    //余额

    QFont ftBalance;
    ftBalance.setFamily(FONT_NAME);
    ftBalance.setPixelSize(16);
    ftBalance.setStyleStrategy(QFont::PreferAntialias);

    ui->lblBalanceName->setFont(ftBalance);
    ui->lblBalanceName->setPalette(paGray);
    ui->lblBalanceName->resize(45, 21);
    ui->lblBalanceName->move(338, 353);
    ui->lblBalanceName->setText("余额");
    //元
    ui->lblYuan2->setFont(ftVehInfoName);
    ui->lblYuan2->setPalette(paOrange);
    ui->lblYuan2->resize(20, 21);
    ui->lblYuan2->move(543, 353);


    //车型显示
    QFont ftVehInfo;
    ftVehInfo.setFamily(FONT_NAME);
    ftVehInfo.setPixelSize(32);
    ftVehInfo.setStyleStrategy(QFont::PreferAntialias);
    ui->lblVehClass->setFont(ftVehInfo);
    ui->lblVehClass->setPalette(paGray);
    ui->lblVehClass->resize(101, 35);
    ui->lblVehClass->move(106, 140);
    ui->lblVehClass->setText("BGFDSNBGFNBGF");
    //车牌显示
    ui->lblPlate->setFont(ftVehInfo);
    ui->lblPlate->setPalette(paGray);
    ui->lblPlate->resize(200, 35);
    ui->lblPlate->move(106, 200);

    //车牌颜色
    ui->lblPlateColor->resize(213,56);
    ui->lblPlateColor->move(86,190);

    //车种
    ui->lblVehType->setFont(ftVehInfo);
    ui->lblVehType->setPalette(paGray);
    ui->lblVehType->resize(200,35);
    ui->lblVehType->move(106,255);

    //识别车辆信息
    QFont ftRecgVehInfo;
    ftRecgVehInfo.setFamily(FONT_NAME);
    ftRecgVehInfo.setPixelSize(27);
    ui->lblRecVehClass->setFont(ftRecgVehInfo);
    ui->lblRecVehClass->setPalette(paRed);
    ui->lblRecVehClass->resize(101, 35);
    ui->lblRecVehClass->move(431, 163-10);

    //识别车牌
    ui->lblRecPlate->setFont(ftRecgVehInfo);
    ui->lblRecPlate->setPalette(paRed);
    ui->lblRecPlate->resize(200, 35);
    ui->lblRecPlate->move(431, 220-15);

    //车牌颜色
    ui->lblRecPlateColor->resize(150,56);
    ui->lblRecPlateColor->move(431,210-15);

    //金额
    QFont ftMoney;
    ftMoney.setFamily(FONT_NAME);
    ftMoney.setPixelSize(26);
    ui->lblMoney->setFont(ftMoney);
    ui->lblMoney->setPalette(paOrange);
    ui->lblMoney->resize(160, 36);
    ui->lblMoney->move(90, 339);
    //余额
    ui->lblBalance->setFont(ftMoney);
    ui->lblBalance->setPalette(paOrange);
    ui->lblBalance->resize(160, 36);
    ui->lblBalance->move(391, 339);

    ClearVehInfo();

    ui->lblWarning->hide();
    ui->lblWarning->move(390,245);
}

void VehInfoPanel::ShowUI()
{
    show();
}

void VehInfoPanel::HideUI()
{
    close();
}

void VehInfoPanel::ClearTransInfo()
{
    ClearVehInfo();
}

void VehInfoPanel::SetOBUID(const char *szOBUID)
{
    ShowOBU(QString::fromLocal8Bit(szOBUID));
}

void VehInfoPanel::SetVehInfo(const CVehInfo &vehInfo)
{
    ShowVehInfo(vehInfo);
}

void VehInfoPanel::SetEntryInfo(const QDateTime &entryTime,const QString &sEntryStation)
{
    ShowEnStation(sEntryStation);
    ShowEnTime(&entryTime);
    return;
}

void VehInfoPanel::SetCardInfo(const CProCardBasicInfo &ProCardBasicInfo, quint32 dwBalance)
{

    ShowCardID(tr(ProCardBasicInfo.szCardNo));
    ShowBalance(ProCardBasicInfo.bType,dwBalance);
    QString sCardType=CCardFileConverter::GetCardTypeName(ProCardBasicInfo.bType);
    ShowCardType(sCardType);
    m_bCardType = ProCardBasicInfo.bType;

    return;
}

void VehInfoPanel::SetOpResult(const QDateTime &transTime, quint32 dwMoney, quint32 dwBalance)
{

    ShowWasteTime(&transTime);
    ShowMoney(dwMoney);
    ShowBalance(m_bCardType,dwBalance);

    return;
}

//初始化
qint32 VehInfoPanel::Init(qint32 nLaneType)
{
    setWindowFlags(Qt::FramelessWindowHint);
    resize(WND_WIDTH, WND_HEIGHT);
    setFixedSize(WND_WIDTH, WND_HEIGHT);

    InitUIControls(nLaneType);
    return 0;
}

void VehInfoPanel::ShowMoney(quint32 dwMoney)
{
    double fMoney= ((double)(dwMoney))/100.0;
    QString sMoney=QString("%1").arg(fMoney,8,'f',2);
    ui->lblMoney->setText(sMoney);
}

void VehInfoPanel::ShowBalance(qint8 bCardType,quint32 dwBalance)
{
    if(23==bCardType)
    {
        ui->lblBalance->setText("记账卡");
        ui->lblYuan2->setVisible(false);
        return;
    }else
        ui->lblYuan2->setVisible(true);

    double fBalance= ((double)(dwBalance))/100.0;
    QString sBalance= QString("%1").arg(fBalance,8,'f',2);
    ui->lblBalance->setText(sBalance);
}

void VehInfoPanel::ClearVehInfo()
{
    ShowOBU("");
    ShowCardID("");
    ShowEnStation("");
    ShowWasteTime(NULL);
    ShowEnTime(NULL);
    ShowCardType("");
    ui->lblBalance->setText("");
    ui->lblMoney->setText("");
    CVehInfo vehInfo;
    ShowVehInfo(vehInfo);
    ShowAutoVehInfo(vehInfo);
}

//显示OBU
void VehInfoPanel::ShowOBU(const QString& obu)
{
    QString text="OBU号 : "+obu;
    ui->lblOBU->setText(text);
}

//显示卡号
void VehInfoPanel::ShowCardID(const QString& cardid)
{
    QString text="卡　号 : "+cardid;
    ui->lblCardID->setText(text);
}

//显示入口站
void VehInfoPanel::ShowEnStation(const QString& station)
{
    QString text="入口站 : "+station;
    ui->lblEnStation->setText(text);
}

//显示业务时间
void VehInfoPanel::ShowWasteTime(const QDateTime* datetime)
{
     QString text="交易时间 : ";
     if (datetime != NULL){
         text += datetime->toString("yy/MM/dd hh:mm:ss");
     }
     ui->lblWasteTime->setText(text);
}

//显示卡类型TODO 换成编码
void VehInfoPanel::ShowCardType(const QString& cardtype)
{
    QString text="卡  类  型 : "+cardtype;
    ui->lblCardType->setText(text);
}

//显示入口时间
void VehInfoPanel::ShowEnTime(const QDateTime* datetime)
{
    QString text="入口时间 : ";
    if (datetime != NULL){
        text += datetime->toString("yy/MM/dd hh:mm:ss");
    }
    ui->lblEnTime->setText(text);
}

void VehInfoPanel::ShowVehInfo(const CVehInfo &vehInfo)
{
    m_curVehInfo.VehClass = vehInfo.VehClass;
    m_curVehInfo.nVehPlateColor = vehInfo.nVehPlateColor;
    memcpy(m_curVehInfo.szVehPlate,vehInfo.szVehPlate,sizeof vehInfo.szVehPlate);

    QString sVehClassName=GetVehClassName(vehInfo.VehClass);
    ui->lblVehClass->setText(sVehClassName);
    QString sVehPlate =GB2312toUnicode(vehInfo.szVehPlate);
    ui->lblPlate->setText(sVehPlate);
    QString sVehTypeName =GetVehTypeName(vehInfo.VehType);
    ui->lblVehType ->setText(sVehTypeName);

    //白色字体
    QPalette paWhite;
    paWhite.setColor(QPalette::WindowText, QColor(255, 255, 255));
    //黑色字体
    QPalette paBlack;
    paBlack.setColor(QPalette::WindowText, QColor(46, 46, 56));

    switch(vehInfo.nVehPlateColor)
    {
    case VP_COLOR_BLUE:
        ui->lblPlateColor->setPixmap(*p_bluePlateColor);
        ui->lblPlate->setPalette(paWhite);break;
    case VP_COLOR_YELLOW:
        ui->lblPlateColor->setPixmap(*p_yellowPlateColor);
        ui->lblPlate->setPalette(paBlack);break;
    case VP_COLOR_BLACK:
        ui->lblPlateColor->setPixmap(*p_blackPlateColor);
        ui->lblPlate->setPalette(paWhite);break;
    case VP_COLOR_WHITE:
        ui->lblPlateColor->setPixmap(*p_whitePlateColor);
        ui->lblPlate->setPalette(paBlack);break;
    default:
        ui->lblPlateColor->clear();
        break;
    }
    CheckVehInfo();
    return;
}
void VehInfoPanel::ShowAutoVehInfo(const CVehInfo &vehInfo)
{
    m_curVehInfo.AutoVehClass = vehInfo.AutoVehClass;
    m_curVehInfo.nAutoVehPlateColor = vehInfo.nAutoVehPlateColor;
    memcpy(m_curVehInfo.szAutoVehPlate,vehInfo.szAutoVehPlate,sizeof vehInfo.szAutoVehPlate);

    QString sVehClassName = GetVehClassName(vehInfo.AutoVehClass);
    ui->lblRecVehClass->setText(sVehClassName);

    QString sVehPlate = GB2312toUnicode(vehInfo.szAutoVehPlate);
    ui->lblRecPlate->setText(sVehPlate);

    //白色字体
    QPalette paWhite;
    paWhite.setColor(QPalette::WindowText, QColor(255, 255, 255));
    //黑色字体
    QPalette paBlack;
    paBlack.setColor(QPalette::WindowText, QColor(46, 46, 56));

    switch(vehInfo.nAutoVehPlateColor)
    {
    case VP_COLOR_BLUE:
        ui->lblRecPlateColor->setPixmap(*p_bluePlateColor);
        ui->lblRecPlate->setPalette(paWhite);break;
    case VP_COLOR_YELLOW:
        ui->lblRecPlateColor->setPixmap(*p_yellowPlateColor);
        ui->lblRecPlate->setPalette(paBlack);break;
    case VP_COLOR_BLACK:
        ui->lblRecPlateColor->setPixmap(*p_blackPlateColor);
        ui->lblRecPlate->setPalette(paWhite);break;
    case VP_COLOR_WHITE:
        ui->lblRecPlateColor->setPixmap(*p_whitePlateColor);
        ui->lblRecPlate->setPalette(paBlack);break;
    default:
        ui->lblRecPlateColor->clear();
        break;
    }
    CheckVehInfo();
    return;
}

void VehInfoPanel::CheckVehInfo()
{
    if(m_curVehInfo.VehClass >0 && m_curVehInfo.AutoVehClass>0)
    {
        if(m_curVehInfo.VehClass != m_curVehInfo.AutoVehClass)
        {
            ShowWarning();
            return;
        }
    }

    if(m_curVehInfo.nVehPlateColor != VP_COLOR_NONE && m_curVehInfo.nAutoVehPlateColor !=VP_COLOR_NONE)
    {
        if(m_curVehInfo.nVehPlateColor != m_curVehInfo.nAutoVehPlateColor)
        {
            ShowWarning();
            return;
        }
    }

    if(qstrlen(m_curVehInfo.szAutoVehPlate)>0 && qstrlen(m_curVehInfo.szVehPlate)>0)
    {
        if(0!=strcmp(m_curVehInfo.szAutoVehPlate,m_curVehInfo.szVehPlate))
        {
            ShowWarning();
            return ;
        }
    }
    return;
}

void VehInfoPanel::ShowWarning(QString warningText)
{
    if(warningText.isEmpty())
    {
        m_sWarningText = QString("车型车牌不符");
    }
    else{
        m_sWarningText = warningText;
    }
    ui->lblWarning->setText(m_sWarningText);
    m_nWarningFlag = 0;
    connect(&m_tWarning,SIGNAL(timeout()),this,SLOT(ontWarningTimeout()));
    ontWarningTimeout();
    m_tWarning.start(1000);
}

void VehInfoPanel::HideWarning()
{
    ui->lblWarning->hide();
    m_tWarning.stop();
    disconnect(&m_tWarning,SIGNAL(timeout()),this,SLOT(ontWarningTimeout()));
}


void VehInfoPanel::ontWarningTimeout()
{
    if(m_nWarningFlag % 2 == 1)
    {
        ui->lblWarning->show();
    }
    else
    {
        ui->lblWarning->hide();
    }
    if(8 < m_nWarningFlag)
    {
        HideWarning();
    }
    m_nWarningFlag++;
}


void VehInfoPanel::ShowAutoVehClass(CVehClass vehClass)
{
    m_curVehInfo.AutoVehClass = vehClass;
    ShowAutoVehInfo(m_curVehInfo);
}
