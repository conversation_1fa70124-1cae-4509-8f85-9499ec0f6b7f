#include "forminputamount.h"
#include <QPainter>
#include <QKeyEvent>
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "vehplatefunc.h"
#include "../../common/lanetype.h"

FormInputAmount::FormInputAmount(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(0)
    , m_pLblVehInfo(0)
    , m_pLblVehTypeLabel(0)
    , m_pLblVehTypeInfo(0)
    , m_pLblAmountLabel(0)
    , m_pEditAmount(0)
    , m_pLblAmountUnit(0)
    , m_pLblMaxAmountTip(0)
    , m_pLblHelpInfo(0)
    , m_inputAmount(0)
    , m_vehPlateColor(1)
    , m_vehType(1)
    , m_maxAmount(0)
{
    CreateControls();
    SetupControlProperties();
    InitConnections();
    
    filterChildrenKeyEvent();
    setObjectName("FormInputAmount");
}

FormInputAmount::~FormInputAmount()
{
    // Qt会自动清理子控件
}

void FormInputAmount::CreateControls()
{
    // 创建界面控件
    m_pLblTitle = new QLabel("补费金额", this);
    m_pLblVehInfo = new QLabel(this);
    m_pLblVehTypeLabel = new QLabel("车型", this);
    m_pLblVehTypeInfo = new QLabel(this);
    m_pLblAmountLabel = new QLabel("补费金额:", this);
    m_pEditAmount = new QLineEdit(this);
    m_pLblAmountUnit = new QLabel("元", this);
    m_pLblMaxAmountTip = new QLabel(this);
    m_pLblHelpInfo = new QLabel(this);
}

void FormInputAmount::SetupControlProperties()
{
    // 设置字体
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    m_fontLabel = QFont(g_GlobalUI.m_FontName, 16);
    m_fontEdit = QFont(g_GlobalUI.m_FontName, 18);
    m_fontHelp = QFont(g_GlobalUI.m_FontName, 12);
    
    // 设置背景色
    m_colorBackground = g_GlobalUI.m_ColorBackground;
    
    // 标题
    m_pLblTitle->setFont(m_fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    
    // 车牌信息
    m_pLblVehInfo->setFont(m_fontLabel);
    m_pLblVehInfo->setAlignment(Qt::AlignCenter);
    
    // 车型标签
    m_pLblVehTypeLabel->setFont(m_fontLabel);
    m_pLblVehTypeLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    
    // 车型信息
    m_pLblVehTypeInfo->setFont(m_fontLabel);
    m_pLblVehTypeInfo->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    
    // 金额标签
    m_pLblAmountLabel->setFont(m_fontLabel);
    m_pLblAmountLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    
    // 金额输入框
    m_pEditAmount->setFont(m_fontEdit);
    m_pEditAmount->setAlignment(Qt::AlignRight);
    m_pEditAmount->setPlaceholderText("0.00");
    
    // 金额单位
    m_pLblAmountUnit->setFont(m_fontLabel);
    m_pLblAmountUnit->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    
    // 最大金额提示
    m_pLblMaxAmountTip->setFont(m_fontHelp);
    m_pLblMaxAmountTip->setAlignment(Qt::AlignCenter);
    m_pLblMaxAmountTip->setStyleSheet("color: rgb(100, 100, 100);");
    
    // 帮助信息
    m_pLblHelpInfo->setFont(m_fontHelp);
    m_pLblHelpInfo->setAlignment(Qt::AlignCenter);
    m_pLblHelpInfo->setWordWrap(false);
    m_pLblHelpInfo->setText("【当趟补费】      按【确认】完成，按【ESC】退出");
}

void FormInputAmount::InitLayout()
{
    int width = rect().width();
    int height = rect().height();
    
    // 表格布局参数
    int tableMargin = 50;  // 表格边距
    int tableWidth = width - 2 * tableMargin;
    int tableX = tableMargin;
    int rowHeight = 50;  // 增加行高
    int labelWidth = 120;
    int contentWidth = tableWidth - labelWidth;
    
    // 标题（居中显示，放大尺寸，去掉边框）- 红1区域
    int titleY = 30;
    int titleWidth = 200;  // 增加宽度
    int titleHeight = 50;  // 增加高度
    int titleX = (width - titleWidth) / 2;
    m_pLblTitle->setGeometry(titleX, titleY, titleWidth, titleHeight);
    m_pLblTitle->setStyleSheet("text-align: center; padding: 5px; font-size: 26px;");  // 去掉边框，增加字体大小
    
    // 表格起始位置
    int tableY = titleY + titleHeight + 20;
    
    // 第一行：车牌信息
    int row1Y = tableY;
    QLabel *lblVehPlateLabel = new QLabel("车牌", this);
    lblVehPlateLabel->setGeometry(tableX, row1Y, labelWidth, rowHeight);
    lblVehPlateLabel->setFont(m_fontLabel);
    lblVehPlateLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    lblVehPlateLabel->setStyleSheet("font-size: 20px; padding: 5px;");  // 去掉边框
    
    m_pLblVehInfo->setGeometry(tableX + labelWidth + 20, row1Y, contentWidth - 20, rowHeight);
    m_pLblVehInfo->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblVehInfo->setStyleSheet("font-size: 20px; padding: 5px;");  // 去掉边框
    
    // 第二行：车型信息（分为标签和内容两部分）
    int row2Y = row1Y + rowHeight + 10;
    m_pLblVehTypeLabel->setGeometry(tableX, row2Y, labelWidth, rowHeight);
    m_pLblVehTypeLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblVehTypeLabel->setStyleSheet("font-size: 20px; padding: 5px;");  // 去掉边框
    
    m_pLblVehTypeInfo->setGeometry(tableX + labelWidth + 20, row2Y, contentWidth - 20, rowHeight);
    m_pLblVehTypeInfo->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblVehTypeInfo->setStyleSheet("font-size: 20px; padding: 5px;");  // 去掉边框
    
    // 第三行：最大收费金额
    int row3Y = row2Y + rowHeight + 10;
    m_pLblMaxAmountTip->setGeometry(tableX, row3Y, tableWidth, rowHeight);
    m_pLblMaxAmountTip->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblMaxAmountTip->setStyleSheet("font-size: 20px; padding: 5px;");  // 去掉边框
    
    // 第四行：补费金额
    int row4Y = row3Y + rowHeight + 10;
    m_pLblAmountLabel->setGeometry(tableX, row4Y, labelWidth, rowHeight);
    m_pLblAmountLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblAmountLabel->setStyleSheet("font-size: 20px; padding: 5px;");  // 去掉边框
    
    // 金额输入区域（输入框+单位）
    int inputWidth = contentWidth - 70;
    int unitWidth = 50;
    m_pEditAmount->setGeometry(tableX + labelWidth + 20, row4Y, inputWidth, rowHeight);
    m_pEditAmount->setAlignment(Qt::AlignCenter);
    m_pEditAmount->setStyleSheet("border: 1px solid gray; font-size: 16px; padding: 5px;");  // 简化边框样式
    
    m_pLblAmountUnit->setGeometry(tableX + labelWidth + 20 + inputWidth + 5, row4Y, unitWidth, rowHeight);
    m_pLblAmountUnit->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_pLblAmountUnit->setStyleSheet("font-size: 20px; padding: 5px;");  // 去掉边框
    
    // 底部操作提示
    int helpHeight = 40;
    int helpY = height - helpHeight - 20;  // 移动到窗口底部
    m_pLblHelpInfo->setGeometry(tableX, helpY, tableWidth, helpHeight);
    m_pLblHelpInfo->setAlignment(Qt::AlignCenter);
    //m_pLblHelpInfo->setStyleSheet("background-color: rgb(250, 250, 250); padding: 5px;");  // 去掉边框
}

void FormInputAmount::InitConnections()
{
    connect(m_pEditAmount, SIGNAL(textChanged(QString)), this, SLOT(OnAmountChanged()));
}

bool FormInputAmount::InputAmount(int currentAmount, const QString &vehPlate, int vehPlateColor, int vehType)
{
    // 保存参数
    m_inputAmount = currentAmount;
    m_vehPlate = vehPlate;
    m_vehPlateColor = vehPlateColor;
    m_vehType = vehType;
    
    // 获取车型最大收费金额
    RepayConfig *pConfig = RepayConfig::GetInstance();
    if (pConfig) {
        m_maxAmount = pConfig->GetMaxFee(m_vehType);
    } else {
        m_maxAmount = 0;
    }
    
    // 初始化界面
    InitUI();
    InitLayout();
    
    // 设置车牌信息显示（只显示车牌号码）
    QString colorName = GetVehPlateColorName(vehPlateColor);
    m_pLblVehInfo->setText(QString("%1").arg(vehPlate));
    
    // 设置车型信息显示（只显示车型内容如"货一"）
    QString vehClassName = GetVehClassName(vehType);
    m_pLblVehTypeInfo->setText(vehClassName);
    
    InfoLog(QString("金额输入界面 - 车型信息设置完成：车型编号=%1, 车型名称=%2").arg(vehType).arg(vehClassName));
    
    // 设置最大金额提示（格式：最大收费金额: xxx.xx元）
    if (m_maxAmount > 0) {
        double maxAmountYuan = m_maxAmount / 100.0;
        m_pLblMaxAmountTip->setText(QString("最大收费金额: %1元").arg(maxAmountYuan, 0, 'f', 2));
    } else {
        m_pLblMaxAmountTip->setText("最大收费金额: 未设置");
    }
    
    // 设置当前金额
    if (currentAmount > 0) {
        m_inputText = QString::number(currentAmount / 100.0, 'f', 2);
        m_pEditAmount->setText(m_inputText);
    } else {
        m_inputText.clear();
        m_pEditAmount->clear();
    }
    
    // 设置焦点
    m_pEditAmount->setFocus();
    
    // 显示模态对话框
    return (CBaseOpWidget::Rlt_OK == doModalShow());
}

void FormInputAmount::InitUI()
{
    CBaseOpWidget::InitUI();
}

int FormInputAmount::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) return 0;

    // 处理数字键
    if (mtcKeyEvent->isNumKey()) {
        mtcKeyEvent->setKeyType(KC_Number);
        OnInputNumber(mtcKeyEvent->key());
        return 1;
    }

    // 处理功能键
    mtcKeyEvent->setKeyType(KC_Func);
    if (mtcKeyEvent->func() == KeyConfirm) {
        OnConfirmClicked();
    } else if (mtcKeyEvent->func() == KeyEsc) {
        OnCancelClicked();
    } else if (mtcKeyEvent->func() == KeyDel) {
        OnDeleteInput();
    }

    return 1;
}

void FormInputAmount::OnInputNumber(int keyInput)
{
    // 添加数字到输入文本
    char ch = static_cast<char>(keyInput);
    if (ch >= '0' && ch <= '9') {
        m_inputText += ch;
        m_pEditAmount->setText(m_inputText);
    }
}

void FormInputAmount::OnDeleteInput()
{
    // 删除最后一个字符
    if (!m_inputText.isEmpty()) {
        m_inputText.chop(1);
        m_pEditAmount->setText(m_inputText);
    }
}

bool FormInputAmount::ValidateAmount(QString &errorMsg)
{
    if (m_inputText.isEmpty()) {
        errorMsg = "请输入补费金额";
        return false;
    }
    
    bool ok;
    double amount = m_inputText.toDouble(&ok);
    if (!ok || amount <= 0) {
        errorMsg = "请输入有效的金额";
        return false;
    }
    
    if (amount > 9999.99) {
        errorMsg = "金额不能超过9999.99元";
        return false;
    }
    
    // 检查是否超过车型最大限额
    if (m_maxAmount > 0) {
        double maxAmountYuan = m_maxAmount / 100.0;
        if (amount > maxAmountYuan) {
            errorMsg = QString("金额不能超过当前车型最大限额%.2f元").arg(maxAmountYuan);
            return false;
        }
    }
    
    return true;
}

void FormInputAmount::OnAmountChanged()
{
    // 实时验证输入
    QString text = m_pEditAmount->text();
    bool ok;
    double amount = text.toDouble(&ok);
    
    if (ok && amount >= 0) {
        m_inputAmount = static_cast<int>(amount * 100); // 转换为分
        m_pEditAmount->setStyleSheet("");
    } else {
        m_pEditAmount->setStyleSheet("border: 1px solid red;");
    }
}

void FormInputAmount::OnConfirmClicked()
{
    QString errorMsg;
    if (ValidateAmount(errorMsg)) {
        // 保存最终金额
        bool ok;
        double amount = m_inputText.toDouble(&ok);
        if (ok) {
            m_inputAmount = static_cast<int>(amount * 100);
            OnOk();
        }
    } else {
        ShowErrorMsg(errorMsg);
    }
}

void FormInputAmount::OnCancelClicked()
{
    OnCancel();
}

void FormInputAmount::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);

    // 绘制背景
    painter.setBrush(m_colorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}
