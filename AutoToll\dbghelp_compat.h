/**
 * @brief dbghelp库兼容头文件
 * @details 针对QT4.8.5与Windows 10 SDK兼容性问题提供的简化版本
 */
#ifndef DBGHELP_COMPAT_H
#define DBGHELP_COMPAT_H

#include <windows.h>

#ifdef __cplusplus
extern "C" {
#endif

// 崩溃转储类型定义
typedef enum _MINIDUMP_TYPE {
    MiniDumpNormal = 0x00000000,
    MiniDumpWithFullMemory = 0x00000002,
    MiniDumpWithFullMemoryInfo = 0x00000800,
    MiniDumpWithHandleData = 0x00000004,
    MiniDumpWithThreadInfo = 0x00000100,
    MiniDumpWithUnloadedModules = 0x00000020
} MINIDUMP_TYPE;

// 异常信息结构体
typedef struct _MINIDUMP_EXCEPTION_INFORMATION {
    DWORD ThreadId;
    PEXCEPTION_POINTERS ExceptionPointers;
    BOOL ClientPointers;
} MINIDUMP_EXCEPTION_INFORMATION, *PMINIDUMP_EXCEPTION_INFORMATION;

// 崩溃转储函数声明
BOOL WINAPI MiniDumpWriteDump(
    HANDLE hProcess,
    DWORD ProcessId,
    HANDLE hFile,
    MINIDUMP_TYPE DumpType,
    PMINIDUMP_EXCEPTION_INFORMATION ExceptionParam,
    PVOID UserStreamParam,
    PVOID CallbackParam
);

// 堆栈帧地址模式
typedef enum {
    AddrModeFlat = 3
} ADDRESS_MODE;

// 堆栈帧地址结构
typedef struct _ADDRESS64 {
    DWORD64 Offset;
    WORD Segment;
    ADDRESS_MODE Mode;
} ADDRESS64, *LPADDRESS64;

// KDHELP64结构体（简化版）
typedef struct _KDHELP64 {
    DWORD64 Thread;
    DWORD ThCallbackStack;
    DWORD ThCallbackBStore;
    DWORD NextCallback;
    DWORD FramePointer;
    DWORD64 KiCallUserMode;
    DWORD64 KeUserCallbackDispatcher;
    DWORD64 SystemRangeStart;
    DWORD64 KiUserExceptionDispatcher;
    DWORD64 StackBase;
    DWORD64 StackLimit;
    DWORD64 Reserved[5];
} KDHELP64, *PKDHELP64;

// 堆栈帧结构
typedef struct _STACKFRAME64 {
    ADDRESS64 AddrPC;
    ADDRESS64 AddrReturn;
    ADDRESS64 AddrFrame;
    ADDRESS64 AddrStack;
    ADDRESS64 AddrBStore;
    PVOID FuncTableEntry;
    DWORD64 Params[4];
    BOOL Far;
    BOOL Virtual;
    DWORD64 Reserved[3];
    KDHELP64 KdHelp;
} STACKFRAME64, *LPSTACKFRAME64;

// 符号信息结构
typedef struct _SYMBOL_INFO {
    ULONG SizeOfStruct;
    ULONG TypeIndex;
    ULONG64 Reserved[2];
    ULONG Index;
    ULONG Size;
    ULONG64 ModBase;
    ULONG Flags;
    ULONG64 Value;
    ULONG64 Address;
    ULONG Register;
    ULONG Scope;
    ULONG Tag;
    ULONG NameLen;
    ULONG MaxNameLen;
    CHAR Name[1];
} SYMBOL_INFO, *PSYMBOL_INFO;

// 行信息结构
typedef struct _IMAGEHLP_LINE64 {
    DWORD SizeOfStruct;
    PVOID Key;
    DWORD LineNumber;
    PCHAR FileName;
    DWORD64 Address;
} IMAGEHLP_LINE64, *PIMAGEHLP_LINE64;

// 符号选项
#define SYMOPT_CASE_INSENSITIVE         0x00000001
#define SYMOPT_UNDNAME                  0x00000002
#define SYMOPT_DEFERRED_LOADS           0x00000004
#define SYMOPT_NO_CPP                   0x00000008
#define SYMOPT_LOAD_LINES               0x00000010
#define SYMOPT_OMAP_FIND_NEAREST        0x00000020

// 机器类型
#define IMAGE_FILE_MACHINE_I386         0x014c
#define IMAGE_FILE_MACHINE_AMD64        0x8664

// 自定义函数指针类型
typedef BOOL (WINAPI *PREAD_PROCESS_MEMORY_ROUTINE64)(
    HANDLE hProcess,
    DWORD64 lpBaseAddress,
    PVOID lpBuffer,
    DWORD nSize,
    LPDWORD lpNumberOfBytesRead
);

typedef PVOID (WINAPI *PFUNCTION_TABLE_ACCESS_ROUTINE64)(
    HANDLE hProcess,
    DWORD64 AddrBase
);

typedef DWORD64 (WINAPI *PGET_MODULE_BASE_ROUTINE64)(
    HANDLE hProcess,
    DWORD64 Address
);

typedef DWORD64 (WINAPI *PTRANSLATE_ADDRESS_ROUTINE64)(
    HANDLE hProcess,
    HANDLE hThread,
    LPADDRESS64 lpaddr
);

// 符号处理函数
BOOL WINAPI StackWalk64(
    DWORD MachineType,
    HANDLE hProcess,
    HANDLE hThread,
    LPSTACKFRAME64 StackFrame,
    PVOID ContextRecord,
    PREAD_PROCESS_MEMORY_ROUTINE64 ReadMemoryRoutine,
    PFUNCTION_TABLE_ACCESS_ROUTINE64 FunctionTableAccessRoutine,
    PGET_MODULE_BASE_ROUTINE64 GetModuleBaseRoutine,
    PTRANSLATE_ADDRESS_ROUTINE64 TranslateAddressRoutine
);

PVOID WINAPI SymFunctionTableAccess64(
    HANDLE hProcess,
    DWORD64 AddrBase
);

DWORD64 WINAPI SymGetModuleBase64(
    HANDLE hProcess,
    DWORD64 dwAddr
);

BOOL WINAPI SymFromAddr(
    HANDLE hProcess,
    DWORD64 Address,
    PDWORD64 Displacement,
    PSYMBOL_INFO Symbol
);

BOOL WINAPI SymGetLineFromAddr64(
    HANDLE hProcess,
    DWORD64 dwAddr,
    PDWORD pdwDisplacement,
    PIMAGEHLP_LINE64 Line
);

DWORD WINAPI SymSetOptions(
    DWORD SymOptions
);

BOOL WINAPI SymInitialize(
    HANDLE hProcess,
    PCSTR UserSearchPath,
    BOOL fInvadeProcess
);

BOOL WINAPI SymCleanup(
    HANDLE hProcess
);

// 加载模块符号函数
DWORD64 WINAPI SymLoadModuleEx(
    HANDLE hProcess,
    HANDLE hFile,
    PCSTR ImageName,
    PCSTR ModuleName,
    DWORD64 BaseOfDll,
    DWORD SizeOfDll,
    PVOID Data,
    DWORD Flags
);

// 符号选项附加值
#define SYMOPT_FAIL_CRITICAL_ERRORS    0x00000200
#define SYMOPT_NO_PROMPTS             0x00080000

#ifdef __cplusplus
}
#endif

#endif // DBGHELP_COMPAT_H 