#include "unlogindlg.h"
#include "lanectrl.h"


CUnLoginDlg::CUnLoginDlg(QWidget *parent) :
    CBaseOpWidget(parent)
{
    InitUI();
}

CUnLoginDlg::~CUnLoginDlg()
{

}

void CUnLoginDlg::InitUI()
{
    CBaseOpWidget::InitUI();


    QFont ftText;
    ftText.setFamily(QString::fromUtf8("微软雅黑"));
    ftText.setPixelSize(33);
    ftText.setStyleStrategy(QFont::PreferAntialias);

    m_lblTitle = new QLabel(this);
    m_lblTitle->setGeometry(150,30, 200, 60);
    m_lblTitle->setAlignment(Qt::AlignCenter);
    m_lblTitle->setFont(ftText);
    m_lblTitle->setText("下班确认");


    ftText.setPixelSize(25);
    m_labelMsg = new QLabel(this);
    m_labelMsg->setGeometry(150,120, 200, 60);
    m_labelMsg->setAlignment(Qt::AlignCenter);
    m_labelMsg->setFont(ftText);
    m_labelMsg->setText("请确认是否下班?");

    ftText.setPixelSize(33);
    m_labelOK = new QLabel(this);
    m_labelOK->setGeometry(80,250, 200, 60);
    m_labelOK->setAlignment(Qt::AlignCenter);
    m_labelOK->setFont(ftText);
    m_labelOK->setText("【确定】");

    m_labelCancel = new QLabel(this);
    m_labelCancel->setGeometry(260,250, 200, 60);
    m_labelCancel->setAlignment(Qt::AlignCenter);
    m_labelCancel->setFont(ftText);
    m_labelCancel->setText("【取消】");
}

int CUnLoginDlg::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if(mtcKeyEvent->func()==KeyConfirm) {  // 下班，改变状态
        setModalResult(1);
        Ptr_Ctrl->UnLogin();
        CLaneCtrl::GetLaneCtrl()->ChangeToUnLoginState();
    }
    if(mtcKeyEvent->func() == KeyEsc) {
        setModalResult(1);
    }
}
