#ifndef CSOUNDPLAYER_H
#define CSOUNDPLAYER_H
#include <QDataStream>

/*******************************
 * QT 播放车道声音处理
 * 声音文件放置到应用目录下的wav子目录中。
 * 在进行报价语音播放时，采用将多个wav文件合并成一个文件的处理方式。
 * 试过在QT下用播放列表处理，但两个文件之间的播放间隙时间不可控，效果不好。
 * ****************************/
#include <QSound>

class CSoundPlayer
{
public:
    CSoundPlayer();
private:
    //由文件输入流中复制字节到输出文件， 返回实际写入字节
    static int CopyWriteToFile(QDataStream &in, QDataStream &out, int nDatasize);
    //将多个wav文件合并成一个wav文件
    static bool MakeWavFile(const QStringList &lstWaveFile, const QString &sOutFile);
public:
    //播放声音文件
    static bool Play_SndFile(QString sFileName);
    //播放收费金额(单位：元）
    static bool Play_TollMoney(int nMoney);
};

#endif // CSOUNDPLAYER_H
