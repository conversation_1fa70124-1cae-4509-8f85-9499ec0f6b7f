#ifndef SYSPARAMDLG_H
#define SYSPARAMDLG_H

#include "baseopwidget.h"
#include "common/QListWrapper.h"
#include "common/paramfilemgr.h"

#define MAX_PARAM_NUM  8
class CSysParamDlg : public CBaseOpWidget {
    Q_OBJECT

public:
    explicit CSysParamDlg(QWidget *parent = 0);
    ~CSysParamDlg();
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    void closeEvent(QCloseEvent *event);
private:
    QTableWidget *m_ptblWidget;
    QListWrapper *m_pListWrapper;
    QList<QLabel*> m_pListLabel;

private:
    void Init();
    void SetMove(int key);
};


#endif // SYSPARAMDLG_H
