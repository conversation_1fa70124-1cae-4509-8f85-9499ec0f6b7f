#include "forminsertcard.h"

#include "globalutils.h"
FormInsertCard::FormInsertCard(QWidget *parent) : BaseForm(parent) {}

void FormInsertCard::paintEvent(QPaintEvent *)
{
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    OnP<PERSON>ter(painter);
    //绘制圆形框
    painter.setBrush(QColor(44, 54, 67));
    painter.setPen(Qt::NoPen);
    painter.drawEllipse(g_GlobalUI.incard_bigRoundRect);
    painter.drawEllipse(g_GlobalUI.incard_smallRoundRect);
    //绘制圆形图片
    QRect rectImage = g_GlobalUI.incard_bigRoundRect;
    int nMargin = 0;
    rectImage.adjust(nMargin, nMargin, -nMargin, -nMargin);
    QPixmap image(GetCurrentPath() + "/images/insertcard.jpg");
    QPixmap roundImage = generatePixmap(image, rectImage.width() / 2);
    painter.drawPixmap(rectImage, roundImage);

    rectImage = g_GlobalUI.incard_smallRoundRect;
    nMargin = 0;
    rectImage.adjust(nMargin, nMargin, -nMargin, -nMargin);
    image.load(GetCurrentPath() + "/images/etccard.jpg");
    roundImage = generatePixmap(image, rectImage.width() / 2);
    painter.drawPixmap(rectImage, roundImage);

    //绘制文字

    QFont bigFont(g_GlobalUI.m_FontName, g_GlobalUI.incard_BigFontSize, QFont::Bold);
    bigFont.setLetterSpacing(QFont::AbsoluteSpacing, g_GlobalUI.incard_BigFontSpace);
    QColor clrText(240, 175, 1);
    painter.setFont(bigFont);
    painter.setPen(clrText);
    painter.drawText(g_GlobalUI.incard_bigTextRect, Qt::AlignVCenter | Qt::AlignLeft, QString("请插入通行卡"));

    QFont smallFont(g_GlobalUI.m_FontName, g_GlobalUI.incard_SmallFontSize, QFont::Bold);
    smallFont.setLetterSpacing(QFont::AbsoluteSpacing, g_GlobalUI.incard_SmallFontSpace);
    painter.setFont(smallFont);
    painter.drawText(g_GlobalUI.incard_smallTextRect, Qt::AlignVCenter | Qt::AlignLeft, QString("ETC车辆请刷卡"));

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

void FormInsertCard::ShowForm()
{
    BaseForm::ShowForm();
    PlaySound("insertcard.wav", 10, 5);
}
