#ifndef REPAYMANAGER_H
#define REPAYMANAGER_H

#include <QObject>
#include <QMutex>
#include "../common/repaytypes.h"
#include "../../transinfo.h"
#include "../../common/lanetype.h"
#include "prodebpayment.h"
#include "authmanager.h"
#include "../config/repayconfig.h"
#include "../ui/forminterceptselect.h"

/**
 * @brief 补费管理器
 * 负责处理补费的核心业务逻辑
 * 包含当趟补费和省内名单补费两种模式
 */
class RepayManager : public QObject
{
    Q_OBJECT
    
public:
    static RepayManager* GetInstance();
    
    // 初始化补费管理器
    bool Initialize();
    
    // 开始补费流程
    bool StartRepay(RepayType type, const CVehInfo &vehInfo);
    
    // 当趟补费流程
    bool ProcessCurrentRepay(const QString &vehPlate, int vehPlateColor, 
                           int vehType, int amount);
    
    // 省内名单补费流程  
    bool ProcessProvinceRepay(const QString &vehPlate, int vehPlateColor,
                                                         const RepayDebtQueryResult &debtResult);
    
    // 验证补费金额
    bool ValidateRepayAmount(int vehType, int amount);
    
    // 查询补费明细
    bool QueryDebtDetail(const QString &vehPlate, int vehPlateColor,
                                                  RepayDebtQueryResult &result);
    
    // 处理支付
    bool ProcessPayment(CTransPayType payType, int amount, 
                       const QString &vehPlate, int vehPlateColor);
    
    // 生成补费流水
    bool GenerateRepayRecord(const CTransInfo &transInfo, RepayType repayType,
                           const QString &orderIds = "");
    
    // 构造交易信息
    void FillTransInfo(CTransInfo &transInfo, const QString &vehPlate, int vehPlateColor, 
                      int vehType, int amount, RepayType repayType, CTransPayType payType);
    
    // 通知省中心完成
    bool NotifyProvinceComplete(const QString &vehPlate, int vehPlateColor,
                              const QString &oweFee, const QString &wasteId,
                              const QString &listno);
    
    // 获取当前补费状态
    RepayStage GetCurrentStage() const { return m_currentStage; }
    RepayType GetCurrentRepayType() const { return m_currentRepayType; }
    
    // 获取当前车辆信息
    QString GetCurrentVehPlate() const { return m_currentVehPlate; }
    int GetCurrentVehPlateColor() const { return m_currentVehPlateColor; }
    
    // 获取当前支付方式
    CTransPayType GetCurrentPayType() const { return m_currentPayType; }
    
    // 取消当前补费流程
    void CancelRepay();
    
    // 设置当前补费信息
    void SetCurrentRepayInfo(const QString &vehPlate, int vehPlateColor, 
                           int vehType, int amount = 0);
    
    // 验证车辆信息
    bool ValidateVehicleInfo(const QString &vehPlate, int vehPlateColor);
    
    // 检查是否需要授权
    bool IsAuthorizationRequired() const;
    
    // 设置补费阶段
    void SetRepayStage(RepayStage stage);
    
    // 获取支付方式列表
    QList<CTransPayType> GetAvailablePaymentTypes() const;

    // 预设拦截方式（在进入流程前由外层界面先行选择）
    void SetPreselectedInterceptType(InterceptType type) { m_currentInterceptType = type; }
    bool HasPreselectedInterceptType() const { return (int)m_currentInterceptType != 0; }

signals:
    // 补费流程开始
    void RepayStarted(RepayType type);
    
    // 补费流程完成
    void RepayCompleted(bool success, const QString &message);
    
    // 补费阶段变更
    void RepayStageChanged(RepayStage stage);
    
    // 支付处理中
    void PaymentProcessing();
    
    // 支付完成
    void PaymentCompleted(bool success, const QString &message);
    
    // 欠费查询完成
    void DebtQueryCompleted(bool success, const RepayDebtQueryResult &result);
    
    // 错误信息
    void ErrorOccurred(RepayErrorCode errorCode, const QString &message);

private slots:
    // 响应授权完成
    void OnAuthorizationCompleted(bool success);
    
    // 响应欠费查询完成
    void OnDebtQueryFinished(bool success, const RepayDebtQueryResult &result);
    
    // 响应省中心通知完成
    void OnNotifyCompleteFinished(bool success, const QString &result);

private:
    RepayManager(QObject *parent = 0);
    
    // 验证补费前提条件
    bool ValidateRepayConditions(RepayType type, const QString &vehPlate, int vehPlateColor);
    
    // 检查补费金额限制
    bool CheckAmountLimit(int vehType, int amount);
    
    // 生成流水ID
    QString GenerateWasteId();
    
    // 记录补费操作
    void LogRepayOperation(RepayType type, const QString &vehPlate, int vehPlateColor,
                          int amount, CTransPayType payType, bool success, 
                          const QString &errorMsg = "", const QString &orderIds = "",
                          const QString &listno = "", const QString &wasteId = "");
    
    // 清理当前补费信息
    void ClearCurrentRepayInfo();
    
    // 处理支付成功
    void HandlePaymentSuccess(CTransPayType payType, int amount);
    
    // 处理支付失败
    void HandlePaymentFailure(const QString &errorMsg);
    
    // 增强错误信息处理
    QString GetEnhancedErrorMessage(const QString &originalError);
    
    // 验证省内名单补费结果
    bool ValidateProvinceRepayResult(const RepayDebtQueryResult &result);
    
    // 现金支付处理
    bool ProcessCashPayment(int amount, const QString &vehPlate, int vehPlateColor, QString &errorMessage);
    
    // 移动支付处理（支付宝/微信/银联卡）
    bool ProcessMobilePayment(CTransPayType payType, int amount, const QString &vehPlate, 
                             int vehPlateColor, QString &errorMessage);
    
    // ETC卡支付处理
    bool ProcessETCPayment(int amount, const QString &vehPlate, int vehPlateColor, QString &errorMessage);
    
    // 现金支付辅助方法
    QString GenerateElectronicInvoice(int amount, const QString &vehPlate, int vehPlateColor);
    void RecordCashCollection(int amount, const QString &vehPlate, int vehPlateColor, const QString &invoiceNo);
    
    // 移动支付辅助方法
    bool CheckNetworkConnection();
    bool ProcessAlipayPayment(int amount, const QString &vehPlate, QString &orderId, QString &errorMessage);
    bool ProcessWeChatPayment(int amount, const QString &vehPlate, QString &orderId, QString &errorMessage);
    bool ProcessUnionPayment(int amount, const QString &vehPlate, QString &orderId, QString &errorMessage);
    void RecordMobilePaymentOrder(CTransPayType payType, int amount, const QString &vehPlate, 
                                 int vehPlateColor, const QString &orderId);
    
    // ETC卡支付辅助方法
    bool CheckETCCardReaderStatus();
    bool WaitForETCCardInsert();
    bool ReadETCCardInfo(CProCardBasicInfo &cardInfo, quint32 &dwBalance, QString &errorMessage);
    bool ValidateETCCardStatus(const CProCardBasicInfo &cardInfo, quint32 dwBalance, QString &errorMessage);
    bool ExecuteETCDeduction(const CProCardBasicInfo &cardInfo, quint32 dwBalance, int amount, QString &transactionId, QString &errorMessage);
    void RecordETCTransaction(int amount, const QString &vehPlate, int vehPlateColor, 
                             const CProCardBasicInfo &cardInfo, const QString &transactionId);

private:
    static RepayManager *m_pInstance;
    static QMutex m_mutex;
    
    // 当前补费状态
    RepayType m_currentRepayType;
    RepayStage m_currentStage;
    QString m_currentVehPlate;
    int m_currentVehPlateColor;
    int m_currentVehType;
    int m_currentAmount;
    
    // 省内名单补费相关
    RepayDebtQueryResult m_currentDebtResult;
    QString m_currentListno;
    QString m_currentWasteId;
    InterceptType m_currentInterceptType;
    
    // 组件实例
    ProDebPayment *m_pProDebPayment;
    AuthManager *m_pAuthManager;
    RepayConfig *m_pRepayConfig;
    
    // 状态标志
    bool m_bInitialized;
    // 移除 m_bRepayInProgress，补费流程改为无状态设计
    
    // 当前支付信息（用于延时处理）
    CTransPayType m_currentPayType;
    int m_currentPayAmount;
    
    // 互斥锁
    QMutex m_repayMutex;
};

#endif // REPAYMANAGER_H