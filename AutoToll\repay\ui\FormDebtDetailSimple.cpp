#include "FormDebtDetailSimple.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"
#include <QApplication>
#include <QKeyEvent>
#include <QDateTime>

// 静态常量类外定义（为避免旧编译器/ODR使用导致的链接错误）
const int FormDebtDetailSimple::MAX_VISIBLE_ITEMS;

FormDebtDetailSimple::FormDebtDetailSimple(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_nCurrentIndex(0)
    , m_bSelectionConfirmed(false)
    , m_bProcessing(false)
{
    // 初始化界面配置
    InitUIConfig();
    
    InfoLog("创建简化版债务详情界面");
}

FormDebtDetailSimple::~FormDebtDetailSimple()
{
    InfoLog("销毁简化版债务详情界面");
}

void FormDebtDetailSimple::InitUIConfig()
{
    // 设置字体，使用全局配置
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize, QFont::Bold);
    m_fontHeader = QFont(g_GlobalUI.m_FontName, 14, QFont::Bold);
    m_fontItem = QFont(g_GlobalUI.m_FontName, 12);
    m_fontHelp = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
}

void FormDebtDetailSimple::InitUI(int iFlag)
{
    CBaseOpWidget::InitUI(iFlag);
    
    InfoLog("初始化简化版债务详情界面");
    
    // 连接信号
    InitConnections();
    
    // 安装子控件键盘事件过滤器
    filterChildrenKeyEvent();
    
    DebugLog("简化版债务详情界面初始化完成");
}

void FormDebtDetailSimple::InitConnections()
{
    // 暂无需要连接的信号
}

bool FormDebtDetailSimple::ShowDebtDetail(const RepayDebtQueryResult &result)
{
    InfoLog("显示债务详情界面");
    
    // 设置债务数据
    SetDebtResult(result);
    
    // 重置状态
    m_nCurrentIndex = 0;
    m_bSelectionConfirmed = false;
    m_bProcessing = false;
    
    // 检查是否有有效数据
    if (m_debtResult.debtItems.isEmpty()) {
        ShowWarningMessage("未查询到有效的欠费记录");
        return false;
    }
    
    // 更新显示
    UpdateSelectionDisplay();
    
    // 显示界面
    int result_code = doModalShow();
    
    if (result_code == CBaseOpWidget::Rlt_OK && m_bSelectionConfirmed) {
        InfoLog(QString("债务详情选择完成 - 选中项:%1, 总金额:%2分")
                .arg(m_nCurrentIndex)
                .arg(GetTotalRepayAmount()));
        return true;
    } else {
        InfoLog("债务详情选择取消");
        return false;
    }
}

void FormDebtDetailSimple::SetDebtResult(const RepayDebtQueryResult &result)
{
    m_debtResult = result;
    
    // 确保有有效的索引
    if (m_nCurrentIndex >= m_debtResult.debtItems.size()) {
        m_nCurrentIndex = 0;
    }
}

int FormDebtDetailSimple::GetTotalRepayAmount() const
{
    // 返回所有债务项目的总金额
    return m_debtResult.totalAmount;
}

RepayDebtItem FormDebtDetailSimple::GetSelectedItem() const
{
    if (m_nCurrentIndex >= 0 && m_nCurrentIndex < m_debtResult.debtItems.size()) {
        return m_debtResult.debtItems[m_nCurrentIndex];
    }
    return RepayDebtItem();
}

void FormDebtDetailSimple::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    
    // 绘制背景
    DrawBackground(painter);
    
    // 绘制标题
    DrawTitle(painter);
    
    // 绘制车辆信息
    DrawVehicleInfo(painter);
    
    // 绘制债务列表
    DrawDebtList(painter);
    
    // 绘制原因描述
    DrawReason(painter);
    
    // 绘制帮助信息
    DrawHelpMessage(painter);
    
    painter.end();
    
    // 绘制到窗口
    QPainter windowPainter(this);
    windowPainter.drawPixmap(rectClient, pixmap);
}

void FormDebtDetailSimple::DrawBackground(QPainter &painter)
{
    QRect rectClient = this->rect();
    
    // 绘制背景色
    painter.setBrush(g_GlobalUI.m_ColorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);
}

void FormDebtDetailSimple::DrawTitle(QPainter &painter)
{
    // 绘制顶部车牌和金额信息
    painter.setFont(m_fontTitle);
    painter.setPen(Qt::black);
    
    QString vehiclePlate = m_debtResult.vehiclePlate.isEmpty() ? "未知车牌" : m_debtResult.vehiclePlate;
    QString titleText = QString("【当前车辆车牌】 补费总金额：%1元")
                        .arg(FormatAmount(m_debtResult.totalAmount));
    
    QRect titleRect(0, 20, rect().width(), TITLE_HEIGHT);
    painter.drawText(titleRect, Qt::AlignCenter, titleText);
}

void FormDebtDetailSimple::DrawVehicleInfo(QPainter &painter)
{
    // 暂时不需要额外的车辆信息显示
}

void FormDebtDetailSimple::DrawDebtList(QPainter &painter)
{
    if (m_debtResult.debtItems.isEmpty()) {
        return;
    }
    
    int startY = TITLE_HEIGHT + VEHICLE_INFO_HEIGHT + 40;
    
    // 绘制表头
    painter.setFont(m_fontHeader);
    painter.setPen(Qt::black);
    
    // 表头背景
    QRect headerRect(50, startY, rect().width() - 100, HEADER_HEIGHT);
    painter.setBrush(QColor(220, 220, 220));
    painter.drawRect(headerRect);
    
    // 表头文字
    painter.setBrush(Qt::NoBrush);
    int colWidth = headerRect.width() / 3;
    painter.drawText(QRect(headerRect.x(), headerRect.y(), colWidth, HEADER_HEIGHT), 
                    Qt::AlignCenter, "出口收费站");
    painter.drawText(QRect(headerRect.x() + colWidth, headerRect.y(), colWidth, HEADER_HEIGHT), 
                    Qt::AlignCenter, "时间");
    painter.drawText(QRect(headerRect.x() + colWidth * 2, headerRect.y(), colWidth, HEADER_HEIGHT), 
                    Qt::AlignCenter, "补费金额");
    
    // 绘制债务项目
    painter.setFont(m_fontItem);
    
    int itemStartY = startY + HEADER_HEIGHT;
    int maxItems = qMin(FormDebtDetailSimple::MAX_VISIBLE_ITEMS, m_debtResult.debtItems.size());
    
    for (int i = 0; i < maxItems; i++) {
        const RepayDebtItem &item = m_debtResult.debtItems[i];
        
        QRect itemRect(50, itemStartY + i * ITEM_HEIGHT, rect().width() - 100, ITEM_HEIGHT);
        
        // 绘制选中项背景
        if (i == m_nCurrentIndex) {
            painter.setBrush(QColor(0, 150, 255, 100));  // 蓝色半透明背景
            painter.drawRect(itemRect);
        }
        
        painter.setBrush(Qt::NoBrush);
        
        // 选中项使用蓝色文字
        if (i == m_nCurrentIndex) {
            painter.setPen(QColor(0, 120, 215));
        } else {
            painter.setPen(Qt::black);
        }
        
        // 绘制站点名称
        QString stationName = TruncateStationName(item.exitStation);
        painter.drawText(QRect(itemRect.x(), itemRect.y(), colWidth, ITEM_HEIGHT), 
                        Qt::AlignCenter, stationName);
        
        // 绘制时间
        QString dateTime = FormatDateTime(item.debtDate);
        painter.drawText(QRect(itemRect.x() + colWidth, itemRect.y(), colWidth, ITEM_HEIGHT), 
                        Qt::AlignCenter, dateTime);
        
        // 绘制金额
        QString amount = FormatAmount(item.debtAmount);
        painter.drawText(QRect(itemRect.x() + colWidth * 2, itemRect.y(), colWidth, ITEM_HEIGHT), 
                        Qt::AlignCenter, amount);
    }
}

void FormDebtDetailSimple::DrawReason(QPainter &painter)
{
    // 绘制底部原因描述区域
    painter.setFont(m_fontItem);
    painter.setPen(Qt::black);
    
    int reasonY = rect().height() - HELP_HEIGHT - REASON_HEIGHT - 20;
    QRect reasonRect(50, reasonY, rect().width() - 100, REASON_HEIGHT);
    
    // 绘制边框
    painter.setBrush(Qt::NoBrush);
    painter.drawRect(reasonRect);
    
    // 绘制文字 - 说明确认后将使用总金额补费
    QString reasonText = QString("【原因描述】确认后将对所有欠费记录进行补费，总金额：%1元")
                         .arg(FormatAmount(m_debtResult.totalAmount).remove("元"));
    painter.drawText(reasonRect, Qt::AlignCenter, reasonText);
}

void FormDebtDetailSimple::DrawHelpMessage(QPainter &painter)
{
    // 绘制底部帮助信息
    painter.setFont(m_fontHelp);
    painter.setPen(Qt::black);
    
    int helpY = rect().height() - HELP_HEIGHT - 10;
    QRect helpRect(0, helpY, rect().width(), HELP_HEIGHT);
    
    QString helpText = "按【↑】【↓】键查看  按（确认）键继续";
    painter.drawText(helpRect, Qt::AlignCenter, helpText);
}

QString FormDebtDetailSimple::FormatAmount(int amount) const
{
    return QString("%1元").arg(QString::number(amount / 100.0, 'f', 2));
}

QString FormDebtDetailSimple::FormatDateTime(const QString &datetime) const
{
    // 简化日期时间格式
    if (datetime.length() >= 19) {
        return datetime.mid(0, 19);  // 取前19个字符 "2025-03-10 21:25:58"
    }
    return datetime;
}

QString FormDebtDetailSimple::TruncateStationName(const QString &name, int maxLength) const
{
    if (name.length() <= maxLength) {
        return name;
    }
    return name.left(maxLength - 3) + "...";
}

int FormDebtDetailSimple::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent || m_bProcessing) return 0;
    
    int keyCode = mtcKeyEvent->key();
    int keyFunc = mtcKeyEvent->func();
    
    // 优先处理功能键
    if (keyFunc == KeyConfirm) {
        ProcessEnterKey();
        return 1;
    }
    
    if (keyFunc == KeyEsc) {
        ProcessEscapeKey();
        return 1;
    }
    
    // 处理方向键
    if (keyCode == KeyUp) {
        ProcessUpKey();
    } else if (keyCode == KeyDown) {
        ProcessDownKey();
    } else {
        return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
    }
    
    return 1;
}

void FormDebtDetailSimple::ProcessUpKey()
{
    MoveSelectionUp();
    DebugLog(QString("用户上键，当前选择：%1").arg(m_nCurrentIndex));
}

void FormDebtDetailSimple::ProcessDownKey()
{
    MoveSelectionDown();
    DebugLog(QString("用户下键，当前选择：%1").arg(m_nCurrentIndex));
}

void FormDebtDetailSimple::ProcessEnterKey()
{
    ConfirmSelection();
}

void FormDebtDetailSimple::ProcessEscapeKey()
{
    CancelSelection();
}

void FormDebtDetailSimple::MoveSelectionUp()
{
    if (m_debtResult.debtItems.isEmpty()) return;
    
    m_nCurrentIndex--;
    if (m_nCurrentIndex < 0) {
        m_nCurrentIndex = m_debtResult.debtItems.size() - 1;
    }
    
    UpdateSelectionDisplay();
}

void FormDebtDetailSimple::MoveSelectionDown()
{
    if (m_debtResult.debtItems.isEmpty()) return;
    
    m_nCurrentIndex++;
    if (m_nCurrentIndex >= m_debtResult.debtItems.size()) {
        m_nCurrentIndex = 0;
    }
    
    UpdateSelectionDisplay();
}

void FormDebtDetailSimple::ConfirmSelection()
{
    if (m_bProcessing) return;
    
    if (m_debtResult.debtItems.isEmpty()) {
        ShowWarningMessage("没有可选择的欠费记录");
        return;
    }
    
    m_bSelectionConfirmed = true;
    
    InfoLog(QString("确认选择债务项目 - 索引:%1, 总金额:%2分")
            .arg(m_nCurrentIndex)
            .arg(GetTotalRepayAmount()));
    
    OnOk();
}

void FormDebtDetailSimple::CancelSelection()
{
    InfoLog("用户取消债务详情选择");
    OnCancel();
}

void FormDebtDetailSimple::UpdateSelectionDisplay()
{
    // 重绘界面以更新选中状态
    update();
}

void FormDebtDetailSimple::SetUIEnabled(bool enabled)
{
    this->setEnabled(enabled);
}

void FormDebtDetailSimple::OnModalShowed()
{
    CBaseOpWidget::OnModalShowed();
    
    // 设置焦点
    this->setFocus();
    
    DebugLog("简化版债务详情界面显示完成");
}

void FormDebtDetailSimple::ShowWarningMessage(const QString &message)
{
    WarnLog(QString("债务详情界面警告：%1").arg(message));
    // 可以在这里添加界面提示，暂时只记录日志
}
