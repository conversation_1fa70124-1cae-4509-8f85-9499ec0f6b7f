#ifndef CVIPUMGR_H
#define CVIPUMGR_H

#include <QObject>
#include "pausablethread.h"
#include "transinfo.h"
#include <QList>
#include <QMutex>
#include "vcrdev.h"

struct CAutoVehInfo
{
    CAutoRegInfo vprInfo;
    VcrResult vcrResult;
    int nCheckTime;

public:
    CAutoVehInfo()
    {
        vprInfo.ClearResult();
        nCheckTime = 0;
    }
};

class CVIPUMgr : public CPausableThread
{
    Q_OBJECT

public:
    static CVIPUMgr *GetVIPUMgr()
    {
        static CVIPUMgr vipu;
        return &vipu;
    }

    explicit CVIPUMgr(QObject *parent = 0);
    void AddVehInfo(const CAutoRegInfo &regInfo, VcrResult *pVcrResult);

    virtual bool RunOnce();
    virtual void AfterRun();
    bool InitVIPUMgr();
    void FreeVIPUMgr();

signals:

public slots:

protected:
    QMutex m_vehMt;
    QList<CAutoVehInfo> m_vehList;
    QString m_sPicPath;
};

#endif  // CVIPUMGR_H
