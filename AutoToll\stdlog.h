#ifndef CSTDLOG_H
#define CSTDLOG_H

#include <QObject>

#include "cardfiledef.h"
#include "cardfiledef_cpc.h"
#include "laneinfo.h"
#include "log4qt.h"
#include "rsudevtype.h"

class CStdLog : public QObject
{
    Q_OBJECT
public:
    explicit CStdLog(QObject *parent = 0);
    static void StdLogLaneInfo(const QDateTime &workDate);
    static void StdLogAppStart(bool bStart);
    static void StdLogLoginInfo(bool bLogin, const QString &sOperatorName,
                                const QString &sShiftName);
    static void StdLogDevInfo_ProductAndVersion();
    static void StdLogDevInfo_DevStatus(qint32 nDevId, qint32 nStatus);
    static void StdLogDevInfo_LoopStatus();
    static void StdLogDevInfo_Loop(int nDI_LoopId, quint8 bStatus);
    static void StdLogOBUInfo_BaseInfo(quint32 OBUId, const COBUBaseInfo *pOBUBaseInfo);
    static void StdLogVehInfo_OBUVehInfo(const QString &sSubKey, const COBUVehInfo *pOBUVehInfo);
    static void StdLogTollCardInfo_CardTollInfo(const QString &sSubKey,
                                                const CCardTollInfo *pCardTollInfo);
    static void StdLogIccInfo_ProCardBasicInfo(const QString &sSubKey,
                                               const CProCardBasicInfo *pProCardBasicInfo,
                                               quint32 dwBalance);
    static void StdLogConsumeInfo(const QString &sSubKey, quint32 dwLastMoney,
                                  quint32 dwConsumeMoney, quint32 dwFeeMileage, quint32 vehClass,
                                  QString sCardNo);
    static void StdLog_EF04(const QString &sSubKey, const CEF04Info *pEF04Info);
    static void StdLog_CPCBaseInfo(const QString &sSubKey, const CCPCBasicInfo &cpcBasicInfo);
    static void StdLog_CPCStationInfo(const QString &sSubKey, const CCPCRoadInfo_New &cpcRoadInfo);
    static void StdLog_CPCFeeInfo(const QString &sSubKey,
                                  const CCPCTollCellInfo_New &cpcTollCellInfo);
signals:

public slots:
};

#endif  // CSTDLOG_H
