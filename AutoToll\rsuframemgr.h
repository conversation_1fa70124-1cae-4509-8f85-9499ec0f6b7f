#ifndef RSUFRAMEMGR_H
#define RSUFRAMEMGR_H

#include <QByteArray>
#include "common/cardfiledef.h"


#pragma pack(push,1)
struct CFrameHead_GB{
    quint8 STX[2];
    quint8 VER;
    quint8 SEQ;
    quint16 bak;
    quint16 LEN;
};
#pragma pack(pop)

class CRsuFrameMgr
{
public:
    CRsuFrameMgr();
    static CRsuFrameMgr *GetRsuFrameMgr()
    {
        static CRsuFrameMgr FrameMgr;
        return &FrameMgr;
    }

    void AppendRawData(const QByteArray& byteArray);
    bool GetFrameData(QByteArray &byteArray,bool& bBccOk);
    bool GetFrameData_GB(QByteArray &byteArray,bool &bBccOk);

    void ClearRawData(){m_RawData.clear();}

public:
    static bool TransInput(QByteArray& byteArray);
    static bool TransOutput(QByteArray& byteArray);

private:

    static qint32 FindFrameHead(const QByteArray& byteArray);
    static qint32 FindFrameTail(const QByteArray& byteArray,qint32 nHeadPos);

private:
    QByteArray m_RawData;
    qint32 m_nHeadPos;
    qint32 m_nTailPos;
};


#endif // RSUFRAMEMGR_H
