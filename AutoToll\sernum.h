#ifndef SERNUM_H
#define SERNUM_H

#include <QObject>
struct CAdapterInfo
{
	char szIP[20];
    unsigned char Mac[8];
};
class SerNum
{
public:
    SerNum();
    static SerNum *GetSerNum()
	{
		static SerNum serMgr;
		return &serMgr;
	}
    ~SerNum();
public:
    //程序初始化时，加载本地硬件信息
    bool LoadAdapterInfo(QString &sError);
    //生成用户信息 nordCode = stationId*100+nLaneId
    bool BuildUserInfo(int nOrgCode,char szUserInfo[17],QString &sError);
    //生成注册码 nordCode = stationId*100+nLaneId
    bool BuildSeriNo(int nOrgCode,const char *szUserInfo,int nUserInfo,char szSeriNo[17],QString &sError);
    //校验注册码信息
    bool CheckSeriNo(int nOrgCode,const char *szSeriNo,int nSeriNoLen,QString &sError);
    
private:
    bool UnPackUserInfo(int nOrgCode, const char *szUserInfo, int nUserInfoLen, uchar *UserInfoRaw, QString &sError);
    bool UnPackSeriNo(int nOrgCode, const char *szSeriNo, int nSeriNoLen, uchar UserInfoRaw[], QString &sError);
    QString GetHostIPAddress();
    QString GetHostMACAddress();
	unsigned short GetRand();
public:
	QList<CAdapterInfo> m_Adapters;
	char m_SeedIndex[6];
    char m_Key[16];
	char m_UserInfoRaw[8];
};

#endif // SERNUM_H
