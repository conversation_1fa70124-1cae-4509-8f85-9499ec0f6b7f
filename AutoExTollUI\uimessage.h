#ifndef UIMESSAGE_H
#define UIMESSAGE_H

const quint32 MAX_UIMSG_LENGTH = 1000;

const quint8 UMSG_TYPE_LANEINFO = 0x01;
const quint8 UMSG_TYPE_HELP = 0x11;
const quint8 UMSG_TYPE_TOLLINFO = 0x12;
const quint8 UMSG_TYPE_TICKET = 0x13;
const quint8 UMSG_TYPE_RETURN = 0xA0;

#pragma pack(push, 1)

struct UIM_MsgHead {
    quint8 msgFlag;  //消息标识（固定值0x8E）
    quint32 msgLen;  //消息长度(由下一个字节,到消息尾部的大小)
    quint8 msgType;  //消息类型
    quint16 msgId;   //消息Id(标识每一条消息，接收端收到后，原值返回
};

#pragma pack(pop)

/**
 * @brief The UIM_Msg01_LaneInfo struct
 * 车道信息
 */
struct UIM_Msg01_LaneInfo {
    QString stationName;
    QString laneName;
};

/**
 * @brief The UIM_Msg11_HelpMsg struct
 * 帮助提示
 */
struct UIM_Msg11_HelpMsg {
    quint16 helpType;
};

/**
 * @brief The UIM_Msg12_TollInfo struct
 * 收费信息
 */
struct UIM_Msg12_TollInfo {
    QString vlp;            //车牌号
    QString vehClassName;   //车型名称
    QString vehTypeName;    //车种名称
    QString enStationName;  //入口收费站
    quint32 tollMoney;      //收费金额（分）
};

struct UIM_MsgA0_Return {
    quint8 returnCode;  //处理结果,0:成功，1:失败
    quint16 errCode;    //处理错误代码（备用）
    QString returnMsg;  //返回信息字符串
};

/**
 * @brief The UIM_Message class
 *
 */
class UIM_Message {
public:
    UIM_Message() { msgObject = 0; }

public:
    struct UIM_MsgHead msgHead;
    void* msgObject;
};

#endif  // UIMESSAGE_H
