<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SerNumFm</class>
 <widget class="QDialog" name="SerNumFm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>514</width>
    <height>212</height>
   </rect>
  </property>
  <property name="contextMenuPolicy">
   <enum>Qt::NoContextMenu</enum>
  </property>
  <property name="windowTitle">
   <string>车道注册</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: rgb(142, 194, 229)</string>
  </property>
  <widget class="QGroupBox" name="groupBox">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>0</y>
     <width>471</width>
     <height>111</height>
    </rect>
   </property>
   <property name="title">
    <string>本机信息</string>
   </property>
   <widget class="QWidget" name="layoutWidget">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>20</y>
      <width>441</width>
      <height>81</height>
     </rect>
    </property>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_5">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>站 代 码</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="le_OrgID">
           <property name="minimumSize">
            <size>
             <width>160</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>160</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(255, 255, 255);</string>
           </property>
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>车道号</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="le_LaneID">
           <property name="maximumSize">
            <size>
             <width>160</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(255, 255, 255);</string>
           </property>
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <item>
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>本机代码</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="le_UserInfo">
         <property name="minimumSize">
          <size>
           <width>160</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>160</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(255, 255, 255);</string>
         </property>
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QWidget" name="layoutWidget1">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>120</y>
     <width>471</width>
     <height>30</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QLabel" name="label_4">
      <property name="text">
       <string>注 册 码</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="le_SerNo">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <property name="maxLength">
       <number>32</number>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="pb_OK">
      <property name="text">
       <string>注册</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QLabel" name="lb_Info">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>170</y>
     <width>441</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>按【确认】键完成注册</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
