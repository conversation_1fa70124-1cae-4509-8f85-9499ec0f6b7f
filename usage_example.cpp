 /**
 * @file usage_example.cpp
 * @brief AutoToll.exe 设备初始化修复功能使用示例
 * @description 展示如何在现有的AutoToll代码中集成设备初始化修复功能
 * 
 * 此文件展示了以下使用场景：
 * 1. 在主窗口中集成设备初始化管理器
 * 2. 在应用程序启动时启用看门狗监控
 * 3. 处理设备初始化事件和错误
 * 4. 生成详细的初始化日志
 * 
 * <AUTHOR>
 * @date 2025-01-21
 * @version 1.0
 */

#include "device_init_fix.h"
#include "dlgmain.h"  // 假设这是现有的主窗口头文件

/**
 * @brief 修改后的QDlgMain类，集成设备初始化修复功能
 * @description 在现有的主窗口类中添加设备初始化管理功能
 */
class QDlgMainFixed : public QDlgMain {
    Q_OBJECT
    
public:
    explicit QDlgMainFixed(QWidget *parent = 0);
    virtual ~QDlgMainFixed();
    
    // 修改后的AppStart方法
    bool AppStart(QString &sError) override;
    
private slots:
    // 设备初始化完成处理
    void onDeviceInitialized(const DeviceInitResult& result);
    
    // 所有设备初始化完成处理
    void onAllDevicesInitialized(bool success, const QString& summary);
    
    // 初始化进度更新处理
    void onInitializationProgress(int totalDevices, int completedDevices, const QString& currentDevice);
    
    // 应用程序挂起检测处理
    void onApplicationHangDetected(qint64 lastHeartbeatTime);
    
    // 准备重启处理
    void onPreparingRestart();
    
private:
    DeviceInitializationManager* m_pDeviceInitMgr;  // 设备初始化管理器
    ApplicationWatchdog* m_pWatchdog;               // 应用程序看门狗
    bool m_bDeviceInitComplete;                     // 设备初始化完成标志
    QStringList m_initErrors;                       // 初始化错误列表
    
    // 初始化设备管理器
    bool initializeDeviceManager();
    
    // 启动应用程序看门狗
    void startApplicationWatchdog();
    
    // 显示初始化进度
    void showInitializationProgress(const QString& message);
    
    // 处理初始化错误
    void handleInitializationError(const QString& deviceName, const QString& error);
};

QDlgMainFixed::QDlgMainFixed(QWidget *parent)
    : QDlgMain(parent), 
      m_pDeviceInitMgr(0), 
      m_pWatchdog(0),
      m_bDeviceInitComplete(false)
{
    // 初始化设备管理器
    m_pDeviceInitMgr = DeviceInitializationManager::getInstance();
    m_pWatchdog = ApplicationWatchdog::getInstance();
    
    // 连接信号槽
    connect(m_pDeviceInitMgr, SIGNAL(deviceInitialized(DeviceInitResult)),
            this, SLOT(onDeviceInitialized(DeviceInitResult)));
    connect(m_pDeviceInitMgr, SIGNAL(allDevicesInitialized(bool, QString)),
            this, SLOT(onAllDevicesInitialized(bool, QString)));
    connect(m_pDeviceInitMgr, SIGNAL(initializationProgress(int, int, QString)),
            this, SLOT(onInitializationProgress(int, int, QString)));
    
    connect(m_pWatchdog, SIGNAL(applicationHangDetected(qint64)),
            this, SLOT(onApplicationHangDetected(qint64)));
    connect(m_pWatchdog, SIGNAL(preparingRestart()),
            this, SLOT(onPreparingRestart()));
}

QDlgMainFixed::~QDlgMainFixed()
{
    if (m_pWatchdog) {
        m_pWatchdog->stopWatchdog();
    }
}

bool QDlgMainFixed::AppStart(QString &sError)
{
    DetailedDeviceLogger::writeToSystemLog("=== AutoToll.exe 应用程序启动 ===");
    
    // 启动应用程序看门狗
    startApplicationWatchdog();
    
    // 执行原有的初始化逻辑（网络、数据库等）
    DetailedDeviceLogger::writeToSystemLog("开始执行原有初始化逻辑...");
    
    // 1. 网络模块初始化（添加超时控制）
    showInitializationProgress("正在初始化网络模块...");
    QString sErrorCode;
    if (!Curl::Global_Init(sErrorCode)) {
        sError = "网络模块初始化失败: " + sErrorCode;
        DetailedDeviceLogger::logInitError(DEVICE_TYPE_NETWORK, "CURL模块", sError);
        return false;
    }
    DetailedDeviceLogger::logInitComplete(DEVICE_TYPE_NETWORK, "CURL模块", true, 0, "成功");
    m_pWatchdog->updateHeartbeat(); // 更新看门狗心跳
    
    // 2. 站点信息验证
    showInitializationProgress("正在验证站点信息...");
    qint32 nStationid = Ptr_Info->GetStationID();
    qint32 station = nStationid % 100;
    if (station < 0) {
        sError = QString("站编码配置错误");
        return false;
    }
    qint32 road = nStationid / 100 % 1000;
    if (road > 255 || road < 0) {
        sError = QString("站编码路段配置错误");
        return false;
    }
    m_pWatchdog->updateHeartbeat(); // 更新看门狗心跳
    
    // 3. 数据库初始化（添加超时控制）
    showInitializationProgress("正在初始化数据库...");
    if (!Ptr_ETCCtrl->InitLocalDB(Ptr_Info->GetDBPath(), Ptr_Info->GetBakDBPath(),
                                  Ptr_Info->bRemoveBakFile())) {
        sError = QString("数据库初始化失败,请联系维护人员");
        DetailedDeviceLogger::logInitError(DEVICE_TYPE_DATABASE, "本地数据库", sError);
        return false;
    }
    DetailedDeviceLogger::logInitComplete(DEVICE_TYPE_DATABASE, "本地数据库", true, 0, "成功");
    m_pWatchdog->updateHeartbeat(); // 更新看门狗心跳
    
    // 4. 批次信息初始化
    showInitializationProgress("正在初始化批次信息...");
    if (!CBatchMgr::GetBatchMgr()->InitBatchInfo(Ptr_Info->GetStationID(), Ptr_Info->GetLaneId())) {
        sError = QString("批次流水号初始化失败");
        return false;
    }
    m_pWatchdog->updateHeartbeat(); // 更新看门狗心跳
    
    // 5. 初始化状态机
    showInitializationProgress("正在初始化状态机...");
    InitAllLaneState();
    m_pWatchdog->updateHeartbeat(); // 更新看门狗心跳
    
    // 6. 参数文件管理
    showInitializationProgress("正在初始化参数文件管理...");
    CParamFileMgr::InitParamFiles(Ptr_Info->GetLaneType(), Ptr_Info->bOpenLane());
    m_pWatchdog->updateHeartbeat(); // 更新看门狗心跳
    
    // 7. 使用新的设备初始化管理器
    showInitializationProgress("正在初始化硬件设备...");
    if (!initializeDeviceManager()) {
        sError = "设备初始化管理器启动失败";
        return false;
    }
    
    // 异步初始化设备（不阻塞主线程）
    m_pDeviceInitMgr->initializeAllDevicesAsync();
    
    // 等待关键设备初始化完成（添加超时）
    QElapsedTimer waitTimer;
    waitTimer.start();
    const int maxWaitTime = 60000; // 最多等待60秒
    
    while (!m_bDeviceInitComplete && waitTimer.elapsed() < maxWaitTime) {
        QApplication::processEvents(); // 处理事件循环
        QThread::msleep(100);          // 短暂休眠
        m_pWatchdog->updateHeartbeat(); // 更新看门狗心跳
    }
    
    if (!m_bDeviceInitComplete) {
        sError = QString("设备初始化超时（%1秒）").arg(maxWaitTime / 1000);
        DetailedDeviceLogger::writeToSystemLog(sError);
        
        // 检查是否有关键设备初始化失败
        bool hasCriticalFailure = false;
        for (const QString& error : m_initErrors) {
            if (error.contains("IO卡") || error.contains("网络") || error.contains("数据库")) {
                hasCriticalFailure = true;
                break;
            }
        }
        
        if (hasCriticalFailure) {
            return false; // 关键设备失败，不能继续
        } else {
            DetailedDeviceLogger::writeToSystemLog("非关键设备初始化超时，继续启动应用程序");
        }
    }
    
    // 8. 继续执行原有的初始化逻辑
    showInitializationProgress("正在完成初始化...");
    
    // ETC交易界面显示
    connect(this, SIGNAL(NotifyETCDisplayEvent(int, int, QList<QVariant>)), this,
            SLOT(OnETCDisplayMsgEvent(int, int, QList<QVariant>)));
    
    // 提示信息
    connect(this, SIGNAL(NotifyShowPromptEvent(QString, bool, bool)), this,
            SLOT(OnShowPromptMsgEvent(QString, bool, bool)));
    
    m_pWatchdog->updateHeartbeat(); // 更新看门狗心跳
    
    // 启动定时器
    m_TimerReqParams = startTimer(5 * 60 * 1000);  // 5分钟下载一次参数文件
    m_TimerOutUseTimeParam = startTimer(60 * 1000);
    
    if (Ptr_Info->bGrantPaperCard()) {
        m_TimerPrintPaper = startTimer(2 * 1000);
    }
    
    m_PrintLogTimer.start(1000);
    UpdateVehState();
    
    m_pWatchdog->updateHeartbeat(); // 更新看门狗心跳
    
    // 生成初始化摘要报告
    QMap<DeviceType, DeviceInitResult> results;
    for (int i = 0; i < DEVICE_TYPE_COUNT; ++i) {
        DeviceType deviceType = static_cast<DeviceType>(i);
        results[deviceType] = m_pDeviceInitMgr->getDeviceResult(deviceType);
    }
    QString summary = DetailedDeviceLogger::generateInitSummary(results);
    DetailedDeviceLogger::writeToSystemLog(summary);
    
    DetailedDeviceLogger::writeToSystemLog("=== AutoToll.exe 应用程序启动完成 ===");
    return true;
}

bool QDlgMainFixed::initializeDeviceManager()
{
    try {
        // 注册自定义设备配置（如果需要）
        DeviceInitConfig customVPRConfig = DeviceConfigHelper::getVPRConfig();
        customVPRConfig.timeoutMs = 20000; // 自定义超时时间
        m_pDeviceInitMgr->registerDeviceConfig(DEVICE_TYPE_VPR, customVPRConfig);
        
        DeviceInitConfig customIOConfig = DeviceConfigHelper::getIOCardConfig();
        customIOConfig.skipOnFailure = false; // IO卡失败不跳过
        m_pDeviceInitMgr->registerDeviceConfig(DEVICE_TYPE_IOCARD, customIOConfig);
        
        return true;
    } catch (const std::exception& e) {
        DetailedDeviceLogger::writeToSystemLog(
            QString("设备初始化管理器初始化异常: %1").arg(e.what()));
        return false;
    }
}

void QDlgMainFixed::startApplicationWatchdog()
{
    // 启动应用程序看门狗
    m_pWatchdog->setAutoRestart(true);  // 启用自动重启
    m_pWatchdog->startWatchdog(5000, 30000); // 每5秒检查，30秒超时
    
    DetailedDeviceLogger::writeToSystemLog("应用程序看门狗已启动");
}

void QDlgMainFixed::showInitializationProgress(const QString& message)
{
    // 在界面上显示初始化进度（如果有启动画面）
    if (m_pFrmLoading) {
        m_pFrmLoading->ShowMessage(message);
    }
    
    // 记录到日志
    DetailedDeviceLogger::writeToSystemLog(QString("初始化进度: %1").arg(message));
    
    // 更新看门狗心跳
    m_pWatchdog->updateHeartbeat();
}

void QDlgMainFixed::handleInitializationError(const QString& deviceName, const QString& error)
{
    QString errorMsg = QString("设备 %1 初始化错误: %2").arg(deviceName).arg(error);
    m_initErrors.append(errorMsg);
    
    DetailedDeviceLogger::writeToSystemLog(errorMsg);
    
    // 在界面上显示错误（如果需要）
    if (m_pFrmPromptMsg) {
        m_pFrmPromptMsg->ShowPromptMsg(errorMsg, true); // 显示警告信息
    }
}

void QDlgMainFixed::onDeviceInitialized(const DeviceInitResult& result)
{
    QString deviceName = DetailedDeviceLogger::getDeviceTypeName(result.deviceType);
    
    switch (result.status) {
        case DEVICE_INIT_SUCCESS:
            DetailedDeviceLogger::writeToSystemLog(
                QString("设备初始化成功: %1 (耗时: %2ms)").arg(deviceName).arg(result.initTime));
            break;
            
        case DEVICE_INIT_FAILED:
            handleInitializationError(deviceName, result.errorMessage);
            break;
            
        case DEVICE_INIT_TIMEOUT:
            handleInitializationError(deviceName, "初始化超时");
            break;
            
        default:
            break;
    }
    
    // 更新看门狗心跳
    m_pWatchdog->updateHeartbeat();
}

void QDlgMainFixed::onAllDevicesInitialized(bool success, const QString& summary)
{
    m_bDeviceInitComplete = true;
    
    QString message = QString("所有设备初始化完成 - %1: %2").arg(success ? "成功" : "部分失败").arg(summary);
    DetailedDeviceLogger::writeToSystemLog(message);
    
    if (success) {
        showInitializationProgress("所有设备初始化成功");
    } else {
        showInitializationProgress("部分设备初始化失败，但可以继续运行");
        
        // 显示错误摘要
        if (!m_initErrors.isEmpty()) {
            QString errorSummary = "设备初始化错误摘要:\n" + m_initErrors.join("\n");
            if (m_pFrmPromptMsg) {
                m_pFrmPromptMsg->ShowPromptMsg(errorSummary, true);
            }
        }
    }
    
    // 更新看门狗心跳
    m_pWatchdog->updateHeartbeat();
}

void QDlgMainFixed::onInitializationProgress(int totalDevices, int completedDevices, const QString& currentDevice)
{
    QString progressMsg = QString("设备初始化进度: %1/%2 当前: %3")
                         .arg(completedDevices)
                         .arg(totalDevices)
                         .arg(currentDevice);
    
    showInitializationProgress(progressMsg);
}

void QDlgMainFixed::onApplicationHangDetected(qint64 lastHeartbeatTime)
{
    QDateTime lastTime = QDateTime::fromMSecsSinceEpoch(lastHeartbeatTime);
    QString message = QString("检测到应用程序可能挂起，上次响应时间: %1")
                     .arg(lastTime.toString("yyyy-MM-dd hh:mm:ss.zzz"));
    
    DetailedDeviceLogger::writeToSystemLog(message);
    
    // 可以在这里添加挂起处理逻辑，例如：
    // 1. 保存当前状态
    // 2. 发送报警通知
    // 3. 尝试恢复操作
    
    if (m_pFrmPromptMsg) {
        m_pFrmPromptMsg->ShowPromptMsg("系统检测到可能的挂起，正在尝试恢复...", true);
    }
}

void QDlgMainFixed::onPreparingRestart()
{
    DetailedDeviceLogger::writeToSystemLog("应用程序准备自动重启...");
    
    if (m_pFrmPromptMsg) {
        m_pFrmPromptMsg->ShowPromptMsg("系统将在2秒后自动重启...", true);
    }
    
    // 执行清理操作
    AppDestroy();
}

/**
 * @brief 在main函数中的使用示例
 */
void mainUsageExample()
{
    // 在main函数中使用修复后的主窗口类
    QApplication app(argc, argv);
    
    // 创建修复后的主窗口
    QDlgMainFixed *pMainWnd = new QDlgMainFixed();
    
    // 初始化主窗口
    pMainWnd->Init();
    pMainWnd->show();
    
    // 启动应用程序
    QString sError;
    if (pMainWnd->AppStart(sError)) {
        int exitCode = app.exec();
        
        // 根据退出码处理重启
        if (exitCode == 999) { // 看门狗重启
            DetailedDeviceLogger::writeToSystemLog("应用程序因看门狗检测到挂起而重启");
            // 这里可以添加重启逻辑
        }
        
        return exitCode;
    } else {
        DetailedDeviceLogger::writeToSystemLog(QString("应用程序启动失败: %1").arg(sError));
        return -1;
    }
}

/**
 * @brief 单独使用设备初始化管理器的示例
 */
void standaloneDeviceInitExample()
{
    // 获取设备初始化管理器实例
    DeviceInitializationManager* deviceMgr = DeviceInitializationManager::getInstance();
    
    // 自定义设备配置
    DeviceInitConfig vprConfig = DeviceConfigHelper::getVPRConfig();
    vprConfig.timeoutMs = 25000;        // 25秒超时
    vprConfig.retryCount = 1;           // 只重试1次
    deviceMgr->registerDeviceConfig(DEVICE_TYPE_VPR, vprConfig);
    
    // 同步初始化所有设备
    QString error;
    bool success = deviceMgr->initializeAllDevices(error);
    
    if (success) {
        qDebug() << "所有设备初始化成功";
    } else {
        qDebug() << "设备初始化失败:" << error;
    }
    
    // 获取初始化结果
    DeviceInitResult vprResult = deviceMgr->getDeviceResult(DEVICE_TYPE_VPR);
    qDebug() << "VPR设备状态:" << vprResult.status << "错误信息:" << vprResult.errorMessage;
}

/**
 * @brief 单独使用应用程序看门狗的示例
 */
void standaloneWatchdogExample()
{
    // 获取应用程序看门狗实例
    ApplicationWatchdog* watchdog = ApplicationWatchdog::getInstance();
    
    // 连接挂起检测信号
    QObject::connect(watchdog, &ApplicationWatchdog::applicationHangDetected, 
                    [](qint64 lastTime) {
        qDebug() << "检测到应用程序挂起，上次心跳:" << 
                    QDateTime::fromMSecsSinceEpoch(lastTime).toString();
    });
    
    // 启动看门狗（每3秒检查，20秒超时）
    watchdog->startWatchdog(3000, 20000);
    
    // 在主要操作中更新心跳
    while (true) {
        // 执行一些操作
        QThread::msleep(1000);
        
        // 更新心跳
        watchdog->updateHeartbeat();
    }
}

#include "usage_example.moc"