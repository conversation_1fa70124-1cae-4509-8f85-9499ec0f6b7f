#ifndef FORMREPAYNEW_H
#define FORMREPAYNEW_H

#include <QWidget>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTimer>
#include <QKeyEvent>
#include <QPainter>
#include <QDateTime>

#include "../../baseopwidget.h"
#include "../../MtcKey/MtcKeyDef.h"
#include "../common/repaytypes.h"
#include "../business/repaymanager.h"
#include "formpaymentselect.h"
#include "../../common/lanetype.h"
#include "../../forminputplate.h"
#include "forminputamount.h"
#include "FormVehTypeInput.h"
#include "FormRepaySuccess.h"

//省份的拼音选择（从forminputplate复制）
//typedef struct
//{
//    QString sPy;
//    QString sHz;
//} ProvSelect;

/**
 * @brief 新补费功能主界面
 * 支持当趟补费和省内名单补费两种模式
 */
class FormRepayNew : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormRepayNew(QWidget *parent = 0);
    virtual ~FormRepayNew();

    // 初始化界面
    virtual void InitUI(int iFlag = 0);
    
    // 设置补费类型
    void SetRepayType(RepayType type);
    
    // 设置车辆信息
    void SetVehicleInfo(const QString &vehPlate, int vehPlateColor, int vehType);
    
    // 开始补费流程
    bool StartRepayProcess();

protected:
    // 重写绘制事件
    void paintEvent(QPaintEvent *event);
    
    // 重写按键事件处理
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    
    // 重写模态显示后的处理
    virtual void OnModalShowed();

    // 重写取消处理，确保清理补费状态
    virtual void OnCancel();

private slots:
    // RepayManager信号响应
    void OnRepayStarted(RepayType type);
    void OnRepayStageChanged(RepayStage stage);
    void OnRepayCompleted(bool success, const QString &message);
    void OnPaymentProcessing();
    void OnPaymentCompleted(bool success, const QString &message);
    void OnDebtQueryCompleted(bool success, const RepayDebtQueryResult &result);
    void OnRepayError(RepayErrorCode errorCode, const QString &message);
    
    // 界面操作响应
    void OnVehPlateChanged();
    void OnAmountChanged();
    void OnPaymentButtonClicked();
    
    // 注意：车牌颜色和支付方式现在通过键盘输入和单独页面处理
    
    // 定时器响应
    void OnQueryTimeout();
    void OnErrorDisplayTimeout();
    
    // 延时操作
    void OnDelayedOk();

private:
    // 界面初始化
    void InitUIConfig();
    void InitControls();
    void SetupControlProperties();
    void SetupButtonStyles();
    void InitLayout();
    void InitConnections();
    
    // 布局辅助方法
    void LayoutInputArea(int startY, int width);
    void LayoutPaymentArea(int startY, int width);
    void LayoutButtonArea(int startY, int width);
    void LayoutInfoArea(int startY, int width, int height);
    
    // 设置车牌颜色
    void SetVehPlateColor(int color);
    
    // 获取当前阶段的帮助信息
    QString GetHelpMessageForCurrentStage();
    
    // 绘制函数
    void DrawBackground(QPainter &painter);
    void DrawTitle(QPainter &painter);
    void DrawStagePrompt(QPainter &painter);
    void DrawVehicleInfo(QPainter &painter);
    void DrawAmountInfo(QPainter &painter);
    void DrawPaymentOptions(QPainter &painter);
    void DrawHelpMessage(QPainter &painter);
    void DrawProgressIndicator(QPainter &painter);
    
    // 辅助函数
    QString GetVehPlateColorName(int color);
    
    // 界面状态控制
    void UpdateUI();
    void UpdateStageDisplay();
    void UpdateDebtInfo(const RepayDebtQueryResult &result);
    void SetUIEnabled(bool enabled);
    void ShowStageMessage(RepayStage stage);
    void ShowStageMessage(const QString &message);
    void ClearInputs();
    void ResetUI();
    
    // 输入验证
    bool ValidateVehPlate();
    bool ValidateAmount();
    bool ValidateInputs();
    
    // 业务处理
    void PerformAuthorization();
    void ProcessCurrentRepay();
    void ProcessProvinceRepay();
    void StartDebtQuery();
    void StartVehicleInput();              // 开始车辆输入
    void StartPaymentProcess();            // 开始支付流程
    void ProcessPayment(CTransPayType payType);
    void HandleRepayCompletion(bool success, const QString &message);
    void HandleRepayError(RepayErrorCode errorCode, const QString &message);
    
    // 界面控制
    void EnableInput(bool enabled);
    void EnablePaymentButtons(bool enabled);
    void SetCurrentStage(RepayStage stage);
    void UpdateProgressDisplay();
    

    
    // 错误处理
    void ShowErrorMessage(const QString &message);
    void ShowSuccessMessage(const QString &message);
    void ShowWarningMessage(const QString &message);
    QString GetEnhancedPaymentErrorMessage(const QString &originalError);
    int GetMaxFeeForCurrentVehicle();
    
    // 车牌输入处理（使用FormInputPlate类）
    bool ShowPlateInputDialog();

    // 车型输入处理（使用FormVehTypeInput类）
    bool ShowVehTypeInputDialog();

    // 金额输入处理（使用FormInputAmount类）
    bool ShowAmountInputDialog();

    // 支付方式选择（使用单独页面）
    bool ShowPaymentSelection(CTransPayType &selectedPayType);

    // 显示补费成功界面
    bool ShowRepaySuccessDialog(CTransPayType payType);

    // 显示债务详情界面
    bool ShowDebtDetailDialog(const RepayDebtQueryResult &result);

private:
    // 界面控件已移除，FormRepayNew 仅作为流程控制器
    
    // 业务数据
    RepayType m_repayType;            // 补费类型
    RepayStage m_currentStage;        // 当前阶段
    QString m_vehPlate;               // 车牌号
    int m_vehPlateColor;              // 车牌颜色
    int m_vehType;                    // 车型
    int m_amount;                     // 补费金额
    RepayDebtQueryResult m_debtResult;     // 欠费查询结果
    
    // 车牌输入组件（改为局部变量方式，不再作为成员变量）
    
    // 状态管理
    bool m_bProcessing;               // 是否正在处理
    bool m_bInputEnabled;             // 输入是否启用
    bool m_bPaymentEnabled;           // 支付按钮是否启用
    bool m_paymentInProgress;         // 支付是否正在进行
    QDateTime m_startTime;            // 开始时间
    
    // 组件实例
    RepayManager *m_pRepayManager;    // 补费管理器
    
    // 定时器
    QTimer *m_pQueryTimer;            // 查询超时定时器
    QTimer *m_pErrorDisplayTimer;     // 错误信息显示定时器
    bool m_bConnectionsInited;        // 信号是否已连接
    
    // 界面配置
    int m_nTitleHeight;               // 标题高度
    int m_nStageHeight;               // 阶段提示高度
    int m_nInputAreaHeight;           // 输入区域高度
    int m_nPaymentAreaHeight;         // 支付区域高度
    int m_nButtonAreaHeight;          // 按钮区域高度
    int m_nInfoAreaHeight;            // 信息区域高度
    
    QFont m_fontTitle;                // 标题字体
    QFont m_fontText;                 // 普通文本字体
    QFont m_fontEdit;                 // 输入框字体
    QFont m_fontButton;               // 按钮字体
    
    // 颜色配置
    QColor m_colorBackground;         // 背景色
    QColor m_colorTitle;              // 标题色
    QColor m_colorText;               // 文本色
    QColor m_colorHighlight;          // 高亮色
    QColor m_colorError;              // 错误色
    QColor m_colorSuccess;            // 成功色
    QColor m_colorWarning;            // 警告色
    
    // 常量定义
    static const int QUERY_TIMEOUT = 30000;      // 查询超时时间(毫秒)
    static const int STAGE_PROMPT_HEIGHT = 40;   // 阶段提示高度
    static const int BUTTON_WIDTH = 80;          // 按钮宽度
    static const int BUTTON_HEIGHT = 40;         // 按钮高度
    static const int EDIT_HEIGHT = 35;           // 输入框高度
    static const int MARGIN = 10;                // 边距
    static const int SPACING = 15;               // 间距
};

#endif // FORMREPAYNEW_H
