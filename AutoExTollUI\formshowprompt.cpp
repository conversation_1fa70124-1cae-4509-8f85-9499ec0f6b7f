#include "formshowprompt.h"

#include "globalutils.h"

FormShowPrompt::FormShowPrompt(QWidget *parent) : BaseForm(parent) { connect(&m_timer, SIGNAL(timeout()), this, SLOT(OnSplash())); }

void FormShowPrompt::paintEvent(QPaintEvent *)
{
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    <PERSON><PERSON><PERSON><PERSON>(painter);

    QPixmap pmIcon;
    pmIcon.load(":/images/prompt.png");
    painter.drawPixmap(g_GlobalUI.prompt_iconRect, pmIcon.scaled(g_GlobalUI.prompt_iconRect.size(), Qt::KeepAspectRatio, Qt::SmoothTransformation));

    //绘制文字

    QFont font(g_GlobalUI.m_FontName, g_GlobalUI.prompt_FontSize, QFont::Bold);
    font.setLetterSpacing(QFont::AbsoluteSpacing, g_GlobalUI.prompt_FontSpace);
    QColor clrText(240, 175, 1);
    painter.setFont(font);
    painter.setPen(clrText);
    if (m_nWarnCount % 2 == 0) {
        painter.drawText(g_GlobalUI.prompt_textRect, Qt::AlignVCenter | Qt::AlignLeft | Qt::TextWordWrap, m_promptMsg);
    }

    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

void FormShowPrompt::OnSplash()
{
    m_nWarnCount++;
    update();
}

void FormShowPrompt::ShowPrompt(const QString &sText, bool bPlaySound)
{
    m_promptMsg = sText;
    m_nWarnCount = 0;
    m_timer.start(1000);
    if (bPlaySound) {
        PlaySound("prompt.wav", 0, 0);
    }
    ShowForm();
}

void FormShowPrompt::ShowPaying(const QString &sText)
{
    m_promptMsg = sText;
    m_nWarnCount = 0;
    m_timer.start(1000);
    PlaySound("paying.wav", 20, 5);
    ShowForm();
}


//正在核对信息，请稍候
void FormShowPrompt::ShowChecking(const QString &sText)
{
    m_promptMsg = sText;
    m_nWarnCount = 0;
    m_timer.start(1000);
    PlaySound("checking.wav", 20, 5);
    ShowForm();
}

//读卡失败，请重新插入有效的卡
void FormShowPrompt::ShowChardError(const QString &sText)
{
    m_promptMsg = sText;
    m_nWarnCount = 0;
    m_timer.start(1000);
    PlaySound("carderror.wav", 0, 0);
    ShowForm();
}

//核对信息失败，
void FormShowPrompt::ShowCheckError(const QString &sText)
{
    m_promptMsg = sText;
    m_nWarnCount = 0;
    m_timer.start(1000);
    PlaySound("checkerror.wav", 0, 0);
    ShowForm();
}

void FormShowPrompt::PayFail(const QString &sText)
{
    m_promptMsg = sText;
    m_nWarnCount = 0;
    m_timer.start(1000);
    PlaySound("payfail.wav", 0, 0);
    ShowForm();
}

void FormShowPrompt::Password(const QString &sText)
{
    m_promptMsg = sText;
    m_nWarnCount = 0;
    m_timer.start(1000);
    PlaySound("password.wav", 0, 0);
    ShowForm();
}

void FormShowPrompt::HideForm()
{
    m_timer.stop();
    BaseForm::HideForm();
}
