#include "forminterceptselect.h"
#include "../../dlgmain.h"
#include "../../log4qt/ilogmsg.h"
#include "../../globalui.h"
#include <QApplication>
#include <QKeyEvent>
#include <QPainter>
#include <QFont>

// 静态常量定义
const FormInterceptSelect::InterceptOption FormInterceptSelect::INTERCEPT_OPTIONS[OPTION_COUNT] = {
    { Intercept_Entry, "入口拦截", "在入口收费站进行车辆拦截处理", "1" },
    { Intercept_Exit,  "出口拦截", "在出口收费站进行车辆拦截处理", "2" }
};

FormInterceptSelect::FormInterceptSelect(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_selectedType(Intercept_Entry)
    , m_nCurrentIndex(0)
    , m_bSelectionConfirmed(false)
    , m_promptMessage("请选择拦截方式")
    , m_bProcessing(false)
{
    // 初始化界面配置
    InitUIConfig();
    
    InfoLog("创建拦截方式选择界面");
}

FormInterceptSelect::~FormInterceptSelect()
{
    InfoLog("销毁拦截方式选择界面");
}

void FormInterceptSelect::InitUIConfig()
{
    // 设置字体，使用全局配置
    m_fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize, QFont::Bold);
    m_fontOption = QFont(g_GlobalUI.m_FontName, 20, QFont::Bold);
    m_fontHelp = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
}

void FormInterceptSelect::InitUI(int iFlag)
{
    CBaseOpWidget::InitUI(iFlag);
    
    InfoLog("初始化拦截方式选择界面");
    
    // 设置界面配置
    InitUIConfig();
    
    // 连接信号
    InitConnections();
    
    // 设置默认选择（出口拦截）
    m_nCurrentIndex = 0;
    m_selectedType = Intercept_Entry;
    
    // 安装子控件键盘事件过滤器
    filterChildrenKeyEvent();
    
    DebugLog("拦截方式选择界面初始化完成");
}

void FormInterceptSelect::InitControls()
{
    // 不需要创建控件，使用自绘方式
    // 这样可以保持与其他界面一致的风格
}

void FormInterceptSelect::SetupControlProperties()
{
    // 不需要设置控件属性，使用自绘方式
}

void FormInterceptSelect::InitLayout()
{
    // 不需要设置布局，使用自绘方式
}

void FormInterceptSelect::InitConnections()
{
    // 无需定时器连接
}

bool FormInterceptSelect::SelectInterceptType(InterceptType defaultType)
{
    InfoLog("显示拦截方式选择界面");
    
    // 设置默认选择
    SetDefaultSelection(defaultType);
    
    // 显示界面并返回结果
    int result = doModalShow();
    
    if (result == CBaseOpWidget::Rlt_OK && m_bSelectionConfirmed) {
        InfoLog(QString("拦截方式选择完成 - 类型:%1(%2)")
                .arg(static_cast<int>(m_selectedType))
                .arg(GetInterceptTypeName(m_selectedType)));
        return true;
    } else {
        InfoLog("拦截方式选择取消");
        return false;
    }
}

bool FormInterceptSelect::ShowInterceptSelect(InterceptType &selectedType)
{
    // 保持兼容性的传统接口
    if (SelectInterceptType(selectedType)) {
        selectedType = m_selectedType;
        return true;
    }
    return false;
}

void FormInterceptSelect::SetDefaultSelection(InterceptType defaultType)
{
    m_selectedType = defaultType;
    m_nCurrentIndex = GetIndexByInterceptType(defaultType);
    
    // 重置状态
    m_bSelectionConfirmed = false;
    m_bProcessing = false;
    
    UpdateSelectionDisplay();
}

void FormInterceptSelect::SetPromptMessage(const QString &message)
{
    m_promptMessage = message;
    // 重绘界面以显示新的提示信息
    update();
}

void FormInterceptSelect::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event);
    
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    
    // 绘制背景
    DrawBackground(painter);
    
    // 绘制标题
    DrawTitle(painter);
    
    // 绘制选项
    DrawOptions(painter);
    
    // 绘制帮助信息
    DrawHelpMessage(painter);
    
    painter.end();
    
    // 绘制到窗口
    QPainter windowPainter(this);
    windowPainter.drawPixmap(rectClient, pixmap);
}

void FormInterceptSelect::DrawBackground(QPainter &painter)
{
    QRect rectClient = this->rect();
    
    // 绘制背景色
    painter.setBrush(g_GlobalUI.m_ColorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);
}

void FormInterceptSelect::DrawTitle(QPainter &painter)
{
    // 绘制标题
    QFont fontTitle = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    QRect rctTitle(0, 0, rect().width(), g_GlobalUI.optw_TitleHeight);
    painter.setFont(fontTitle);
    painter.setPen(Qt::black);
    painter.drawText(rctTitle, Qt::AlignCenter, "拦截方式");
}

void FormInterceptSelect::DrawOptions(QPainter &painter)
{
    // 计算选项位置
    int startY = g_GlobalUI.optw_TitleHeight + 50;  // 标题下方留空间
    int lineHeight = 60;
    
    // 设置选项字体
    QFont fontOption = QFont(g_GlobalUI.m_FontName, 20, QFont::Normal);
    painter.setFont(fontOption);
    
    // 绘制选项1：入口拦截
    QRect rect1(0, startY, rect().width(), lineHeight);
    if (m_nCurrentIndex == 0) {
        painter.setPen(QColor(0, 120, 215));  // 选中时蓝色
    } else {
        painter.setPen(Qt::black);  // 未选中时黑色
    }
    painter.drawText(rect1, Qt::AlignCenter, "1、入口拦截");
    
    // 绘制选项2：出口拦截
    QRect rect2(0, startY + lineHeight + 20, rect().width(), lineHeight);
    if (m_nCurrentIndex == 1) {
        painter.setPen(QColor(0, 120, 215));  // 选中时蓝色
    } else {
        painter.setPen(Qt::black);  // 未选中时黑色
    }
    painter.drawText(rect2, Qt::AlignCenter, "2、出口拦截");
}

void FormInterceptSelect::DrawHelpMessage(QPainter &painter)
{
    // 绘制底部帮助信息
    QFont fontHelp = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    painter.setFont(fontHelp);
    painter.setPen(Qt::black);
    
    int helpY = rect().height() - 100;
    QRect rctHelp(0, helpY, rect().width(), 80);
    QString helpText = "请按数字键选择  按【确认】键继续";
    painter.drawText(rctHelp, Qt::AlignCenter, helpText);
}

int FormInterceptSelect::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent || m_bProcessing) return 0;
    
    int keyCode = mtcKeyEvent->key();
    int keyFunc = mtcKeyEvent->func();
    
    // 优先处理功能键
    if (keyFunc == KeyConfirm) {
        ProcessEnterKey();
        return 1;
    }
    
    if (keyFunc == KeyEsc) {
        ProcessEscapeKey();
        return 1;
    }
    
    // 处理数字键和方向键
    if (keyCode == Key1 || keyCode == '1') {
        ProcessKey1();
    } else if (keyCode == Key2 || keyCode == '2') {
        ProcessKey2();
    } else if (keyCode == KeyUp) {
        ProcessUpKey();
    } else if (keyCode == KeyDown) {
        ProcessDownKey();
    } else {
        return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
    }
    
    return 1;
}

void FormInterceptSelect::OnModalShowed()
{
    CBaseOpWidget::OnModalShowed();
    
    // 设置焦点
    this->setFocus();
    
    DebugLog("拦截方式选择界面显示完成");
}

void FormInterceptSelect::ProcessKey1()
{
    SetCurrentSelection(0);
    DebugLog("用户按键选择：入口拦截");
}

void FormInterceptSelect::ProcessKey2()
{
    SetCurrentSelection(1);
    DebugLog("用户按键选择：出口拦截");
}

void FormInterceptSelect::ProcessUpKey()
{
    int newIndex = (m_nCurrentIndex > 0) ? m_nCurrentIndex - 1 : OPTION_COUNT - 1;
    SetCurrentSelection(newIndex);
    DebugLog(QString("用户上键选择：索引%1").arg(newIndex));
}

void FormInterceptSelect::ProcessDownKey()
{
    int newIndex = (m_nCurrentIndex < OPTION_COUNT - 1) ? m_nCurrentIndex + 1 : 0;
    SetCurrentSelection(newIndex);
    DebugLog(QString("用户下键选择：索引%1").arg(newIndex));
}

void FormInterceptSelect::ProcessEnterKey()
{
    ConfirmSelection();
}

void FormInterceptSelect::ProcessEscapeKey()
{
    CancelSelection();
}

void FormInterceptSelect::SetCurrentSelection(int index)
{
    if (index >= 0 && index < OPTION_COUNT) {
        m_nCurrentIndex = index;
        m_selectedType = INTERCEPT_OPTIONS[index].type;
        UpdateSelectionDisplay();
    }
}

void FormInterceptSelect::ConfirmSelection()
{
    if (m_bProcessing) return;
    
    m_bSelectionConfirmed = true;
    
    InfoLog(QString("确认选择拦截方式 - %1").arg(GetInterceptTypeName(m_selectedType)));
    
    ShowSuccessMessage(QString("已选择：%1").arg(GetInterceptTypeName(m_selectedType)));
    
    // 立即关闭界面
    OnOk();
}

void FormInterceptSelect::CancelSelection()
{
    InfoLog("用户取消拦截方式选择");
    OnCancel();
}

void FormInterceptSelect::UpdateSelectionDisplay()
{
    // 重绘界面以更新选中状态
    update();
}

void FormInterceptSelect::UpdateHelpMessage()
{
    // 帮助信息由paintEvent中的DrawHelpMessage处理
}

void FormInterceptSelect::SetUIEnabled(bool enabled)
{
    this->setEnabled(enabled);
}

void FormInterceptSelect::ShowErrorMessage(const QString &message)
{
    ErrorLog(QString("拦截方式选择界面错误：%1").arg(message));
    // 可以在这里显示错误提示，但暂时保持简单
}

void FormInterceptSelect::ShowSuccessMessage(const QString &message)
{
    InfoLog(QString("拦截方式选择界面成功：%1").arg(message));
    // 成功信息不需要显示，直接记录日志
}

void FormInterceptSelect::ShowWarningMessage(const QString &message)
{
    WarnLog(QString("拦截方式选择界面警告：%1").arg(message));
    // 警告信息不需要显示，直接记录日志
}

void FormInterceptSelect::ClearStatusMessage()
{
    // 不需要清理状态信息，因为不再使用控件
}

InterceptType FormInterceptSelect::GetInterceptTypeByIndex(int index) const
{
    if (index >= 0 && index < OPTION_COUNT) {
        return INTERCEPT_OPTIONS[index].type;
    }
    return Intercept_Exit; // 默认返回出口拦截
}

int FormInterceptSelect::GetIndexByInterceptType(InterceptType type) const
{
    for (int i = 0; i < OPTION_COUNT; i++) {
        if (INTERCEPT_OPTIONS[i].type == type) {
            return i;
        }
    }
    return 1; // 默认返回出口拦截的索引
}

QString FormInterceptSelect::GetInterceptTypeName(InterceptType type) const
{
    for (int i = 0; i < OPTION_COUNT; i++) {
        if (INTERCEPT_OPTIONS[i].type == type) {
            return INTERCEPT_OPTIONS[i].name;
        }
    }
    return "出口拦截"; // 默认名称
}

QString FormInterceptSelect::GetInterceptTypeDescription(InterceptType type) const
{
    for (int i = 0; i < OPTION_COUNT; i++) {
        if (INTERCEPT_OPTIONS[i].type == type) {
            return INTERCEPT_OPTIONS[i].description;
        }
    }
    return "在出口收费站进行车辆拦截处理"; // 默认描述
}

// 无延时操作
