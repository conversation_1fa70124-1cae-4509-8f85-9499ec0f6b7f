#ifndef MOBILEPAYDUAL_H
#define MOBILEPAYDUAL_H

#include "mobilepay.h"
#include <QLibrary>
#include "abstractdev.h"

#include <QTextCodec>

// Qt类前置声明
class QEventLoop;
class QTimer;
// Windows API 类型定义
#ifdef Q_OS_WIN
#include <windows.h>
#else
// 非Windows系统下的类型定义
typedef unsigned int UINT;
typedef void* HWND;
#endif

// 消息功能代码定义
#define MSGFUNC_DEBIT          1  // 扣款回馈
#define MSGFUNC_CANCEL         2  // 撤单回馈
#define MSGFUNC_UNATTENDED     3  // 无人值守
#define MSGFUNC_NONSTOP        4  // 不停车
#define MSGFUNC_PAY_STATUS     5  // 支付中间状态回馈
#define MSGFUNC_ETC_SPECIAL    6  // ETC特情处理回馈
#define MSGFUNC_CASH_RECEIPT   7  // 现金发票流水操作回馈

// 扣款状态代码
#define DEBIT_SUCCESS           0  // 扣费成功
#define DEBIT_INPUTTING_PWD     1  // 用户正在输入密码
#define DEBIT_ERR_NO_MONEY      2  // 失败：余额不足
#define DEBIT_ERR_INVALID_QR    3  // 失败：二维码非法
#define DEBIT_ERR_EXPIRED_QR    4  // 失败：二维码有效期非法
#define DEBIT_ERR_NO_USER       5  // 失败：硬件扫码未获取有效用户信息
#define DEBIT_ERR_TIMEOUT       6  // 失败：扣款超时
#define DEBIT_ERR_WRONG_PWD     7  // 失败：密码输入错误
#define DEBIT_ERR_PARAM        8  // 失败：传入参数异常
#define DEBIT_ERR_LAST_TRADE    9  // 失败：上次交易未完成
#define DEBIT_ERR_NO_INIT      10  // 失败：组件未初始化
#define DEBIT_ERR_NO_AUTH      11  // 失败：组件未经授权
#define DEBIT_ERR_NETWORK      12  // 失败：支付网络出现故障
#define DEBIT_GOT_CERTIFICATE  13  // 已获取支付凭证
#define DEBIT_ERR_INTERNAL    255  // 失败：内部错误

// 定义消息结构
/**
 * @brief 设备消息结构体
 * @details 存储消息解析结果
 */
struct DeviceMessage {
    int functionCode;   ///< 功能代码（WPARAM的第1个字节）
    int statusCode;    ///< 执行状态（WPARAM的第2个字节）
    QByteArray data;   ///< 消息数据（LPARAM指向的数据）
};

/**
 * @brief 转换状态码为可读消息
 * @param funcCode 功能代码
 * @param statusCode 状态码
 * @return 状态描述文本
 */
inline QString statusToString(int funcCode, int statusCode) {
    // 根据功能代码和状态码返回描述文本
    switch (funcCode) {
        case MSGFUNC_DEBIT:
            switch (statusCode) {
                case DEBIT_SUCCESS: return QString::fromUtf8("扣款成功");
                case DEBIT_INPUTTING_PWD: return QString::fromUtf8("用户正在输入密码");
                case DEBIT_ERR_NO_MONEY: return QString::fromUtf8("余额不足");
                case DEBIT_ERR_INVALID_QR: return QString::fromUtf8("二维码非法");
                case DEBIT_ERR_EXPIRED_QR: return QString::fromUtf8("二维码有效期非法");
                case DEBIT_ERR_NO_USER: return QString::fromUtf8("未获取有效用户信息");
                case DEBIT_ERR_TIMEOUT: return QString::fromUtf8("扣款超时");
                case DEBIT_ERR_WRONG_PWD: return QString::fromUtf8("密码输入错误");
                case DEBIT_ERR_PARAM: return QString::fromUtf8("参数异常");
                case DEBIT_ERR_LAST_TRADE: return QString::fromUtf8("上次交易未完成");
                case DEBIT_ERR_NO_INIT: return QString::fromUtf8("组件未初始化");
                case DEBIT_ERR_NO_AUTH: return QString::fromUtf8("组件未经授权");
                case DEBIT_ERR_NETWORK: return QString::fromUtf8("支付网络出现故障");
                case DEBIT_GOT_CERTIFICATE: return QString::fromUtf8("已获取支付凭证");
                case DEBIT_ERR_INTERNAL: return QString::fromUtf8("内部错误");
                default: return QString::fromUtf8("未知状态码") + QString::number(statusCode);
            }
        case MSGFUNC_CANCEL:
            switch (statusCode) {
                case 0: return QString::fromUtf8("撤单成功");
                default: return QString::fromUtf8("撤单失败，状态码") + QString::number(statusCode);
            }
        default:
            return QString::fromUtf8("功能码") + QString::number(funcCode) + 
                   QString::fromUtf8(", 状态码") + QString::number(statusCode);
    }
}



// 定义函数指针类型
typedef bool (__stdcall *FUN_InitEnvironment)(const UINT& nThreadID, const HWND& hWnd, const unsigned int& nNotifyMsgID, const char* szAreaInfo, const char* szLoaclStation, const char* szLoaclLaneID, const char* szServerInfo, const int& iProvinceID);
typedef bool (__stdcall *FUN_Destroy)();
typedef bool (__stdcall *FUN_DebitMoney)(const char* szParamContext, const int& iParamSize, const int& iFormatType);
typedef bool (__stdcall *FUN_DebitCancel)(const char* szParamContext, const int& iParamSize, const int& iFormatType);
typedef bool (__stdcall *FUN_GetDebitResult)(char* szParamContext, int& iParamSize, const int& iFormatType);

/**
 * @brief 双链路支付设备初始化配置结构体
 * @details 用于存储初始化设备所需的配置参数
 */
struct MobilePayDualConfig
{
    unsigned int nNotifyMsgID;  ///< 消息通知ID
    QString szAreaInfo;         ///< 区域信息，格式：区域编码#路段编码
    QString szLoaclStation;     ///< 站点信息，格式：站号#站名
    QString szLoaclLaneID;      ///< 车道号
    QString szServerInfo;       ///< 服务器信息，格式：服务器类型#服务器IP地址#服务器开放端口
    int iProvinceID;            ///< 省份编码
    
    /**
     * @brief 构造函数
     * @details 设置默认配置值，确保中文使用GBK编码
     */
    MobilePayDualConfig()
    {
        nNotifyMsgID = WM_USER + 100;
        
        // 使用QTextCodec确保字符串使用GBK编码
        //QTextCodec *codec = QTextCodec::codecForName("GBK");
        
        // 对于纯ASCII字符串，直接赋值即可
        szAreaInfo = "3601#3611100";
        
        // 对于包含中文的字符串，需要明确指定编码
        // 这里假设源代码文件是以GBK编码保存的
        szLoaclStation = QString::fromUtf8("3611108#赣县北");
        
        szLoaclLaneID = "101";
        szServerInfo = "1#*************#8080";
        iProvinceID = 36;
    }
};

/*
 * 双链路支付设备
 * 通过DLL实现支付功能
 */
class MobilePayDual : public MobilePayBase
{
    Q_OBJECT
public:
    explicit MobilePayDual();
    virtual ~MobilePayDual();
    
    /**
     * @brief 处理Windows原生消息（Qt 4.8.5兼容版本）
     * @param message 消息指针
     * @param result 处理结果
     * @return 是否已处理该消息
     */
    bool processNativeMessage(void *message, long *result);

public:
    // 设备管理接口实现
    virtual bool StartDev();
    virtual void CloseDev();
    virtual bool LoadDriver();
    virtual void ReleaseDriver();

    // 支付接口实现
    virtual bool Pay(const MBPay_PayRequest& payRequest, TW_RESULT& resp);
    virtual bool QueryOrder(QString payIdentifier, quint32 submitCount, TW_RESULT& resp);
    virtual bool Reverse(QString payIdentifier, quint32 submitCount, TW_RESULT& payResult);

    /**
     * @brief 检查驱动是否已加载
     * @return 已加载返回true，否则返回false
     */
    bool IsDriverLoaded() const { return m_bDriverLoaded; }
    
    /**
     * @brief 检查设备是否已打开
     * @return 已打开返回true，否则返回false
     */
    bool IsDeviceOpen() const { return m_bDevInited; }
    
    /**
     * @brief 设置双链路支付设备配置
     * @param config 配置参数
     */
    void SetConfig(const MobilePayDualConfig& config) { m_config = config; }
    
    /**
     * @brief 获取当前配置
     * @return 当前配置参数
     */
    MobilePayDualConfig GetConfig() const { return m_config; }

// 确保信号是public的
signals:
    /**
     * @brief 支付结果信号
     * @param result 支付结果，JSON格式字符串
     */
    void debitResultSignal(QString result);
    
    /**
     * @brief 设备状态变化信号
     * @param funcCode 功能代码
     * @param statusCode 状态码
     * @param message 状态描述
     */
    void deviceStatusSignal(int funcCode, int statusCode, QString message);

private slots:
    /**
     * @brief 处理设备消息
     * @param funcCode 功能代码
     * @param statusCode 状态码
     * @param data 消息数据
     */
    void handleDeviceMessage(int funcCode, int statusCode, QByteArray data);
    
    /**
     * @brief 处理异步支付结果（用于同步等待）
     * @param result 支付结果JSON字符串
     */
    void onAsyncPayResult(QString result);
    
    /**
     * @brief 处理设备状态消息（用于同步等待）
     * @param funcCode 功能代码
     * @param statusCode 状态代码
     * @param message 状态描述
     */
    void onAsyncDeviceStatus(int funcCode, int statusCode, QString message);

// 内部信号（仅用于同步等待机制）
signals:
    /**
     * @brief 内部结果信号（用于同步等待）
     * @param result 支付结果JSON字符串
     */
    void internalResultSignal(QString result);
    
    /**
     * @brief 内部状态信号（用于同步等待）
     * @param funcCode 功能代码
     * @param statusCode 状态代码
     * @param message 状态描述
     */
    void internalStatusSignal(int funcCode, int statusCode, QString message);

private:
    // DLL加载状态
    static bool m_bDriverLoaded;
    static QLibrary m_hLibModule;
    bool m_bDevInited;
    MobilePayDualConfig m_config;  ///< 设备配置参数
    
    // 消息处理相关
    unsigned int m_nNotifyMsgID;  ///< 接收消息的ID

    // DLL函数指针
    FUN_InitEnvironment m_pFun_InitEnvironment;
    FUN_Destroy m_pFun_Destroy;
    FUN_DebitMoney m_pFun_DebitMoney;
    FUN_DebitCancel m_pFun_DebitCancel;
    FUN_GetDebitResult m_pFun_GetDebitResult;
    
    // 同步等待机制
    QEventLoop* m_pEventLoop;           ///< 事件循环，用于同步等待
    QTimer* m_pTimeoutTimer;            ///< 超时定时器
    TW_RESULT m_asyncResult;            ///< 异步结果存储
    bool m_bWaitingResult;              ///< 是否正在等待结果
    QString m_currentPayIdentifier;     ///< 当前支付标识符

    // 辅助方法
    QByteArray convertPayJson(const MBPay_PayRequest& payRequest);
    QString convertQueryJson(QString payIdentifier, quint32 submitCount);
    QString convertReverseJson(QString payIdentifier, quint32 submitCount);
public:
    bool parseResultJson(const QString& jsonResult, TW_RESULT& result);
};

#endif // MOBILEPAYDUAL_H
