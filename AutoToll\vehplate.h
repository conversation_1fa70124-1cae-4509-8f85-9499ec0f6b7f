﻿#ifndef VEHPLATE_H
#define VEHPLATE_H

#include <QWidget>

namespace Ui {
class VehPlate;
}

class VehPlate : public QWidget
{
    Q_OBJECT

public:
    explicit VehPlate(QWidget *parent = 0);
    ~VehPlate();
    void InitUI();
    void ShowPic(const QString &name);
    void ChangPic();
    void showVehpic(const QString &picpath);
    void ClearPic();
private:
    Ui::VehPlate *ui;
    QString Path;
private:
    void paintEvent(QPaintEvent *);

};

#endif // VEHPLATE_H
