#include "FormRepaySuccess.h"
#include <QPainter>
#include <QFont>
#include "../../globalui.h"
#include "../../dlgmain.h"
#include "../../mtckey/MtcKeyDef.h"
#include "../common/vehplatefunc.h"

FormRepaySuccess::FormRepaySuccess(QWidget *parent)
    : CBaseOpWidget(parent)
    , m_pLblTitle(nullptr)
    , m_pLblVehInfo(nullptr)
    , m_pLblPayResult(nullptr)
    , m_pLblHelpInfo(nullptr)
    , m_vehPlateColor(1)
    , m_amount(0)
    , m_pAutoCloseTimer(nullptr)
    , m_autoCloseSeconds(0)
    , m_originalAutoCloseSeconds(0)
{
    // 创建控件
    m_pLblTitle = new QLabel(this);
    m_pLblVehInfo = new QLabel(this);
    m_pLblPayResult = new QLabel(this);
    m_pLblHelpInfo = new QLabel(this);
    
    // 创建自动关闭定时器
    m_pAutoCloseTimer = new QTimer(this);
    connect(m_pAutoCloseTimer, SIGNAL(timeout()), this, SLOT(OnAutoCloseTimer()));
    
    // 设置对象名称
    setObjectName(QString("FormRepaySuccess"));
    
    // 过滤子控件的键盘事件
    filterChildrenKeyEvent();
}

FormRepaySuccess::~FormRepaySuccess()
{
    if (m_pAutoCloseTimer) {
        m_pAutoCloseTimer->stop();
        delete m_pAutoCloseTimer;
        m_pAutoCloseTimer = nullptr;
    }
    
    delete m_pLblTitle;
    delete m_pLblVehInfo;
    delete m_pLblPayResult;
    delete m_pLblHelpInfo;
}

bool FormRepaySuccess::ShowRepaySuccess(const QString &repayTypeName, 
                                       const QString &vehPlate, 
                                       int vehPlateColor,
                                       const QString &payTypeName, 
                                       int amount,
                                       int autoCloseSeconds)
{
    // 保存显示数据
    m_repayTypeName = repayTypeName;
    m_vehPlate = vehPlate;
    m_vehPlateColor = vehPlateColor;
    m_payTypeName = payTypeName;
    m_amount = amount;
    m_autoCloseSeconds = autoCloseSeconds;
    m_originalAutoCloseSeconds = autoCloseSeconds;
    
    // 初始化界面
    InitUI();
    
    // 设置控件内容
    m_pLblTitle->setText(QString("【%1】操作").arg(repayTypeName));
    
    QString vehColorName = GetVehPlateColorName(vehPlateColor);
    m_pLblVehInfo->setText(QString("车辆【%1%2】").arg(vehColorName).arg(vehPlate));
    
    double amountYuan = amount / 100.0;
    m_pLblPayResult->setText(QString("【%1 %2元】支付成功").arg(payTypeName).arg(amountYuan, 0, 'f', 2));
    
    // 设置操作提示
    if (autoCloseSeconds > 0) {
        m_pLblHelpInfo->setText(QString("按【确定】或【ESC】键退出（%1秒后自动关闭）").arg(autoCloseSeconds));
        // 启动自动关闭定时器
        m_pAutoCloseTimer->start(1000); // 每秒更新一次
    } else {
        m_pLblHelpInfo->setText("按【确定】或【ESC】键退出");
    }
    
    InfoLog(QString("显示补费成功界面 - 方式:%1, 车牌:%2%3, 支付:%4, 金额:%5元")
            .arg(repayTypeName).arg(vehColorName).arg(vehPlate).arg(payTypeName).arg(amountYuan, 0, 'f', 2));
    
    // 显示模态对话框
    return (CBaseOpWidget::Rlt_OK == doModalShow());
}

void FormRepaySuccess::InitUI()
{
    CBaseOpWidget::InitUI();
    
    // 设置字体
    QFont fontTitle = QFont(g_GlobalUI.m_FontName, 24);    // 标题字体减小2号
    QFont fontVehInfo = QFont(g_GlobalUI.m_FontName, 20);  // 车辆信息字体小1号
    QFont fontContent = QFont(g_GlobalUI.m_FontName, 20);  // 内容字体减小2号
    QFont fontHelp = QFont(g_GlobalUI.m_FontName, 14);     // 帮助字体减小2号
    
    // 设置标题标签属性（取消加粗）
    m_pLblTitle->setFont(fontTitle);
    m_pLblTitle->setAlignment(Qt::AlignCenter);
    m_pLblTitle->setStyleSheet("QLabel { color: black; }");
    
    // 设置车辆信息标签属性
    m_pLblVehInfo->setFont(fontVehInfo);
    m_pLblVehInfo->setAlignment(Qt::AlignCenter);
    m_pLblVehInfo->setStyleSheet("QLabel { color: black; }");
    
    // 设置支付结果标签属性
    m_pLblPayResult->setFont(fontContent);
    m_pLblPayResult->setAlignment(Qt::AlignCenter);
    m_pLblPayResult->setStyleSheet("QLabel { color: green; }");
    
    // 设置帮助信息标签属性
    m_pLblHelpInfo->setFont(fontHelp);
    m_pLblHelpInfo->setAlignment(Qt::AlignCenter);
    m_pLblHelpInfo->setStyleSheet("QLabel { color: black; }");
    
    // 计算控件位置
    int centerX = rect().width() / 2;
    int contentWidth = rect().width() - 40; // 左右各留20像素边距
    int lineHeight = 60;
    
    // 标题位置 - 在窗口上部
    int titleY = g_GlobalUI.optw_TitleHeight + 40;
    QRect titleRect(20, titleY, contentWidth, lineHeight);
    m_pLblTitle->setGeometry(titleRect);
    
    // 车辆信息位置 - 上移到蓝框区域
    int vehInfoY = g_GlobalUI.optw_TitleHeight + 120;  // 直接定位到标题栏下方较近位置
    QRect vehInfoRect(20, vehInfoY, contentWidth, lineHeight);
    m_pLblVehInfo->setGeometry(vehInfoRect);
    
    // 支付结果位置 - 紧跟车辆信息下方
    int payResultY = vehInfoY + lineHeight + 20;
    QRect payResultRect(20, payResultY, contentWidth, lineHeight);
    m_pLblPayResult->setGeometry(payResultRect);
    
    // 帮助信息位置 - 在窗口下部
    int helpY = rect().height() - 80;
    QRect helpRect(20, helpY, contentWidth, 40);
    m_pLblHelpInfo->setGeometry(helpRect);
}

int FormRepaySuccess::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) {
        return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
    }
    
    int keyFunc = mtcKeyEvent->func();
    
    // 处理确认键
    if (keyFunc == KeyConfirm) {
        InfoLog("用户确认补费成功信息");
        OnOk();
        return 1;
    }
    
    // 处理取消键
    if (keyFunc == KeyEsc) {
        InfoLog("用户关闭补费成功界面");
        OnOk(); // ESC键也返回OK，因为操作已经成功
        return 1;
    }
    
    return CBaseOpWidget::mtcKeyPressed(mtcKeyEvent);
}

void FormRepaySuccess::paintEvent(QPaintEvent *)
{
    QRect rectClient = this->rect();
    QPixmap pixmap(rectClient.width(), rectClient.height());
    QPainter painter(&pixmap);
    
    // 绘制背景色
    painter.setBrush(g_GlobalUI.m_ColorBackground);
    painter.setPen(Qt::black);
    painter.drawRect(rectClient.x(), rectClient.y(), rectClient.width() - 1, rectClient.height() - 1);
    
    // 去掉标题栏文字显示
    // QFont fontTitleBar = QFont(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    // QRect rctTitleBar(0, 0, rect().width(), g_GlobalUI.optw_TitleHeight);
    // painter.setFont(fontTitleBar);
    // painter.drawText(rctTitleBar, Qt::AlignCenter, "补费成功");
    
    // 取消装饰线条
    // painter.setPen(QPen(Qt::gray, 2));
    // int lineY1 = m_pLblTitle->geometry().bottom() + 10;
    // painter.drawLine(40, lineY1, rect().width() - 40, lineY1);
    // 
    // int lineY2 = m_pLblVehInfo->geometry().bottom() + 10;
    // painter.drawLine(40, lineY2, rect().width() - 40, lineY2);
    
    // 绘制成功图标或装饰（可选）
    painter.setPen(QPen(Qt::green, 3));
    painter.setBrush(Qt::NoBrush);
    int iconX = rect().width() / 2 - 15;
    int iconY = m_pLblPayResult->geometry().center().y() - 15;
    painter.drawEllipse(iconX, iconY, 30, 30);
    
    // 绘制对勾
    painter.setPen(QPen(Qt::green, 4));
    painter.drawLine(iconX + 8, iconY + 15, iconX + 13, iconY + 20);
    painter.drawLine(iconX + 13, iconY + 20, iconX + 22, iconY + 10);
    
    painter.end();
    QPainter ptThis(this);
    ptThis.drawPixmap(rectClient, pixmap);
}

void FormRepaySuccess::OnAutoCloseTimer()
{
    if (m_autoCloseSeconds > 0) {
        m_autoCloseSeconds--;
        m_pLblHelpInfo->setText(QString("按【确定】或【ESC】键退出（%1秒后自动关闭）").arg(m_autoCloseSeconds));
        
        if (m_autoCloseSeconds <= 0) {
            m_pAutoCloseTimer->stop();
            InfoLog("补费成功界面自动关闭");
            OnOk();
        }
    } else {
        m_pAutoCloseTimer->stop();
    }
}
