#ifndef VEHINFOPANEL_H
#define VEHINFOPANEL_H

#include <QWidget>
#include <QDateTime>
#include "lanetype.h"
//#include "AbstractUI.h"
//#include "VCRDev.h"

namespace Ui {
class VehInfoPanel;
}

class VehInfoPanel : public QWidget,public CTransInfoUI
{
    Q_OBJECT

public:
    explicit VehInfoPanel(QWidget *parent = 0);
    virtual ~VehInfoPanel();

private:
    Ui::VehInfoPanel *ui;

private:
    //初始化界面显示
    void InitUIControls(qint32 nLaneType);
public:
    //公共接口
    virtual void ShowUI();
    virtual void HideUI();
    virtual void ClearTransInfo();
    virtual void SetOBUID(const char *szOBUID);
    virtual void SetVehInfo(const CVehInfo &vehInfo);
    virtual void SetEntryInfo(const QDateTime &entryTime,const QString &sEntryStation);
    virtual void SetCardInfo(const CProCardBasicInfo &ProCardBasicInfo,quint32 dwBalance);
    virtual void SetOpResult(const QDateTime &transTime,quint32 dwMoney,quint32 dwBalance);

public:
    //初始化
    qint32 Init(qint32 nLaneType);
    //显示通行费，单位元
    void ShowMoney(quint32 dwMoney);
    //显示余额，单位(分)
    void ShowBalance(qint8 bCardType,quint32 dwBalance );
    void ClearVehInfo();

    //显示OBU
    void ShowOBU(const QString& obu);
    //显示卡号
    void ShowCardID(const QString& cardid);
    //显示入口站
    void ShowEnStation(const QString& station);
    //显示业务时间
    void ShowWasteTime(const QDateTime* datetime);
    //显示卡类型TODO 换成编码
    void ShowCardType(const QString& cardtype);
    //显示入口时间
    void ShowEnTime(const QDateTime* datetime);
    //
    void ShowVehInfo(const CVehInfo &vehInfo);
    void ShowAutoVehInfo(const CVehInfo &vehInfo);
    //车型车牌不符提示显示
    void ShowWarning(QString warningText=QString(""));
    //车型车牌不符提示关闭
    void HideWarning();
    void CheckVehInfo();
    
    void ShowAutoVehClass(CVehClass vehClass);
private slots:
    void ontWarningTimeout();
private:
    QPixmap *p_bluePlateColor;
    QPixmap *p_blackPlateColor;
    QPixmap *p_yellowPlateColor;
    QPixmap *p_whitePlateColor;
    QTimer m_tWarning;
    QString m_sWarningText;
    int m_nWarningFlag;
    CVehInfo  m_curVehInfo;
    quint8 m_bCardType;
};

#endif // VEHINFOPANEL_H
