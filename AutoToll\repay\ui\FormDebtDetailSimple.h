#ifndef FORMDEBTDETAILSIMPLE_H
#define FORMDEBTDETAILSIMPLE_H

#include <QLabel>
#include <QTimer>
#include <QPainter>
#include <QKeyEvent>
#include <QVBoxLayout>
#include <QHBoxLayout>

#include "../../baseopwidget.h"
#include "../../MtcKey/MtcKeyDef.h"
#include "../../globalui.h"
#include "../common/repaytypes.h"

/**
 * @brief 简化版债务详情显示界面
 * 按照图片样式显示当前车辆的追收信息，支持上下键选择和确认
 */
class FormDebtDetailSimple : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormDebtDetailSimple(QWidget *parent = 0);
    ~FormDebtDetailSimple();

    // 初始化界面
    virtual void InitUI(int iFlag = 0);
    
    // 显示债务详情界面
    bool ShowDebtDetail(const RepayDebtQueryResult &result);
    
    // 获取总补费金额（所有债务项目的总和）
    int GetTotalRepayAmount() const;
    
    // 获取选中的项目（用于显示）
    RepayDebtItem GetSelectedItem() const;

protected:
    // 重写绘制事件
    void paintEvent(QPaintEvent *event);
    
    // 重写按键事件处理
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    
    // 重写模态显示后的处理
    virtual void OnModalShowed();

private:
    // 界面初始化
    void InitUIConfig();
    void InitConnections();
    
    // 绘制函数
    void DrawBackground(QPainter &painter);
    void DrawTitle(QPainter &painter);
    void DrawVehicleInfo(QPainter &painter);
    void DrawDebtList(QPainter &painter);
    void DrawReason(QPainter &painter);
    void DrawHelpMessage(QPainter &painter);
    
    // 数据处理
    void SetDebtResult(const RepayDebtQueryResult &result);
    QString FormatAmount(int amount) const;
    QString FormatDateTime(const QString &datetime) const;
    QString TruncateStationName(const QString &name, int maxLength = 8) const;
    
    // 选择处理
    void MoveSelectionUp();
    void MoveSelectionDown();
    void ConfirmSelection();
    void CancelSelection();
    void UpdateSelectionDisplay();
    
    // 输入处理
    void ProcessUpKey();
    void ProcessDownKey();
    void ProcessEnterKey();
    void ProcessEscapeKey();
    
    // 界面状态控制
    void SetUIEnabled(bool enabled);

    // 错误/提示处理
    void ShowWarningMessage(const QString &message);

private:
    // 数据
    RepayDebtQueryResult m_debtResult;     // 欠费查询结果
    
    // 界面状态
    int m_nCurrentIndex;              // 当前选中项索引
    bool m_bSelectionConfirmed;       // 是否已确认选择
    bool m_bProcessing;               // 是否正在处理
    
    // 界面配置
    QFont m_fontTitle;                // 标题字体
    QFont m_fontHeader;               // 表头字体
    QFont m_fontItem;                 // 明细项字体
    QFont m_fontHelp;                 // 帮助信息字体
    
    // 布局配置
    static const int TITLE_HEIGHT = 60;           // 标题高度
    static const int VEHICLE_INFO_HEIGHT = 40;    // 车辆信息区高度
    static const int HEADER_HEIGHT = 40;          // 表头高度
    static const int ITEM_HEIGHT = 50;            // 明细项高度
    static const int REASON_HEIGHT = 60;          // 原因描述区高度
    static const int HELP_HEIGHT = 50;            // 帮助区高度
    static const int MARGIN = 10;                 // 边距
    static const int SPACING = 5;                 // 间距
    
    // 显示配置
    static const int MAX_STATION_NAME_LENGTH = 8;  // 站点名称最大显示长度
    static const int MAX_VISIBLE_ITEMS = 2;        // 最大可见明细项数（根据图片显示2条）
};

#endif // FORMDEBTDETAILSIMPLE_H
