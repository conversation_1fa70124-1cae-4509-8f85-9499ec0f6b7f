#ifndef UNLOGINDLG_H
#define UNLOGINDLG_H

#include "QLabel"
#include "baseopwidget.h"

namespace Ui {
  class CUnLoginDlg;
}

class CUnLoginDlg : public CBaseOpWidget
{
    Q_OBJECT
public:

public:
    explicit CUnLoginDlg(QWidget *parent = 0);
    ~CUnLoginDlg();
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);

private:
    void InitUI();

private:
    QLabel *m_lblTitle;
    QLabel *m_labelMsg;
    QLabel *m_labelOK;
    QLabel *m_labelCancel;

};

#endif // UNLOGINDLG_H
