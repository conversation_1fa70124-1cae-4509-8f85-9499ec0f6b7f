#include "sernum.h"
#include "QNetworkInterface"
#include <QDateTime>
#include "globalutils.h"
#include "trides.h"
SerNum::SerNum()
{
    m_SeedIndex[0]=0;
	m_SeedIndex[1]=1;
	m_SeedIndex[2]=0;
	m_SeedIndex[3]=0;
	m_SeedIndex[4]=1;
	m_SeedIndex[5]=1;

	m_Key[0]=0x78;
	m_Key[1]=0x2c;
	m_Key[2]=0x49;
	m_Key[3]=0xe7;
	m_Key[4]=0xb9;
	m_Key[5]=0xef;
	m_Key[6]=0xd6;
	m_Key[7]=0x44;

	m_Key[8]=0x23;
	m_Key[9]=0x18;
	m_Key[10]=0xa5;
	m_Key[11]=0xde;
	m_Key[12]=0xdb;
	m_Key[13]=0x7d;
	m_Key[14]=0xa9;
	m_Key[15]=0x2c;
}

SerNum::~SerNum()
{
    
}
//加载本地硬件信息
bool SerNum::LoadAdapterInfo(QString &sError)
{
    Q_UNUSED(sError)
    CAdapterInfo adapterInfo;
    QByteArray qba=GetHostIPAddress().toLatin1();
    strcpy(adapterInfo.szIP,qba.data());
    QString sMac=GetHostMACAddress().replace(":","");
    Hex2Raw(adapterInfo.Mac,sMac.toLatin1().data(),12);

    m_Adapters.push_back(adapterInfo);

    if(m_Adapters.count()>0)
        return true;
    else
        return false;
}

bool SerNum::BuildUserInfo(int nOrgCode, char szUserInfo[], QString &sError)
{
    if(nOrgCode==0)
    {
        sError="机构代码不能为空";
        return false;
    }
    unsigned char sMac[8];
    if(0==m_Adapters.size())
    {
        sError="本机MAC地址空";
        return false;
    }
    CAdapterInfo it=m_Adapters.at(0);
    memcpy(sMac,it.Mac,sizeof(it.Mac));
    uchar OrgCodeRaw[4];
    memset(OrgCodeRaw,0,sizeof OrgCodeRaw);
    memcpy(OrgCodeRaw,&nOrgCode,sizeof nOrgCode);
    //OrgCodeRaw=QByteArray(&nOrgCode);
    for(int i=0;i<2;i++)
    {
        OrgCodeRaw[i]=OrgCodeRaw[i]^OrgCodeRaw[i+2];
    }
    uchar UserInfoRaw[8];
    memset(UserInfoRaw,0,sizeof UserInfoRaw);
    unsigned short nRand=GetRand();
    memcpy(UserInfoRaw,&nRand,2);
    memcpy(UserInfoRaw+2,OrgCodeRaw,2);
    memcpy(UserInfoRaw+4,sMac+2,4);
    memcpy(m_UserInfoRaw,UserInfoRaw,sizeof UserInfoRaw);
    for(int i=0;i<6;i++)
        UserInfoRaw[i+2]=UserInfoRaw[i+2]^UserInfoRaw[uchar(m_SeedIndex[i])];
    Raw2HexStr(szUserInfo,17,UserInfoRaw,8);
    return true;
}

bool SerNum::BuildSeriNo(int nOrgCode, const char *szUserInfo, int nUserInfo, char szSeriNo[], QString &sError)
{
    Q_UNUSED(nUserInfo)
    uchar UserInfoRaw[8];
    memset(UserInfoRaw,0,8);
    if(!UnPackUserInfo(nOrgCode,szUserInfo,17,UserInfoRaw,sError))
    {
        return false;
    }
    //unsigned int nRand=GetRand();
    //memcpy(UserInfoRaw,&nRand,2);
    for(int i=0;i<6;i++)
    {
        UserInfoRaw[i+2]=UserInfoRaw[i+2]^UserInfoRaw[uchar(m_SeedIndex[i])];
    }
    char EnData[16];
    memset(EnData,0,sizeof EnData);
    unsigned long nPadLen=sizeof EnData;
    int nRlt=RunPad(0,(char*)UserInfoRaw,8,EnData,&nPadLen);
    if(0!=nRlt)
    {
        sError="RunPad Error";
        return false;
    }
    uchar sOut[8];
    memset(sOut,0,sizeof sOut);
    nRlt=RunDes(EN_CRYPT,ECB,EnData,(char*)sOut,nPadLen,(char*)m_Key,16);
    if(0!=nRlt)
    {
        sError="RunDes Error";
        return false;
    }
    Raw2HexStr(szSeriNo,17,sOut,8);
    return true;
}

bool SerNum::CheckSeriNo(int nOrgCode, const char *szSeriNo, int nSeriNoLen, QString &sError)
{
    Q_UNUSED(nSeriNoLen)
    uchar UserInfoRaw[8];
    memset(UserInfoRaw,0,sizeof UserInfoRaw);
    if(!UnPackSeriNo(nOrgCode,szSeriNo,17,UserInfoRaw,sError))
    {
        return false;
    }
    for(int i=0;i<m_Adapters.count();i++)
    {
        if(!memcmp(UserInfoRaw+4,m_Adapters.at(i).Mac+2,4))
        {
            return true;
        }
    }
    sError="注册错误";
    return false;
}

bool SerNum::UnPackUserInfo(int nOrgCode, const char *szUserInfo, int nUserInfoLen, uchar* UserInfoRaw, QString &sError)
{
    Q_UNUSED(nUserInfoLen)
    uchar OrgCodeRaw[4];
    memset(OrgCodeRaw,0,sizeof OrgCodeRaw);
    uchar TemUserInfoRaw[8];
    memset(TemUserInfoRaw,0,sizeof TemUserInfoRaw);

    memcpy(OrgCodeRaw,&nOrgCode,sizeof nOrgCode);
    Hex2Raw(TemUserInfoRaw,szUserInfo,16);
    for(int i=0;i<6;++i)
    {
        TemUserInfoRaw[i+2]=TemUserInfoRaw[i+2]^TemUserInfoRaw[uchar(m_SeedIndex[i])];
    }
    uchar temOrgCode[4];
    memset(temOrgCode,0,sizeof temOrgCode);
    for(int i=0;i<2;++i)
    {
        temOrgCode[i]=TemUserInfoRaw[i+2]^OrgCodeRaw[i+2];
        temOrgCode[i+2]=TemUserInfoRaw[i+2]^OrgCodeRaw[i];
    }
    int nTemOrgCode=0;
    memcpy(&nTemOrgCode,temOrgCode,4);
    if(nTemOrgCode!=nOrgCode)
    {
        sError="站代码或车道代码与机器代码不匹配";
        return false;
    }
    memcpy(UserInfoRaw,TemUserInfoRaw,8);
    return true;
}

bool SerNum::UnPackSeriNo(int nOrgCode, const char *szSeriNo, int nSeriNoLen, uchar UserInfoRaw[8], QString &sError)
{
    uchar SerNoRaw[8];
    memset(SerNoRaw,0,sizeof SerNoRaw);
    Hex2Raw(SerNoRaw,szSeriNo,nSeriNoLen);
    int nRet=RunDes(DE_CRYPT,ECB,(char*)SerNoRaw,(char*)UserInfoRaw,8,(char*)m_Key,16);
    if(0!=nRet)
    {
        sError="DeCrypt Error";
        return false;
    }
    char sUserInfo[17];
    memset(sUserInfo,0,sizeof sUserInfo);
    Raw2HexStr(sUserInfo,17,UserInfoRaw,8);
    if(!UnPackUserInfo(nOrgCode,sUserInfo,sizeof sUserInfo,UserInfoRaw,sError))
    {
        return false;
    }
    return true;
}

QString SerNum::GetHostIPAddress()
{
    QString strIpAddress;
    QList<QHostAddress> ipaddrlist=QNetworkInterface::allAddresses();
    int nListCount=ipaddrlist.size();
    for(int i=0;i<nListCount;++i)
    {
        if(ipaddrlist.at(i)!=QHostAddress::LocalHost&&
           ipaddrlist.at(i).toIPv4Address()){
            strIpAddress=ipaddrlist.at(i).toString();
            break;
        }
    }
    if(strIpAddress.isEmpty())
    {
        strIpAddress=QHostAddress(QHostAddress::LocalHost).toString();
    }
    return strIpAddress;
}

QString SerNum::GetHostMACAddress()
{
    QList<QNetworkInterface> nets=QNetworkInterface::allInterfaces();
    int nCnt=nets.count();
    QString strMacAddr="";
    for(int i=0;i<nCnt;++i)
    {
        if(nets[i].flags().testFlag(QNetworkInterface::IsUp) &&
           nets[i].flags().testFlag(QNetworkInterface::IsRunning) &&
           !nets[i].flags().testFlag(QNetworkInterface::IsLoopBack))
        {
            strMacAddr = nets[i].hardwareAddress();
            break;
        }
    }
    return strMacAddr;
}

unsigned short SerNum::GetRand()
{
    QDateTime curTime=QDateTime::currentDateTime();
    int nTime=curTime.toTime_t();
    unsigned int nSeed=(uint)(nTime%0xffffffff);
    qsrand(nSeed);
    unsigned int nRand=qrand();
    return nRand%0xffff;
}

