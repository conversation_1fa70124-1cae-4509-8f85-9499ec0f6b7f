#ifndef VEHICLEAXISCHECK_H
#define VEHICLEAXISCHECK_H

#include <QObject>
#include <QString>

/**
 * @brief 货车轴型判断模块
 * 用于处理货车轴型校验、车型库查询等功能
 */
class VehicleAxisCheck : public QObject
{
    Q_OBJECT
public:
    explicit VehicleAxisCheck(QObject *parent = 0);
    ~VehicleAxisCheck();

    /**
     * @brief 校验非ETC普通货车轴型
     * @param scaleAxisCount 地磅轴数
     * @param cameraAxisCount 车型识别设备轴数
     * @param dbAxisCount 车型库轴数
     * @return bool 校验结果
     */
    bool checkNormalTruckAxis(int scaleAxisCount, int cameraAxisCount, int dbAxisCount);

    /**
     * @brief 校验ETC普通货车轴型
     * @param scaleAxisCount 地磅轴数
     * @param obuAxisCount OBU发行车型对应轴数
     * @param dbAxisCount 车型库轴数
     * @return bool 校验结果
     */
    bool checkETCTruckAxis(int scaleAxisCount, int obuAxisCount, int dbAxisCount);

    /**
     * @brief 获取车型库信息
     * @param vehicleType 车型
     * @return QString 车型库信息，格式：车型|车种|轴数|轴型
     */
    QString getVehicleTypeDbInfo(int vehicleType);

    /**
     * @brief 计算实际计费车型
     * @param axisCount 轴数
     * @param vehicleType 车型
     * @return int 实际计费车型代码
     */
    int calculateVehicleType(int axisCount, int vehicleType);

private:
    /**
     * @brief 查询车型库
     * @param vehicleType 车型
     * @return QString 车型库返回信息
     */
    QString queryVehicleTypeDb(int vehicleType);
};

#endif // VEHICLEAXISCHECK_H 