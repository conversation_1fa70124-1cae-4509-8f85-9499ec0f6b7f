﻿#include "messagebox.h"
#include "globalui.h"
#include "remotecontrolmgr.h"

CMessageBox::CMessageBox(const QString &sMessage, int nBtnStyle, int nMsgStyle, QWidget *parent)
    : CBaseEtcDialog(QString(""), parent)
{
    m_lblMessage = new QLabel(this);
    m_lblHelpMsg = new QLabel(this);

    m_pPushButtonOK = new QPushButton(this);
    m_pPushButtonCancel = new QPushButton(this);

    m_BtnStyle = nBtnStyle;
    m_MsgStyle = nMsgStyle;
    filterChildrenKeyEvent();
    InitUI(nBtnStyle);

    QFontMetrics metrics = m_lblMessage->fontMetrics();
    QString sTmp = sMessage, sOut;
    int nPos = 0;
    for (int i = 1; i < sMessage.size(); ++i) {
        sTmp = sMessage.mid(nPos, i - nPos + 1);
        if (metrics.width(sTmp) > m_lblMessage->width()) {
            sOut.append(sTmp.left(sTmp.size() - 1)).append('\n');
            nPos = i;
        }
    }
    sOut.append(sMessage.mid(nPos));
    m_lblMessage->setText(sOut);
    m_pTimer = new QTimer();
    QObject::connect(m_pTimer, SIGNAL(timeout()), this, SLOT(OnTimeOut()));

    QObject::connect(m_pPushButtonOK, SIGNAL(clicked()), this, SLOT(OnClickedOk()));
    QObject::connect(m_pPushButtonCancel, SIGNAL(clicked()), this, SLOT(OnClickedCancel()));

    m_nSeconds = 0;
    setObjectName(QString("CMessageBox"));
}

CMessageBox::~CMessageBox()
{
    if (m_pTimer) {
        QObject::disconnect(m_pTimer, SIGNAL(timeout()), this, SLOT(OnTimeOut()));
        delete m_pTimer;
    }

    if (m_pPushButtonOK) {
        QObject::disconnect(m_pPushButtonOK, SIGNAL(clicked()), this, SLOT(OnClickedOk()));
    }

    if (m_pPushButtonCancel) {
        QObject::disconnect(m_pPushButtonCancel, SIGNAL(clicked()), this, SLOT(OnClickedCancel()));
    }
}

bool CMessageBox::Information(const QString sMessage, int nBtnStyle, QWidget *parent)
{
    CMessageBox box(sMessage, nBtnStyle, 0, parent);
    return QDialog::Accepted == box.UIDoModel();
}

bool CMessageBox::WarnOnTime(const QString &sMessage, const QString &sTimeMsg, int nSeconds)
{
    CMessageBox box(sMessage, Style_OkCancel);
    box.SetTimeMsg(sTimeMsg);
    box.SetAndStartWaitTime(nSeconds);

    return QDialog::Accepted == box.UIDoModel();
}

bool CMessageBox::DoWarnMessageOnTime(const QString &sTimeMsg, int nSeconds)
{
    SetTimeMsg(sTimeMsg);
    SetAndStartWaitTime(nSeconds);
    return QDialog::Accepted == UIDoModel();
}

bool CMessageBox::Information_Help(const QString &sTitle, const QString &sMessage,
                                   const QString &sHelpMsg, int nBtnStyle, QWidget *parent,
                                   bool allowRemoteCtrl)
{
    CMessageBox box(sMessage, nBtnStyle, 0, parent);
    box.SetTitle(sTitle);
    box.SetHelpMsg(sHelpMsg);
    //连接信号
    if (allowRemoteCtrl) {
        Ptr_RemoteCtrl->ConfirmBegin(sTitle, sMessage, sHelpMsg, nBtnStyle, &box,
                                     SLOT(OnConfirmed(bool)));
    }
    int ret = box.UIDoModel();
    if (allowRemoteCtrl) {
        Ptr_RemoteCtrl->ConfirmEnd(&box, SLOT(OnConfirmed(bool)));
    }

    return (QDialog::Accepted == ret);
}

void CMessageBox::WarnMessage(const QString &sMessage)
{
    CMessageBox box(sMessage, Style_Ok, MsgStyle_Warn);
    box.UIDoModel();
    return;
}

void CMessageBox::InitUI(int nStyle)
{
    CBaseEtcDialog::InitUI();

    QFont font(g_GlobalUI.m_FontName, g_GlobalUI.optw_TitleFontSize);
    QFont fontText(g_GlobalUI.m_FontName, g_GlobalUI.optw_TextFontSize);
    QFontMetrics fmText(fontText);
    int helpTextHeight = 2 * fmText.height();
    int messageTextHeight = height() * 0.7 - g_GlobalUI.optw_TitleHeight - helpTextHeight;
    QRect rctMessage(5, g_GlobalUI.optw_TitleHeight, width(), messageTextHeight - 10);
    QRect rctHelp(5, g_GlobalUI.optw_TitleHeight + messageTextHeight, width(), helpTextHeight - 10);
    m_lblMessage->setFont(font);
    m_lblMessage->setGeometry(rctMessage);
    m_lblMessage->setAlignment(Qt::AlignCenter);

    m_lblHelpMsg->setFont(fontText);
    m_lblHelpMsg->setGeometry(rctHelp);
    m_lblHelpMsg->setAlignment(Qt::AlignCenter);

    m_BtnStyle = nStyle;

    //定义按钮位置和大小
    m_pPushButtonOK->setFont(font);
    m_pPushButtonCancel->setFont(font);
    int btnHeight = 3 * g_GlobalUI.optw_TitleFontSize;
    int btnWidth = rect().width() / 5;
    int btnTop = rect().height() * 0.7;

    m_pPushButtonOK->setText("确定");
    m_pPushButtonCancel->setText("取消");

    if (Style_NoButton == nStyle) {
        m_pPushButtonOK->setVisible(false);
        m_pPushButtonCancel->setVisible(false);
    } else if (Style_Ok == nStyle) {
        m_pPushButtonCancel->setVisible(false);
        m_pPushButtonOK->setVisible(true);
        QRect rctOK(btnWidth * 2, btnTop, btnWidth, btnHeight);
        m_pPushButtonOK->setGeometry(rctOK);
    } else {
        QRect rctOK(btnWidth, btnTop, btnWidth, btnHeight);
        QRect rctCancel(3 * btnWidth, btnTop, btnWidth, btnHeight);
        m_pPushButtonOK->setGeometry(rctOK);
        m_pPushButtonOK->setVisible(true);

        m_pPushButtonCancel->setGeometry(rctCancel);
        m_pPushButtonCancel->setVisible(true);
    }

    //    setStyleSheet("background-color: rgb(238, 238, 238)");
}

void CMessageBox::SetTimeMsg(const QString sMsg) { m_sTimeMsg = sMsg; }

void CMessageBox::SetHelpMsg(const QString sHelpMsg)
{
    m_sHelpMsg = sHelpMsg;
    m_lblHelpMsg->setText(sHelpMsg);
}

void CMessageBox::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    mtcKeyEvent->setKeyType(KC_Func);
    switch (mtcKeyEvent->func()) {
        case KeyConfirm:
            OnConfirmed(true);
            break;
        case KeyEsc:
            OnConfirmed(false);
            break;
    }
    return;
}

void CMessageBox::SetAndStartWaitTime(int nSeconds)
{
    if (m_pTimer) {
        m_pTimer->start(1000);
        m_nSeconds = nSeconds;
    }
}

void CMessageBox::OnTimeOut()
{
    m_nSeconds--;
    QString str = QString("剩余时间%1秒...").arg(m_nSeconds);
    if (m_sTimeMsg.length() > 0) str = QString("剩余%1秒后%2").arg(m_nSeconds).arg(m_sTimeMsg);
    SetTitle(str);
    if (0 == m_nSeconds) {
        m_pTimer->stop();
        this->accept();
    }
}

void CMessageBox::OnClickedOk() { OnConfirmed(true); }

void CMessageBox::OnClickedCancel() { OnConfirmed(false); }

void CMessageBox::OnConfirmed(bool bOk)
{
    if (bOk) {
        if (m_pTimer->isActive()) {
            m_pTimer->stop();
        }
        this->accept();
    } else {
        if (Style_OkCancel == m_BtnStyle ) {
            if (m_pTimer->isActive()) m_pTimer->stop();
            this->reject();
        }
    }
}
