#include "vehquemgr.h"
#include "devicefactory.h"
#include "vehquemgr_etc.h"
#include "lanectrl.h"
CVehQueMgr::CVehQueMgr(QObject *parent) : QObject(parent)
{
    m_nAllowPassCount=0;
    m_nDetectCount=0;
}

//获取未过车数量
int CVehQueMgr::GetAllowPass()
{
    QMutexLocker locker(&m_Mutex);
    return m_nAllowPassCount;
}

void CVehQueMgr::SetAllowPass(qint32 nAllowCount)
{
    DebugLog("抬杆放行!");
    QMutexLocker locker(&m_Mutex);
    m_nAllowPassCount +=nAllowCount;
    if(m_nAllowPassCount>0){
        Ptr_Ctrl->SetIsAllowPass(true);
        CDeviceFactory::GetIOCard()->SetAllowPass();
        //费显灯变绿
        //CDeviceFactory::GetFareDisplayer()->SetPassLight(true);
    }
    return;
}


void CVehQueMgr::SetRefusePass(bool bResetVehCount)
{
    QMutexLocker locker(&m_Mutex);
    if(bResetVehCount){
        m_nAllowPassCount=0;
    }

    if(0==m_nAllowPassCount){
        CDeviceFactory::GetIOCard()->SetRefusePass();
        //费显灯变红
        //CDeviceFactory::GetFareDisplayer()->SetPassLight(false);
    }
    return ;
}

bool CVehQueMgr::ProcessBackLoopEvent(bool bStatus)
{
    QMutexLocker locker(&m_Mutex);
    if(bStatus){
        if(0==m_nAllowPassCount)
            return false;
    }else{
        if(m_nAllowPassCount>0)
            --m_nAllowPassCount;
        if(0==m_nAllowPassCount)
        {
            //如果是锁杆状态，本次业务完成后不落杆
            if(1){
                CDeviceFactory::GetIOCard()->SetRefusePass();
                Ptr_Ctrl->SetIsAllowPass(false);
            }
        }
    }
    return true;
}


