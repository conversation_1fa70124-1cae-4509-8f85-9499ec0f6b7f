﻿#include "vehplate.h"
#include "ui_vehplate.h"
#include <qimage.h>
#include <qdir.h>
#include <QPainter>
#include "common/lanetype.h"
#include "etclanectrl.h"

#define WND_WIDTH 150 //250
#define WND_HIGHT 142 //160
VehPlate::VehPlate(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::VehPlate)
{
    ui->setupUi(this);
}

VehPlate::~VehPlate()
{
    delete ui;
}

void VehPlate::InitUI()
{
    this->move(406, 225);
    ui->lbPic->setStyleSheet("background-color: rgb(0, 0, 0)");
    //this->move(190, 0);
    this->resize(WND_WIDTH,WND_HIGHT);
    this->setFixedSize(WND_WIDTH,WND_HIGHT);
    setWindowFlags(Qt::FramelessWindowHint);
//    ui->lbInfo->setText(QString::fromUtf8("抓 拍 图 像"));
//    ui->lbInfo->move(30,10);
//    ui->lbInfo->resize(100,20);
    ui->lbPic->resize(WND_WIDTH-1,WND_HIGHT-1);
    ui->lbPic->move(1,0);
}

void VehPlate::ShowPic(const QString &name)
{
    ui->lbPic->setPixmap(QPixmap(Path+name));
    ui->lbPic->show();
}

void VehPlate::paintEvent(QPaintEvent *)
{
    QPainter painter(this);
//    if(Ptr_Ctrl->GetLaneStatus() == lsNormalWorking)
//    {
//        painter.drawPixmap(0,0,WND_WIDTH*2, WND_HIGHT,QPixmap(":/images/images/vehplate.png"));
//    }
//    else
//    {
        painter.drawPixmap(0,0,WND_WIDTH, WND_HIGHT,QPixmap(":/images/images/vehplate.png"));
//    }
}
void VehPlate::ChangPic()
{
    if(Ptr_ETCCtrl->GetLaneStatus() == lsNormalWorking)
    {
        this->move(525,485);
        this->resize(WND_WIDTH*2,WND_HIGHT);
        this->setFixedSize(WND_WIDTH*2,WND_HIGHT);
        //ui->lbInfo->setText(QString::fromUtf8(" 抓 拍 图 像"));
    }
    else
    {
       this->move(767,485);
    }
}
void VehPlate::showVehpic(const QString &picpath)
{
    ui->Lable_picture->setGeometry(0,0,WND_WIDTH+3,150);
    QImage *image = new QImage;
    if(!image->load(picpath)){
        delete image;
        return;
    }
    ui->Lable_picture->setScaledContents(true);
    *image=image->scaled(WND_WIDTH,115,Qt::IgnoreAspectRatio);
    ui->Lable_picture->setPixmap(QPixmap::fromImage(*image));

    delete image;
}

void VehPlate::ClearPic()
{
    ui->Lable_picture->clear();
}
