#ifndef SPEVENTMGR_H
#define SPEVENTMGR_H
#include <QtCore>

struct CSpEvent
{
    qint32 nSpId;               //特殊事件id
    bool bAlarm;                //是否报警
    bool bStop;                 //是否停止处理
    quint8 nFaileCause;         //逻辑失败代码
    QString FareDisplayInfo;    //费显显示信息
    QString DisplayInfo;        //界面显示信息
public:
    void operator=(const CSpEvent &SpEvent)
    {
        if(this !=&SpEvent)
        {
            nSpId   = SpEvent.nSpId;
            bAlarm  = SpEvent.bAlarm;
            bStop   = SpEvent.bStop;
            nFaileCause     = SpEvent.nFaileCause;
            FareDisplayInfo = SpEvent.FareDisplayInfo;
            DisplayInfo     =SpEvent.DisplayInfo;
        }
        return;
    }
};

enum CLogicalFailCause{
    FaileCause_None,
    FaileCause_NoBalance,
    FaileCause_Dissamble,
    FaileCause_OBUStop=4,
    FaileCause_CardStop,
    FaileCause_OBUOutTime,
    FaileCause_CardOutTime,
    FaileCause_OBUNotInUse,
    FaileCause_CardNotInUse,
    FaileCause_10,
    FaileCause_NoEntry,
    FaileCause_NoCard,
    FaileCause_CardIssueErr,
    FaileCause_OBUIssueErr,
    FaileCause_NoFare,
    FaileCause_CardLost,
    FaileCause_OBULost,
    FaileCause_NeighBorDisturb,
    FaileCause_OweBlackList,//追缴黑名单
    FaileCause_MinFareFailed,
    FaileCause_FeeCalcFailed,
    FaileCause_OBUCardIssueDiff,
    FaileCause_VehCardNotSame,
    FaileCause_OutSpeed=25,
    FaileCause_StopPassByTime,
    FaileCause_NoWeight,
    FaileCause_OverWeight,
    FaileCause_GreenStop,
    FaileCause_OBUFeeErr,
    FaileCause_TotalFeeOverMin,
    FaileCause_Other=99
};

class CSpEventMgr
{
public:
    enum{
        SpEvent_None,
        SpEvent_NoOBU,			//无OBU
        SpEvent_UnUseOBU,       //尚未启用OBU
        SpEvent_OutTimeOBU,     //过期OBU
        SpEvent_BlackOBU,       //黑名单标签
        SpEvent_DissambleOBU,   //拆卸标签
        SpEvent_OBULowPower,    //电量低
        SpEvent_NotInsertCard,  //未插卡
        SpEvent_OBUVehClassErr, //标签车型错误
        SpEvent_DisturbCar,		 //前车干扰


        SpEvent_UnUseCard,       //未启用卡片
        SpEvent_OutTimeCard,     //过期卡
        SpEvent_NotLocalCard,    //未联网卡
        SpEvent_AbnormalCard,    //非正常卡类型
        SpEvent_LostCard,        //挂失
        SpEvent_CancleCard,      //黑名单注销
        SpEvent_OverDraftCard,   //透支卡
        SpEvent_BlackCard,        //禁用卡
        SpEvent_WriteCardFailed, //写卡失败
        SpEvent_WillLockCard,    //将锁卡
        SpEvent_HaveLockCar,     //已锁卡
        SpEvent_NotSameOBUCard,  //卡片和标签发行地不符

        SpEvent_InvalidEntry,   //出入口不匹配，入口无效
        SpEvent_VehClassDiff,   //出入口车型不符
        SpEvent_VehPlateDiff,   //出入口车牌不符

        SpEvent_NoEntry,        //无入口信息

        SpEvent_U,              //非法U型车，入口无效
        SpEvent_NotSelfCard,    //非本车卡，标签和卡车牌不一致
        SpEvent_OverTime,       //车辆超时
        SpEvent_OutTimeStay,    //超时停留
        SpEvent_NoBalance,      //余额不足

        SpEvent_PSamLock,       //psam 锁
        SpEvent_PsamError,      //Psam 异常

        SpEvent_BlackCar,       //黑名单车
        SpEvent_TransFailed,    //交易失败

        SpEvent_EntryNoFee,     //路径不可达/无费率
        SpEvent_MutlPath,       //路径不唯一
        SpEvent_LogOut,         //下班

        SpEvent_Other,          //

        SpEvent_NotLocalOBU,    //未联网标签


        //SpEvent_BlackCard,      //黑名单卡

        SpEvent_FrameError,     //帧数据错误

        SPEVENT_EF04_VALID,        //EF04文件无效

        SPEVENT_EF04_ABNORMAL,        //ef04文件费用异常
        SPEVENT_EF04_ReadFaild,       //ef04读取失败
        SPEVENT_EF04_OBUCARDDIF,      //ef04与卡内不符
        SPEVENT_TransStop,            //交易中断
        SPEvent_Violate,              //闯关

        SPEvent_TolalFeeOver,        //通行费溢出，超过最大金额
        SPEvent_StopPassByTime,      //限时通行

        SpEvent_Ignore,             //忽略的事件
        SpEvent_TruckStopPass,      //货车禁止通行

        SpEvent_Truck_OBU,        //20250527 入口OBU货车确认轴组特情
        SpEvent_End
    };

    CSpEventMgr();

    static CSpEvent GetSpEvent(qint32 nSpId,bool bIsExit);
    static qint32 GetEventIdByCardBListType(qint32 bType);

 private:
   // static CSpEvent m_SpEvents[SpEvent_End];
};

#endif // SPEVENTMGR_H
