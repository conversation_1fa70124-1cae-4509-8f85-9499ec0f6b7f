#include "sernumfm.h"
#include "ui_sernumfm.h"
#include "laneinfo.h"
#include <QMessageBox>
#include <QSettings>
#define Run_Key "HKEY_LOCAL_MACHINE\\SOFTWARE\\AutoLane\\Key"
SerNumFm::SerNumFm(QWidget *parent) : CBaseEtcDialog("", parent), ui(new Ui::SerNumFm)
{
    // InitUI();
    ui->setupUi(this);
    Init();
    ser = new SerNum();
    GetLocalInfo();
    filterChildrenKeyEvent();
    ui->pb_OK->setHidden(true);
    ui->le_SerNo->clear();
    ui->le_SerNo->setFocus();
}

SerNumFm::~SerNumFm() { delete ui; }

void SerNumFm::Init()
{
    CBaseEtcDialog::InitUI();
    ui->groupBox->move(10, 10);
}

void SerNumFm::GetLocalInfo()
{
    QString sError = "";
    char szUserInfo[17];
    char szSerNo[30];
    memset(szUserInfo, 0, sizeof szUserInfo);
    int nOrgID = Ptr_Info->GetStationID();
    int nLaneID = Ptr_Info->GetLaneId();
    ui->le_OrgID->setText(QString::number(nOrgID));
    ui->le_LaneID->setText(QString::number(nLaneID));
    nOrgCode = nOrgID * 100 + nLaneID;
    bool bRlt = ser->LoadAdapterInfo(sError);
    if (!bRlt) {
        ui->lb_Info->setText("警告:加载本机硬件信息出错");
        DebugLog(sError);
        return;
    }
    bRlt = ser->BuildUserInfo(nOrgCode, szUserInfo, sError);
    if (!bRlt) {
        ui->lb_Info->setText("警告:获取本机信息出错");
        DebugLog(sError);
        return;
    }
    ui->le_UserInfo->setText(QString::fromLocal8Bit(szUserInfo));
    bRlt = ser->BuildSeriNo(nOrgCode, szUserInfo, sizeof szUserInfo, szSerNo, sError);
    if (!bRlt) {
        ui->lb_Info->setText("警告:获取注册码信息出错");
        DebugLog(sError);
        return;
    }
    ui->le_SerNo->setText(QString::fromLocal8Bit(szSerNo));
}

void SerNumFm::on_pb_OK_clicked()
{
    QString sSerNo = ui->le_SerNo->text();
    QString sError = "";
    if (sSerNo == "") {
        ui->lb_Info->setText("警告:注册码不能为空");
        ErrorLog(sError);
        return;
    }
    bool bRlt =
        ser->CheckSeriNo(nOrgCode, sSerNo.toLatin1().data(), sizeof sSerNo.length(), sError);
    if (!bRlt) {
        ui->lb_Info->setText("警告:注册码错误");
        DebugLog(sError);
        return;
    }
    QSettings *pReg = new QSettings(Run_Key, QSettings::NativeFormat);
    pReg->setValue("SerNum", sSerNo);
    done(Accepted);
}

void SerNumFm::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (mtcKeyEvent->isFuncKey()) {
        switch (mtcKeyEvent->func()) {
            case KeyConfirm: {
                ui->lb_Info->clear();
                on_pb_OK_clicked();
                break;
            }
            case KeyDel: {
                int index = ui->le_SerNo->cursorPosition();
                QString sTemp = ui->le_SerNo->text();
                sTemp = sTemp.remove(index - 1, 1);
                ui->le_SerNo->setText(sTemp);
                ui->le_SerNo->setCursorPosition(index - 1);
                break;
            }
            case KeyLeft: {
                ui->le_SerNo->cursorBackward(false);
                break;
            }
            case KeyRight: {
                ui->le_SerNo->cursorForward(false);
                break;
            }
            default:
                break;
        }
        mtcKeyEvent->setKeyType(KC_Func);
    }
    if (mtcKeyEvent->isNumKey() || mtcKeyEvent->isLetterKey()) {
        if (mtcKeyEvent->isNumKey()) mtcKeyEvent->setKeyType(KC_Number);
        if (mtcKeyEvent->isLetterKey()) mtcKeyEvent->setKeyType(KC_Letter);
        int index = ui->le_SerNo->cursorPosition();
        QString sText = ui->le_SerNo->text();
        sText = sText.insert(index, QString(QChar(mtcKeyEvent->key())));
        ui->le_SerNo->setText(sText);
        ui->le_SerNo->setCursorPosition(index + 1);
    }
}
