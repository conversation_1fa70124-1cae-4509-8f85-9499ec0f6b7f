#ifndef TOTALFM_H
#define TOTALFM_H

#include <QWidget>

namespace Ui {
class TotalFm;
}

class TotalFm : public QWidget
{
    Q_OBJECT

public:
    explicit TotalFm(QWidget *parent = 0);
    ~TotalFm();

    void InitUI();

    void SetTolalPic(bool nTolalLight,bool nTolalPass);
    void SetETCIsOpen(bool isOpen);
public slots:
    void OnShowText(QString sText,int nRow);

private:
    Ui::TotalFm *ui;
private:
    void paintEvent(QPaintEvent *);
};

#endif // TOTALFM_H
