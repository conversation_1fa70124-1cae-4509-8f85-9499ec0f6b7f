#include <windef.h>
#include <windows.h>

#include <QApplication>
#include <QTextCodec>

#include "globalui.h"
#include "ilogmsg.h"
#include "mainwindow.h"
#include "parammgr.h"

HANDLE m_hFileHandle;  //并口文件句柄

bool openlpt1()
{
    if (m_hFileHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(m_hFileHandle);
        m_hFileHandle = INVALID_HANDLE_VALUE;
    }

    QString lptname = "\\\\.\\LPT1";
    WCHAR wstr[MAX_PATH] = {0};
    MultiByteToWideChar(CP_ACP, 0, lptname.toAscii(), -1, wstr, sizeof(wstr));

    //    m_hFileHandle = CreateFile((LPCWSTR)("\\\\.\\LPT1"), GENERIC_READ | GENERIC_WRITE, 0,
    //    NULL,
    //                               OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
    m_hFileHandle = CreateFile(wstr, GENERIC_READ | GENERIC_WRITE, 0, NULL, OPEN_EXISTING,
                               FILE_ATTRIBUTE_NORMAL, NULL);

    if (m_hFileHandle == INVALID_HANDLE_VALUE) {
        ErrorLog(QString("并口打印机初始化错误%1").arg(::GetLastError()));
        return FALSE;
    } else {
        DCB MyDCB;
        GetCommState(m_hFileHandle, &MyDCB);
        MyDCB.BaudRate = 9600;
        MyDCB.ByteSize = 8;
        MyDCB.Parity = NOPARITY;
        MyDCB.StopBits = ONESTOPBIT;
        SetCommState(m_hFileHandle, &MyDCB);

        COMMTIMEOUTS MyCommTimeouts;
        MyCommTimeouts.ReadIntervalTimeout = 0;
        MyCommTimeouts.ReadTotalTimeoutConstant = 0;
        MyCommTimeouts.ReadTotalTimeoutMultiplier = 0;
        MyCommTimeouts.WriteTotalTimeoutMultiplier = 0;
        // note: 可能最低要设置成2000，否则SetCommTimeouts返回参数错误
        MyCommTimeouts.WriteTotalTimeoutConstant = 1000;
        while (MyCommTimeouts.WriteTotalTimeoutConstant <= 9000) {
            bool LResult = SetCommTimeouts(m_hFileHandle, &MyCommTimeouts);
            if (!LResult) {
                ErrorLog(QObject::tr("SetCommTimeouts failed: Timeout=%1, ErrorCode=%2")
                             .arg(MyCommTimeouts.WriteTotalTimeoutConstant)
                             .arg(GetLastError()));
                MyCommTimeouts.WriteTotalTimeoutConstant += 500;
            } else {
                InfoLog(QObject::tr("SetCommTimeouts Success: Timeout=%1")
                            .arg(MyCommTimeouts.WriteTotalTimeoutConstant));
                break;
            }
        }
        return true;
    }
}

// void convert(){
//    QFile ff("d:\\aa.txt");
//    ff.open(QIODevice::ReadOnly);
//    QTextStream in(&ff);
//    QString intStr = in.readAll();
//    QString outStr;
//    for(int i=0; i<intStr.length(); ){
//        outStr.append(intStr.at(i++));
//        outStr.append(intStr.at(i++));
//        outStr.append(" ");
//    }

//    QFile dd("d:\\bb.txt");
//    dd.open(QIODevice::WriteOnly);
//    QTextStream out(&dd);
//    out << outStr;

//    dd.close();
//    ff.close();
//}

int main(int argc, char *argv[])
{
    /*****************************
     * 1.0.0
     * 2021/08/26   初始版本
     * **************************/
    g_ParamMgr.m_sVersion = "V1.0.3";  //更改版权信息//"V 1.0.2";
    QApplication a(argc, argv);

    QTextCodec *utf = QTextCodec::codecForName("UTF-8");
    QTextCodec::setCodecForCStrings(utf);
    QTextCodec::setCodecForTr(utf);
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("GBK"));

    QString sError;
    if (!log_init("1.0", QString("ExTollUI"), sError)) {
        ErrorLog(sError);
        log_fini();
        return 0;
    }

    //    convert();
    //    return 1;
    g_ParamMgr.Init();
    g_GlobalUI.Init();

    MainWindow w;
    if (!w.Init()) {
        ErrorLog("启动失败");
        return -1;
    }
    w.InitUI();
    w.show();

    DebugLog(QString("%1").arg(QApplication::applicationDirPath()));

    int retCode = a.exec();

    w.Destroy();

    log_fini();
    return retCode;
}
