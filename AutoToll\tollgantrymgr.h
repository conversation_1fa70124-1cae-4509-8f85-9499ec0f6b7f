#ifndef CTOLLGANTRYMGR_H
#define CTOLLGANTRYMGR_H

#include <QObject>

#include "batchmgr.h"
#include "calcfee.h"
#include "gbmsgtype.h"
#include "transinfo.h"

#pragma pack(push, 1)

struct CVehCountByCard
{
    quint16 etcCount;
    quint16 cpcCount;
};

struct CGantrySumInfo_H
{
    char szGantryId[22];             // 19门架编号
    char szCollectHourBatch[11];     // yyyyMMddhh 小时批次
    quint8 bComputerOrder;           //控制器编号，1-主机 2-备机
    char szCollectDate[10];          // yyyyMMdd
    quint32 batchCount;              //交易流水批次内总数
    CVehCountByCard TypeCount[30];   //车型交易量
    CVehCountByCard ClassCount[30];  //车种交易量
    quint32 etcSucessCount;          //成功交易量
    quint32 etcSuccessFee;           // ETC 成功交易额
    quint32 etcFailCount;            // ETC 失败交易额
    quint32 cpcTypeCount;            //不用
    quint32 cpcClassCount;           //不用
    quint32 cpcSuccessCount;
    quint32 cpcSuccessFee;
    quint32 cpcFailCount;
};

#pragma pack(pop)
const int MAX_GANTRYNUM = 20;

class CTollGantryMgr : public QObject
{
    Q_OBJECT
public:
    explicit CTollGantryMgr(QObject *parent = 0);

    static CTollGantryMgr *GetTollGantryMgr()
    {
        static CTollGantryMgr gantryMgr;
        return &gantryMgr;
    }

public:
    //设置门架基本信息
    void SetBaseInfo(const QString &sTollGantry, const QString &sTollGantryHex,
                     const QString &sTollIntervals, const QString &sGantryName, int nGantryType);

    //取门架方向
    quint32 GetGantryDirectId();
    QString GetGantryId() { return m_tollGantryId; }
    QString GetGantryHex() { return m_sHex; }

    bool GetCurGantryInfo(const QString &sGantryHex, CVirGantryInfo &gantryInfo, bool &bOpenGantry);
    int GetGantryType() { return m_nGantryType; }
    //设置门架合计数信息（程序启动时从数据库内读取）
    void SetGantrySumInfo(const QByteArray &SumInfo);  // const CGantrySumInfo_H &SumInfo);
    void SetGantrySumInfo_New(const QByteArray &SumInfo);

    void GantrySum(const CDoorWaste_ETCTU &etctu, int nIndex = 0);
    void GantrySum_New(const CDoorWaste_ETCTU &etctu, const QString &sGantryId);
    void GetGantrySumInfo(QByteArray &SumInfo);  // CGantrySumInfo_H &SumInfo);
    void GetGantrySumInfo_New(QByteArray &sumData);
    bool CheckNeedGrantryCalcFee();

    void AddGantryInfo(const CVirGantryInfo &gantryInfo);
    void AddOpenGantryInfo(const QList<CVirGantryInfo> &gantryList);

protected:
    static void GantrySum_Single(const CDoorWaste_ETCTU &etctu, CGantrySumInfo_H &gantrySum);
    bool SaveGantrySumData();
signals:

public slots:
    void CheckBatchChanged(QString sNewBatch);
    void CheckBatchChanged_New(QString sNewBatch);

protected:
    QMutex m_Mutex;
    QString m_tollGantryId;
    QString m_sHex;
    quint8 m_bDirection;      //方向
    qint32 m_nGantrySn;       //顺序号
    QString m_tollIntervals;  //收费单元组合
    QString m_sCantryName;
    CGantrySumInfo_H m_GantrySumInfos[MAX_GANTRYNUM];
    QMap<QString, CGantrySumInfo_H> m_GantrySumInfoMap;
    int m_nGantryType;  //门架类型

    QList<CVirGantryInfo> m_GantryList;
};

#endif  // CTOLLGANTRYMGR_H
