#ifndef BASEFORM_H
#define BASEFORM_H
#include <QDateTime>
#include <QMutex>
#include <QPainter>
#include <QWidget>
#include <phonon>

#include "globalui.h"

enum CTransPayType
{
    TransPT_None,
    TransPT_OBU,
    TransPT_ETCCard,
    TransPT_Cash = 11,
    TransPT_Other,
    TransPT_Union,
    TransPT_AliPay = 16,
    TransPT_WeChat
};

class BaseForm : public QWidget
{
    Q_OBJECT
public:
    explicit BaseForm(QWidget *parent = 0);
    ~BaseForm();
signals:

public slots:
protected:
    QPixmap scaledPixmap(const QPixmap &src, int width, int height = 0);
    QPixmap generatePixmap(const QPixmap &src, const int &radius, bool bRound = true);

private:
    //播放的声音
    Phonon::MediaObject *m_pSoundPlay;
    Phonon::AudioOutput *m_pAudioOutput;
    //播放的文件
    QString m_sSoundFileName;
    //循环播放间隔时间
    int m_nLoopInterval;
    //循环播放次数
    int m_nLoopTimes;

    //循环播放定时器
    QTimer *m_pLoopTimer;
    QMutex m_playMutex;

protected slots:
    void onSoundFinished();
    void onLoopPlay();

public:
    virtual void InitUI();
    virtual void OnPainter(QPainter &painter);
    //显示
    virtual void ShowForm();
    //不显示
    virtual void HideForm();

    //播放声音
    void PlaySound(const QString &fileName, int loopTimes = 0, int interval_Second = 0);
    void PlaySound(const QList<QString> lstFileName);
};

#endif  // BASEFORM_H
