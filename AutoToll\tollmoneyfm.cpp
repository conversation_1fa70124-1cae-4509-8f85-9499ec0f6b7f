#include "tollmoneyfm.h"


CTollMoneyFm::CTollMoneyFm(CTollMoneyInfo TMInfo, QWidget *parent) :
    CBaseOpWidget(parent)
{
    for(int i=0; i<MAX_LABEL_NUM; i++){
        m_pLabel[i] = NULL;
    }
    m_pLineEdit = NULL;

    m_lCardCost = 0;
    m_lTollMoney = 0;
    m_lCardCost = TMInfo.nCashCardMoney;
    m_lTollMoney = TMInfo.nCashMoney;

    Init();

    filterChildrenKeyEvent();
}

CTollMoneyFm::~CTollMoneyFm()
{

}

void CTollMoneyFm::Init()
{
    CBaseOpWidget::InitUI();
    //CBaseOpWidget::SetTitle(sTitle);

    for(int i=0; i<MAX_LABEL_NUM; i++){
        if(NULL == m_pLabel[i])
           m_pLabel[i] = new QLabel(this);
    }
    if(!m_pLineEdit)
        m_pLineEdit = new CNumLineEdit(Type_Num, 10, this);

    QFont ftText;
    ftText.setFamily(QString::fromUtf8("微软雅黑"));
    ftText.setPixelSize(25);
    ftText.setStyleStrategy(QFont::PreferAntialias);

    m_pLabel[0]->setGeometry(90,100,110,40);
    m_pLabel[0]->setAlignment(Qt::AlignLeft);
    m_pLabel[0]->setStyleSheet("QLabel{color: rgb(30, 30, 30);}");
    m_pLabel[0]->setFont(ftText);
    m_pLabel[0]->setText("应收金额");

    m_pLabel[1]->setGeometry(210,100,190,40);
    m_pLabel[1]->setAlignment(Qt::AlignLeft);
    m_pLabel[1]->setStyleSheet("QLabel{color: rgb(30, 30, 30); background-color:rgb(255,255,255)}");
    m_pLabel[1]->setFont(ftText);
    QString sText = QString::number((m_lCardCost+m_lTollMoney), 10);
    m_pLabel[1]->setText(sText);

    m_pLabel[2]->setGeometry(405,100,30,40);
    m_pLabel[2]->setAlignment(Qt::AlignLeft);
    m_pLabel[2]->setStyleSheet("QLabel{color: rgb(30, 30, 30);}");
    m_pLabel[2]->setFont(ftText);
    m_pLabel[2]->setText("元");

    m_pLabel[3]->setGeometry(90,170,110,40);
    m_pLabel[3]->setAlignment(Qt::AlignLeft);
    m_pLabel[3]->setStyleSheet("QLabel{color: rgb(30, 30, 30);}");
    m_pLabel[3]->setFont(ftText);
    m_pLabel[3]->setText("实收金额");

    m_pLabel[4]->setGeometry(405,170,30,40);
    m_pLabel[4]->setAlignment(Qt::AlignLeft);
    m_pLabel[4]->setStyleSheet("QLabel{color: rgb(30, 30, 30);}");
    m_pLabel[4]->setFont(ftText);
    m_pLabel[4]->setText("元");

    m_pLineEdit->setGeometry(210,170,190,40);
    m_pLineEdit->setAlignment(Qt::AlignLeft);
    m_pLineEdit->setFont(ftText);
    m_pLineEdit->setFocus();

    //CBaseOpWidget::SetMessage(sMsg);
}

// 设置实收金额
void CTollMoneyFm::SetActualMoney(QString sActualMoney)
{
    if(m_pLineEdit){
        m_pLineEdit->setText(sActualMoney);
    }
}

// 获取实收金额
quint32 CTollMoneyFm::GetActualMoney()
{
    if(m_pLineEdit){
        return m_pLineEdit->text().toLong();
    } else
        return 0;
}

// 实收金额校验
bool CTollMoneyFm::VerifyActualMoney(QString &sError)
{
    bool bFlag = false;
    if(NULL == m_pLineEdit)
        return false;

    if(m_lCardCost<=0 /*|| m_lTollMoney<0*/) {
        sError = QString("应收金额不正确，请重新输入");
        return false;
    }

    // 实收金额5的倍数
    quint32 lMoney = m_pLineEdit->text().toLong();
    if(0 != lMoney%5) {
        sError = QString("实收金额不是5的倍数，请重新输入");
        m_pLineEdit->clear();
        return false;
    }

    // 实收金额不大于应收金额，实收金额大于卡成本
    if(lMoney <= 0){
        sError = QString("实收金额应大于0，请重新输入");
        m_pLineEdit->clear();
    } else if(lMoney > 0 && lMoney < m_lCardCost){
        sError = QString("实收金额%1 < 卡成本%2，请重新输入").arg(lMoney).arg(m_lCardCost);
        m_pLineEdit->clear();
    } else if(lMoney > (m_lCardCost+m_lTollMoney)) {
        sError = QString("实收金额%1 > 应收金额%2，请重新输入").arg(lMoney).arg(m_lCardCost+m_lTollMoney);
        m_pLineEdit->clear();
    }else
        bFlag = true;

    return bFlag;
}

int CTollMoneyFm::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if(mtcKeyEvent->isNumKey()) {   // 数字
        if(!m_pLineEdit) return 0;
        m_pLineEdit->SetLineEditText(mtcKeyEvent);
    }

    if(mtcKeyEvent->isFuncKey() &&
            mtcKeyEvent->func() == KeyDel) {   // 删除键
        if(!m_pLineEdit) return 0;
        m_pLineEdit->DeleteLineEditText();
    }

    if(mtcKeyEvent->isFuncKey() &&
            mtcKeyEvent->func() == KeyEsc) {  // 退出键
        this->setModalResult(0);
    }

    if(mtcKeyEvent->isFuncKey() &&
            mtcKeyEvent->func() == KeyConfirm) {   // 确定键
        QString sError;
        if(!VerifyActualMoney(sError)){
           this->SetMessage(sError);
        } else {
            this->setModalResult(1);
        }
    }

   return 0;
}




