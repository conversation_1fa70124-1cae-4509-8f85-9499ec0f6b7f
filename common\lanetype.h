﻿#ifndef LANETYPE_H
#define LANETYPE_H

#include <QtCore>

#include "globalutils.h"

#define LaneCfg_FileName "lane.ini"

//定义函数返回类型
#define RETURN_OK 1
#define RETURN_FAILED 0

const quint32 TotalWeight_Default = 1500;  //缺省车辆总重量1500千克
const quint32 TotalWeight_Limit = 4500;
const quint32 LISTEN_PORT = 40005;
const int PassCardNum_LEN = 9;       //通行卡号长度
const int PaperCardNum_LEN = 7;      //纸卡卡号长度
const qint32 MAX_VEHPLATE_LEN = 12;  //最大车牌长度
const qint32 FULL_VEHPLATE_LEN = 7;  //最小车牌长度, 军车一般为7位

const qint16 ORG_NETWORKID = 3600;  //网络编号
const qint16 ORG_NETWORKID_GB = 3601;
const qint16 ORG_NETWORKID_SHORT = 36;  //网络编号
const qint8 ORG_TYPE_STATION_GB = 1;
const qint8 ORG_TYPE_LANE_GB = 2;
const qint8 ORG_TYPE_GANTRY_GB = 3;

const QString ORG_NETWORKID_HEX = QString("3601");
const qint16 ARMY_NETWORKID = 501;       //军车网络编号
const qint32 ORG_TYPE_CENTER = 9;        // 21;		//收费区域中心
const qint32 ORG_TYPE_SUBCENTER = 8;     // 22;     //收费分中心
const qint32 ORG_TYPE_STATION = 6;       // 23;		//收费站
const qint32 ORG_TYPE_VIRTUAL_STA = 15;  // 25;	//立交虚拟点
// const qint32 ORG_TYPE_ROAD_OWNER=40;	//拆账小业主
const qint32 ORG_TYPE_ROAD = 10;        // 41;        //拆账路段,业主收费中心
const qint32 ORG_TYPE_ADM_REGION = 43;  //行政区域

//定义流水业务类型
const qint32 LST_TYPE_NORMAL = 1;      //发卡收费
const qint32 LST_TYPE_MOTORBEGIN = 2;  //车队开始
const qint32 LST_TYPE_MOTORPASS = 3;   //车队过车
const qint32 LST_TYPE_MOTOREND = 4;    //车队结束
const qint32 LST_TYPE_HEDGE = 5;       //冲减入口倒车出口重打

const qint32 LST_TYPE_SUBPAY = 6;      //分段支付
const qint32 LST_TYPE_REPAY = 7;       //补费
const qint32 LST_TYPE_CARLEADOUT = 8;  //车辆引出
const qint32 LST_TYPE_EXREVERSE = 9;   //出口倒车报文

const qint32 LST_TYPE_VIOLATE_LOGOUT = 20;      //下班闯关
const qint32 LST_TYPE_VIOLATE = 21;             //闯关
const qint32 LST_TYPE_VIOLATE_FALSEALARM = 23;  //误报警
const qint32 LST_TYPE_VIOLATE_HELDBACK = 22;    //闯关被拦截
const qint32 LST_TYPE_HALFWASTE_ETC = 98;       // ETC半条流水
const qint32 LST_TYPE_HALFWASTE_MTC = 99;       // MTC半条流水
const qint32 VehWeight_MaxAlxeNum = 30;         //车辆最大轴数
enum CWorkToDoAfterClose
{
    wcNone,     //
    wcClose,    //关闭程序
    wcRestart,  //重新启动程序
    wcReboot,   //重新启动系统
    wcShutDown  //关机
};

enum CBadCardType
{
    BadCard_None,
    BadCard_Nomral = 1,
    BadCard_Factitious
};

enum CCfgFile
{
    cfProvinceDebtList,  // 省内追收名单 2025.04 新版补费 25
    cfSysParaDic,      //系统参数29
    cfOrgCode,         //机构编码1  旧
    cfLaneCode,        //车道编码2
    cfVCCode,          //车型编码3  旧
    cfOper,            //操作员编码9 旧
    cfGBCardBList,     //国标卡黑名单13旧 sqlite
    cfGBCardBListInc,  //国标卡黑名单 增量65
    cfVehWhite,        //车辆白名单14 旧
    cfVBList,          //车辆黑名单15旧
    cfPsamBList,       // Psam黑名单16旧
    cfAreaCode,        //区域编码 21旧
    cfSpPara,          //特殊参数22旧
    cfPublish,         //公告 23
    cfHolidayFree,     //节假日24旧
    cfShift,           //工班27旧
    cfCardVGList,      //卡灰名单28 空 旧
    cfVGList,          //车辆灰名单（嫌疑车）30
    cfLaneSound,       // 32
    cfLiangKeYiWei,    //两客一危85
    cfBigVehList,      //大件运输86
    cfMinFee,          //最短路径89   sqlite
    cfCreditGList,     //信用灰名单 87  sqlite
    cfCreditBList,     //信用黑名单88  sqlite
    cfFareDll,         //省级计费模块84
    cfOrgBasicInfo,    //基础信息90
    cfDoorCode,        //门夹字典80
    cfMinPathPara,     //最短路径参数78 sqlite
    cfPayBList,        //预缴黑名单
    cfStationDoor,     //门架承载关系 77
    cfEmVeh,           //紧急车67
    cfGreen,           //绿通车65
    cfFlagList,        //调头点参数

    cfEnd,
    //以下为旧版独有参数
    cfOfficFree,        //公务卡免费20旧
    cfBaseRate,         //基础费率   旧
    cfAllRate,          //全路径    旧
    cfGanTongKUseTime,  //赣通卡启用时间  旧
    cfSpRate,           //特殊计费参数

    cfAllEnd
};

#pragma pack(push, 1)
struct CCfgFileHeadRaw
{
    char szFileID[2];        //参数编码 char(2);
    char szVersion[9];       //版本号 char(9)(不足右补空格);
    char dwTotalLen[10];     //文件总长度 char (10) (不足左补0);
    char szUseTime[14];      //版本启用时间 char(14);
    char dwRecordCount[10];  //总记录条数 char (10) (不足左补0);
    char EncryptFlag;        //压缩标志 char(1),（默认为0，已压缩);
    char Spare[8];           //预留 char (8);
    char VerifyCode[32];     // MD5校验码 char (32)
};
#pragma pack(pop)

enum CVehClass
{
    VC_None = 0,
    VC_Car1,  //客1
    VC_Car2,  //客2
    VC_Car3,  //客3
    VC_Car4,  //客4
    VC_Car5,  //客5
    VC_Car6,  //客6

    VC_Truck = 10,
    VC_Truck1,  //货1
    VC_Truck2,  //货2
    VC_Truck3,  //货3
    VC_Truck4,  //货4
    VC_Truck5,  //货5
    VC_Truck6,  //货6

    VC_YJ1 = 21,  //一类专项作业
    VC_YJ2,       //二类专项作业
    VC_YJ3,       //三类专项作业
    VC_YJ4,       //四类专项作业
    VC_YJ5,       //五类专项作业
    VC_YJ6        //六类专项作业
};

inline bool isTruck(int nVehClass) { return (VC_Truck < nVehClass) && (nVehClass <= VC_Truck6); }
inline bool isZhuangXiangTruck(int nVehClass)
{
    return (VC_YJ1 <= nVehClass) && (nVehClass <= VC_YJ6);
}
inline bool isCar(int nVehClass) { return (VC_None < nVehClass) && (nVehClass <= VC_Car4); }
// 获得车型名称
inline QString GetVehClassName(int wVehClass)
{
    switch (wVehClass) {
        case VC_None:
            return QString("");
        case VC_Car1:
            return QString("客1");
        case VC_Car2:
            return QString("客2");
        case VC_Car3:
            return QString("客3");
        case VC_Car4:
            return QString("客4");
        case VC_Car5:
            return QString("客5");
        case VC_Car6:
            return QString("客6");

        case VC_Truck1:
            return QString("货1");
        case VC_Truck2:
            return QString("货2");
        case VC_Truck3:
            return QString("货3");
        case VC_Truck4:
            return QString("货4");
        case VC_Truck5:
            return QString("货5");
        case VC_Truck6:
            return QString("货6");
        case VC_YJ1:
            return QString("专项1");
        case VC_YJ2:
            return QString("专项2");
        case VC_YJ3:
            return QString("专项3");
        case VC_YJ4:
            return QString("专项4");
        case VC_YJ5:
            return QString("专项5");
        case VC_YJ6:
            return QString("专项6");
        default:
            return QString("");
    }
    return QString("");
}

////车种定义
// enum CVehType
//{
//    VT_None=0,          //0未知
//    VT_Normal,           //1普通
//    VT_Youhui,          //优惠客车
//    VT_Army =10,
//    VT_Police,
//    VT_Motorcade,       //车队
//    VT_FanXun,				//13：其他免征-救灾车；
//    VT_GuoAn,				//14：其他免征-国安车；
//    VT_JinJi,				//15：其他免征-紧急车；
//    VT_WhiteGongWu,			//16：白名单公务车；
//    VT_AllFreeGongWu=20,	//20：全免公务车；=> 统缴车
//    VT_PartFreeGongWu,		//21：路段免费公务车；
//    VT_AreaFreeGongWu,		//22：区域免费公务车；
//    VT_LvTongIn=30,			//30：省内绿通车；
//    VT_LvTongOut,			//31：省外绿通车。
//    VT_HolidayFree=99,		//99: 节假日免费车辆 //chi_spo
//    VT_AnnualTicket=108,	//108:九江二桥年票车
//    VT_GongAN=129,          //129:其他免征-公安车
//    VT_CombineHarvester=130,//130:其他免征-联合收割
//    VT_ThermalCoal=131,     //131:其他免征-电煤
//    VT_OtherFree,            //132:其他免征-其他

//    VT_END,             //以上为江西车种

//};

enum CUnionVehType
{
    UVT_Normal,                //普通车
    UVT_Army = 8,              // 8-军警车
    UVT_Emergency = 10,        // 10-紧急车
    UVT_MotorCade = 14,        // 4-车队
    UVT_FarmProduct = 21,      // 21-绿通
    UVT_CombinHarvester = 22,  // 22-联合收割
    UVT_Rescue = 23,           // 23-救灾
    UVT_J1 = 24,               // 24-集装箱
    UVT_BigTruck = 25,         // 25-大件运输
    UVT_YJVeh = 26,            // 26-应急救援车,专用obu国家综合性消防救援车
    UVT_TRUCK = 27,            // 27货车或半挂
    UVT_J2 = 28,               // J2类集装箱
    UVT_End = 99,              //以下是自定义车种
    UVT_Reverse,               //劝返
    UVT_Holiday,               //节假日
    UVT_Vaccin,                //疫苗
    UVT_EmergencyRescue,       //应急抢险车
    UVT_Police,                //警车
    UVT_MAX
};

enum CDiscountType
{
    DiscountType_None,
    DiscountType_FarmProduct = 1,
    DiscountType_CombinHarvester,
    DiscountType_J1,
    DiscountType_J2,
    DiscountType_Holiday,  //中心节假日免费-车道不用
    DiscountType_VaccIn,
    DiscountType_JiuZai  //救灾
};

enum CVehState
{
    VehState_BigTruck = 0x00,
    VehState_NotDiscount,     // 1非优惠车
    VehState_Green,           // 2绿通
    VehState_Combine,         // 3联合收割
    VehState_JZhuang,         // 4集装箱
    VehState_Vaccin,          // 5疫苗
    VehState_EmergencyResue,  // 6紧急救灾
    VehState_Def = 0xff       //缺省
};

extern bool IsEmergencyVeh(const QString &sPlate, int nPlateColor);
extern bool IsFreeVehType_GB(CUnionVehType uVehType);
extern CUnionVehType GetGBVehTypeByUserType(quint8 bUserType);

extern quint8 GetVehStateByVehType(quint8 bVehType);
extern int GetVehAxisNumByVC(quint8 bVehClass);
extern CVehClass GetTruckVehClassByAxisNum(quint8 nAxisNum);
extern int GetDiscountTypeByVehType(quint8 bVehType);

// 获得车类名称
extern QString GetUnionVehTypeName(CUnionVehType vehType);

//车牌颜色
enum VP_COLOR
{
    VP_COLOR_BLUE = 0,
    VP_COLOR_YELLOW,
    VP_COLOR_BLACK,
    VP_COLOR_WHITE,
    VP_COLOR_LittleGREEN,  //渐变绿色
    VP_COLOR_YELLOWGREEN,  //黄绿
    VP_COLOR_BLUEWHITE,    //蓝白
    VP_COLOR_TEMP,         // 7-临时
    VP_COLOR_UNKNOW = 9,   // 9-未知  -见工程建设方案工程补遗
    VP_COLOR_GREEN = 11,   //绿色
    VP_COLOR_RED,          //红色

    VP_COLOR_NONE = 0xFF
};

//货车轴型
enum CAxisType
{
    AXT_0,      // 0:其他轴型/异型轴
    AXT_A1T1,   // 1:单轴单胎
    AXT_A1T2,   // 2:单轴双胎
    AXT_A2T1,   // 3:双联轴单胎
    AXT_A2T12,  // 4:双联轴单双胎
    AXT_A2T2,   // 5:双联轴双胎
    AXT_A3T1,   // 6:三联轴单胎
    AXT_A3T2,   // 7:三联轴双胎
    AXT_A3T21,  // 8:三联轴双单胎
    AXT_A3T22   // 9:三联轴双双胎
};

//流水特请字段对应特情
enum SPEVENTID
{
    SP_TOLLBYVC = 1,    // 0-货车按车型收费
    SP_TSBDB2S,         // 1-车型不符入大出小时，要同时填1、2
    SP_TSBDS2B,         // 2-车型不符入小出大时，要同时填1、3
    SP_RESERVE1,        // 3-预留
    SP_TSVMP2F,         // 4-车类不符收改免
    SP_TSVMF2P,         // 5-车类不符免该收
    SP_TSVTH2K,         // 6-客货不符货改客
    SP_TSVTK2H,         // 7-客货不符客改货
    SP_UXPAY,           // 8-U型收费
    SP_UXFREE,          // 9-U型车免费
    SP_RESERVE2,        // 10-预留
    SP_SEOVTIME,        // 11-非正常超时
    SP_SECG1,           // 12-闯关（收费员上班）
    SP_SECG2,           // 13-闯关（收费员未上班）
    SP_SECG3,           // 14-闯关（设备误报警）
    SP_SECG4,           // 15-闯关（车辆误入）
    SP_SECG5,           // 16-闯关（车辆退回）
    SP_SECG6,           // 17-闯关（闯关被拦截）
    SP_WXTXJ,           // 18-无效通行劵（不可读）
    SP_SEJT,            // 19-J型车
    SP_TSIVERR,         // 20-废票
    SP_PYHC,            // 21-优惠
    SP_EXITBADCARD,     // 22-坏卡
    SP_CHANGECARD,      // 23-换卡
    SP_CARDGQ,          // 24-卡过期（通行介质）
    SP_CARDG,           // 25-卡灰名单
    SP_DIFFVLP,         // 26-车牌不符
    SP_VEHB,            // 27-车辆黑名单
    SP_GrayVeh,         // 28-灰名单车辆
    SP_VEHW,            // 29-车辆白名单
    SP_PSAMB,           // 30-PSAM卡黑名单
    SP_PSAMNW,          // 31-PSAM卡不住白名单
    SP_EDITWEIGHT,      // 32-编辑计重
    SP_TSIVRPT,         // 33-重打票
    SP_INVOCIEC,        // 34-修改票号
    SP_HDOWNBAR,        // 35-手动落杆
    SP_LOCK,            // 36-锁杆
    SP_ALOCKCARD,       // 37-自动锁卡
    SP_HLOCKCARD,       // 38-手动锁卡
    SP_RESERVE3,        // 39-预留
    SP_RepairInv,       // 40-补票
    SP_Tract,           // 41-拖车
    SP_NoBoxCard,       // 42-非本卡盒卡
    SP_EditFlagInfo,    // 44	标识信息编辑
    SP_DifFlagInfo,     // 45	标识信息不一致
    SP_DifMinFlagInfo,  // 46	标识信息不一致但为最低
    SP_DifFlagCount,    // 47	标识站数量不致
    SP_USelectSta,      // 48	U 型选站
    SP_MotorCade,       // 49	车队
    SP_NOPayCar,        // 50	未付车
    SP_NOArmyCar,       // 51	军警车
    SP_NOCARD = 52,     // 52-无卡
    // SP_ENREDO=51,		   //51-入口补卡
    SP_GWCARD,          // 53  公务车
    SP_OBUNOIssued,     // 54	OBU 未发行
    SP_OBUUNRead,       // 55	OBU 不可读
    SP_OBUDisassembly,  // 56	OBU 拆卸
    SP_NOEnStation,     // 57	无入口信息
    SP_PaperCard,       // 58	纸质通行券处理
    SP_RepairCard,      // 59	补交处理
    SP_GrayCPCCard,     // 60	CPC 卡灰名单
    SP_OBUBlackBlist    // 61	OBU 状态名单
};
enum GBSPEVENTID
{
    GBSP_Normal,            // 0	正常
    GBSP_OBUNOIssued,       // 1	OBU 未发行
    GBSP_OBUUNRead,         // 2	OBU 不可读
    GBSP_OBUDisassembly,    // 3	OBU 拆卸
    GBSP_NOMatchPlate,      // 4	入出口车牌不一致
    GBSP_NOMatchVehclass,   // 5	入出口车型不一致
    GBSP_NOMatchVehtype,    // 6	入出口车种不一致
    GBSP_NOEnStation,       // 7	无入口信息
    GBSP_NOPassCard,        // 8	无卡
    GBSP_BadCard,           // 9	坏卡
    GBSP_PaperCard,         // 10	纸质通行券处理
    GBSP_RepairCard,        // 11	补交处理
    GBSP_GrayCPCCard,       // 12	CPC 卡灰名单
    GBSP_OBUBlackBlist,     // 13	OBU 状态名单
    GBSP_CreditBlackBlist,  // 14	信用黑名单
    GBSP_CreditGrayBlist,   // 15	信用灰名单
    GBSP_End
};

extern GBSPEVENTID TOGBSpEvent(SPEVENTID eventID);
enum CAxisEditType
{
    WT_None,
    WT_GetVehAxis,
    WT_ModifyVehAxis,
    WT_InsertVehAxis,
    WT_DeleteVehAxis,
    WT_MergeVehAxis,
    WT_SplitVehAxis,
    WT_CarOverWeight = 10,  //客车超重
    WT_TopAxis,             //顶轴
    WT_VirtualAxis,         //虚轴
    WT_Rush                 //冲磅
};

//定义报文中设备位
// enum DEVSTATUS_POS
//{
//    DEV_POS_Printer,        //打印机
//    DEV_POS_CardReader1,    //卡读写器1
//    DEV_POS_FeeDisplay,     //费额显示器
//    DEV_POS_BAR,            //栏杆
//    DEV_POS_CardMgr,        //卡机
//    DEV_POS_StoreCardMgr,   //储值卡
//    DEV_POS_CardBox,        //卡箱
//    DEV_POS_TrigerLoop,     //触发线圈
//    DEV_POS_FrontLoop,      //前线圈
//    DEV_POS_DetcetLoop,     //检测线圈线圈
//    DEV_POS_BackLoop,       //过车线圈
//    DEV_POS_passLight,      //通行信号灯
//    DEV_POS_VDM,            //字符叠加器
//    DEV_POS_SCanner,        //扫描仪
//    DEV_POS_MagCardMgr,     //
//    DEV_POS_Weight,         //计重设备
//    DEV_POS_VPR,            //车牌识别
//    DEV_POS_RSU,            //天线
//    DEV_POS_ETCInfo,        //情报板
//    DEV_POS_CanopyLight,    //雨棚灯
//    DEV_POS_AlarmDetect,    //声光报警
//    DEV_POS_CardReader2,    //卡读写器2
//    DEV_POS_CardReader3,    //卡读写器3
//    DEV_POS_CardReader4,    //卡读写器4
//    DEV_POS_End
//};
enum DEVSTATUS_POS
{
    DEV_POS_None,
    DEV_POS_NET,      //网络状态  0,未知 1正常 2 故障
    DEV_POS_Printer,  //打印机 '0'，未知,'1', 正常 '2'，故障 '3',缺纸
    DEV_POS_CardReader1,  //卡读写器 '0'，未知,'1',正常 '2',故障 无人职守车道上读写器
    DEV_POS_CardReader,  //卡读写器 '0'，未知,'1',正常 '2',故障 无人职守车道下读写器
    DEV_POS_CardReader2,    //卡读写器 '0'，未知,'1',正常 '2',故障 备用读写器
    DEV_POS_FeeDisplay,     //费显  '0',未知 '1'，正长 '2'，故障
    DEV_POS_BAR,            //栏杆  '0'，未知 '1'，起 '2'，落
    DEV_POS_TrigerLoop,     //触发线圈1 '0'，未知 '1'，有车 '2'，无车
    DEV_POS_DetcetLoop,     //触发线圈2 '0'，未知 '1'，有车 '2'，无车
    DEV_POS_FrontLoop,      //前线圈 '0'，未知 '1'，有车 '2'，无车
    DEV_POS_BackLoop,       //后线圈 '0'，未知 '1'，有车 '2'，无车
    DEV_POS_CanopyLight,    //雨棚信号灯 '0'未知 '1'绿 '2' 红
    DEV_POS_passLight,      //通行信号灯  '0',未知 '1'，绿 ，'2'红
    DEV_POS_ALARMDETECTOR,  //脚踏报警器      '0' 未知  '1',正常 '2'报警'
    DEV_POS_ALARMLIGHT,     //黄闪报警器      '0' 未知  '1',正常 '2'报警'
    DEV_POS_AlarmDetect,    //声音报警器      '0' 未知  '1',正常 '2'报警'
    DEV_POS_Weight,         //计重设备   '0',未知 '1'，正常 '2'故障
    DEV_POS_VDM,            //字符叠加   '0',未知 '1'，正常 '2'故障
    DEV_POS_VPR,            //车牌识别   '0'，未知，'1'，正常 '2'故障
    DEV_POS_VideoCard,      //视频采集卡  '0',未知,  '1' 正常  '2'故障
    DEV_POS_CCU,            // CCU模块（车道技术柜）'0',未知,  '1' 正常  '2'故障
    DEV_POS_RSU,            // RSU(ETC天线)        '0',未知   '1'，正常 '2'故障
    DEV_POS_AUTOCARDMGR,  //自动发卡机（或人工道小卡机)  '0',未知 '1'，正常 '2'故障
    DEV_POS_CARDMGR1,     //自动发卡机内小卡机 1 :'0' 未知 '1' 正常 '2'故障 '3'坏卡
    DEV_POS_CARDMGR2,     //自动发卡机内小卡机 2 :'0' 未知 '1'正常 '2'故障 '3'坏卡
    DEV_POS_CARDMGR3,     //自动发卡机内小卡机 3 :'0' 未知 '1'正常 '2'故障 '3'坏卡
    DEV_POS_CARDMGR4,     //自动发卡机内小卡机 4 : '0' 未知 '1'正常 '2'故障 '3'坏卡
    Reserve1,
    Reserve2,
    Reserve3,
    Reserve4,
    Reserve5,
    Reserve6,
    Reserve7
};

//逻辑卡类型
enum LCardType
{
    LCT_NONE,            //	0	未知卡类型
    LCT_PASSCARD,        //	1	通行卡
    LCT_PRECODECARD,     //	2	预编码卡
    LCT_PAPERCARD,       //	3	纸卡
    LCT_LTACARD,         //	4	鲁通A
    LCT_LTBCARD,         //	5	鲁通B
    LCT_LTCSTORE,        //	6	鲁通C储值
    LCT_LTCTALLY,        //	7	鲁通C记账
    LCT_OLDTALLY = 10,   //	10	旧记账卡
    LCT_STORECARD = 22,  //	22	国标储值(省外)
    LCT_TALLYCARD = 23   //	23	国标记账(省外)
};

//车道事件ID

enum LaneEventID
{
    LEV_None = 0,
    LEV_DsaFrontLoop,         // 1停用前线圈(可以无车发卡)
    LEV_EnaFrontLoop,         // 2启用前线圈(有车发卡)
    LEV_EnaAllowUseResetKey,  // 3启模拟键
    LEV_DsaAllowUseResetKey,  // 4停用模拟键
    LEV_EnaVpr,               // 5启用车牌识别-m
    LEV_DsaVpr,               // 6停用车牌识别-m
    LEV_EnaTollByVClass,      // 7启用货车按车型收费-m
    LEV_DsaTollByVClass,      // 8停用货车按车型收费-m
    LEV_EnaCardMgr,           // 9启用卡机
    LEV_DsaCardMgr,           // 10停用卡机
    LEV_ReGrantCard,          // 11补卡 -m
    LEV_LeadOut,              // 12车辆引出
    LEV_ReDo,                 // 13车辆重新处理
    LEV_Motor,                // 14车队-m
    LEV_PaperCard,            // 15纸卡
    LEV_BadCard,              // 16坏卡
    LEV_NoCard,               // 17无卡
    LEV_PassFailed,           // 18密码输入错误（yn）
    LEV_UVeh,                 // 19U型车
    LEV_UFreeFee,             // 20U型车免费
    LEV_SubSecFee,            // 21分段付费
    LEV_LockBar,              // 22锁杆-m
    LEV_UnLockBar,            // 23解锁-m
    LEV_DeductFailed,         // 24扣款失败
    LEV_ShortBalance,         // 25余额不足
    LEV_YTWriteFailed,        // 26云通卡写卡失败车辆放行
    LEV_Invoice,              // 27票据处理
    LEV_CardBox,              // 28卡箱操作
    LEV_EditWeight,           // 29编辑计重
    LEV_Reverse,              // 30倒车
    LEV_FALSEALARM,           // 31设备误报警
    LEV_VIALATEHELDBACK,      // 32闯关被拦截
    LEV_VehPlateDiff,         // 33入出车牌不符
    LEV_VehClassDiff,         // 34入出车型不符
    LEV_Reserve35,            //保留
    LEV_DevStatus = 36,       // 36设备状态变化
    LEV_QryEnList,            // 37入口信息查询
    LEV_QryEnPic,             // 38查询入口图像
    LEV_EnVBL,                // 39入口车辆黑名单
    LEV_ExtVBL,               // 40出口车辆黑名单
    LEV_EnCardB,              // 41入口卡黑名单
    LEV_ExtCardB,             // 42出口卡黑名单
    LEV_EnLTDiff,             // 43入口鲁通卡信息不符
    LEV_ExtLTDiff,            // 44出口鲁通卡信息不符
    LEV_PSamNoInWL,           // 45车道PSAM卡不在白名单
    LEV_VehVGL,               // 46车辆灰名单
    LEV_NoOBU,                // 47无电子标签(ETC)
    LEV_OBUBL,                // 48obu黑名单
    LEV_NotAreaCard,          // 49非本区域卡
    LEV_OBUVlpDiff,           // 50obu车牌不符
    LEV_ParamUpdate,          // 51参数更新
    LEV_NetStatus,            // 52网络状态
    LEV_ProCardOutTime,       // 53云通卡过期
    LEV_IllegalCard,          // 54伪卡、非法卡/无效卡
    LEV_VWL,                  // 55车辆白名单
    LEV_OBUDis,               // 56 OBU非法拆卸
    LEV_OBUNoCard,            // 57OBU 无卡
    LEV_CardError,            // 58 ETC读卡错误
    LEV_NOEntryInfo,          // 59 无入口信息
    LEV_ErrorEntryInfo,       // 60 非法入口信息
    LEV_OBUOutTime,           // 61 OBU过期
    LEV_StayOverTime,         // 62 车辆静态超时
    LEV_OverTimeCar,          // 63 超时车
    LEV_CardDuSai,            // 64 滞卡
    LEV_CancelReprint,        // 65 打印失败，取消重新打印
    LEV_CancelPrint,          // 66 打印失败，取消打印
    LEV_VehClassS2B,          // 67 车型小改大
    LEV_OldEvent,  //将新定义的事件同原来云南事件分开，并且新定义的暂不发送报文。
    LEV_LongVeh,   //超长车
    LEV_EditFlag,  //编辑标识点
    LEV_End
};

//车辆入口信息类型
enum EntryInfoType
{
    Entry_None,      //空
    Entry_ByManual,  //手工选择入口
    Entry_ByCard,    //卡内记录
    Entry_ByQry      //查询返回
};

enum CFreeType
{
    FT_None,
    FT_FullFree,
    FT_RoadOwner,       //	2-管理处
    FT_AdmRegion,       //	3-行政区域
    FT_NeighborSta,     //	4-相邻站
    FT_NeighborStaPlus  //	5-相邻站+入出口
};

//计费方式
enum CTollType
{
    TOLL_Type_None,        //
    TOLL_Type_VehClass,    //车型收费
    TOLL_Type_Weight,      //计重收费
    TOLL_Type_OweRepay,    //欠费补费
    TOLL_Type_BlackRepay,  //黑命单补费
    TOLL_Type_FleeRepay,   //补费
    TOLL_Type_OtherRepay   //其他补费
};

enum CLaneType
{
    LanType_None = 0,
    LaneType_MtcEntry = 1,     // ME入口
    LaneType_MtcExit = 2,      // ME出口
    LaneType_EtcEntry = 3,     // ETC入口
    LaneType_EtcExit = 4,      // ETC出口
    LaneType_AutoLane = 0x10,  //自助发卡-国标
};

//闯关类型
enum CViolateType
{
    ViolateT_None,
    ViolateT_Violate,
    ViolateT_FalseAlarm,
    ViolateT_ViolateHeldBack
};

//支付方式
/*
enum CPayType
{
    PT_None=0,
    PT_Cash,                //现金
    PT_StoreCard,           //云通储值卡
    PT_TallyCard,           //云通记账卡
    PT_ServantCard,         //云通公务卡
    PT_MonthCard,           //云通包缴卡
    PT_Reserver1,
    PT_Reserver2,
    PT_UniPayCard,          //银联
    PT_OntCartoon,          //一卡通
    PT_ProvOutStore,        //省外储值
    PT_ProOutTally,         //省外记账
    PT_PassDue=88,          //欠费
    PT_Free=99,             //免费

    //以下为临时标记，实际支付方式只有以上几种
    PT_ProCard         //鲁通卡
};
*/
//支付类型 1-现金 2-其他第三方 3-银联 4-ETC 6-支付宝 7-微信
enum CPayType
{
    PayType_None = 0,
    PayType_Cash,
    PayType_Other,
    PayType_Union,
    PayType_ETC,
    PayType_BAK,
    PayType_Alipay,
    PayType_WeChat
};

// 交易支付方式，1-出口ETC通行 2-出口ETC刷卡通行 11-现金 12-第三方支付 13银联卡支付 16-支付宝
// 17-微信
enum CTransPayType
{
    TransPT_None,
    TransPT_OBU,
    TransPT_ETCCard,
    TransPT_Cash = 11,
    TransPT_Other,
    TransPT_Union,
    TransPT_AliPay = 16,
    TransPT_WeChat
};

extern CPayType TransPayTypeToPayType(CTransPayType transPayType);

enum PASS_STATUS
{
    PASS_STATUS_RCV,              // 0-出口收回
    PASS_STATUS_MTCEN,            // 1-封闭式入口发出
    PASS_STATUS_ETCEN = 3,        // 3-封闭式ETC入口发出
    PASS_STATUS_PORTABLE = 5,     // 5-便携式入口发出
    PASS_STATUS_PDA = 7,          // 7-手持入口发出
    PASS_STATUS_AUTOCARD = 9,     // 9-无人值守入口发出
    PASS_STATUS_GBAUTOCARD = 16,  // 16-国标自助发卡标志
    PASS_STATUS_REPARI = 11
};

enum CLaneStatus
{
    lsUnlogin,        //下班
    lsNormalWorking,  //上班
    lsRepare,         //维护
    lsWaitCfg,        //等待下载参数
    lsSleep           //休眠,不存在该车道状态，由相关状态机实现
};

//车辆信息改变类型
enum CVehChangeType
{
    VCT_None,
    VCT_VC,   //车型
    VCT_VT,   //车情
    VCT_VLP,  //车牌
    VCT_Clear
};

enum CVehClassWay
{
    VehClassWay_None,
    VehClassWay_Auto,
    VehClassWay_Lib,
    VehClassway_Input,
    VehClassWay_OBU
};

struct CVehInfo
{
    CVehClass PVehClass;                        //初判车型
    CVehClass AutoVehClass;                     //自动识别车型
    CVehClass VehClass;                         //最终车型
    char szAutoVehPlate[MAX_VEHPLATE_LEN + 1];  //自动识别车牌
    quint8 nAutoVehPlateColor;                  //自动识别车牌颜色
    char szVehPlate[MAX_VEHPLATE_LEN + 1];      //最终车牌
    quint8 nVehPlateColor;                      //最终车牌颜色
    CUnionVehType GBVehType;                    //国标车种
    CVehClass qryVehClass;                      //查询车型
    int qryVehType;                             //查询车种
    int nVehClassWay;                           // 0 空 1-自动识别 2-车型库 3-人工输入
    int nScore;                                 //车型可信度

public:
    CVehInfo() { Clear(); }
    void Clear()
    {
        PVehClass = VC_None;
        VehClass = VC_None;
        AutoVehClass = VC_None;

        GBVehType = UVT_Normal;

        memset(szAutoVehPlate, 0, sizeof szAutoVehPlate);
        nAutoVehPlateColor = VP_COLOR_NONE;

        memset(szVehPlate, 0, sizeof szVehPlate);
        nVehPlateColor = VP_COLOR_NONE;
        nVehClassWay = VehClassWay_None;
        qryVehType = 0;
        qryVehClass = VC_None;
        nScore = 0;
    }

    CVehInfo &operator=(const CVehInfo &b)
    {
        if (this != &b) {
            PVehClass = b.PVehClass;
            AutoVehClass = b.AutoVehClass;
            VehClass = b.VehClass;
            GBVehType = b.GBVehType;

            memcpy(szAutoVehPlate, b.szAutoVehPlate, sizeof(b.szAutoVehPlate));
            nAutoVehPlateColor = b.nAutoVehPlateColor;
            memcpy(szVehPlate, b.szVehPlate, sizeof(b.szVehPlate));
            nVehPlateColor = b.nVehPlateColor;
            qryVehClass = b.qryVehClass;
            qryVehType = b.qryVehType;
            nVehClassWay = b.nVehClassWay;
            nScore = b.nScore;
        }
        return *this;
    }

    bool IsEmpty() const { return VehClass == VC_None; }

    bool HasAutoRegResult()
    {
        return (AutoVehClass != VC_None) || (qstrnlen(szAutoVehPlate, MAX_VEHPLATE_LEN) > 0);
    }

    bool IsVLPEmpty() const
    {
        if (/*VP_COLOR_NONE == nVehPlateColor &&*/ 0 >= strlen(szVehPlate))
            return true;
        else if (GB2312toUnicode(szVehPlate) == QString("无车牌"))
            return true;
        else
            return false;
    }
    quint8 GetGBVehTypeForTrans()
    {
        if (GBVehType == UVT_Police) return UVT_Army;

        if (GBVehType >= UVT_End) {
            return UVT_Normal;
        }
        return GBVehType;
    }
    bool bNeedConfirmAxis() const
    {
        return GBVehType == UVT_TRUCK || GBVehType == UVT_BigTruck || UVT_J1 == GBVehType ||
               UVT_J2 == GBVehType;
    }
};
Q_DECLARE_METATYPE(CVehInfo)

struct CAuthorizeInfo
{
    char szMonitorId[10];    // 0结束
    char szMonitorName[11];  // 0结束
    QDateTime AuthorizeTime;

public:
    CAuthorizeInfo() { Clear(); }
    void Clear()
    {
        memset(szMonitorId, 0, sizeof szMonitorId);
        memset(szMonitorName, 0, sizeof szMonitorName);
    }

    CAuthorizeInfo &operator=(const CAuthorizeInfo &b)
    {
        memcpy(szMonitorId, b.szMonitorId, sizeof(b.szMonitorId));
        memcpy(szMonitorName, b.szMonitorName, sizeof(b.szMonitorName));
        AuthorizeTime = b.AuthorizeTime;

        return *this;
    }
};

struct CVehEntryInfo
{
    int nEntryType;  //入口信息类型 Entry_ByCard,
    // quint16 wEnNetWorkId;  //入口网络号

    quint32 dwEnStationID;  //入口站
    quint32 dwExStationID;  //出口站
    QString sEnStaionName;  //入口站名称
    QString sExStationName;
    int nEnLaneID;  //入口车道

    QDateTime EnTime;    //入口时间
    quint8 bEnLaneType;  //入口车道类型
    quint32 dwEnOper;    //入口操作员
    quint8 bEnShiftId;   //入口班次
    quint8 bEnVC;        //入口车型
    quint8 bEnVT;        //入口车种
    quint8 bEnVLPC;      //入口车牌颜色
    char szEnVLP[16];    //入口车牌
    quint32 nPassTime;   //车辆行驶时间,单位秒

    quint32 dwTotalWeight;
    quint32 dwWeightLimit;
    quint8 VehicalAxles;
    quint32 gantryPassTime;
    QString sGantryNumHex;
    quint8 bVehState;  //车辆通行状态 00-大件运输,01-非优惠车 0xFF-缺省不用

    QString sEnNetWorkIdHex;  //入口网络号hex
    QString sEnStationHex;    //
    QString sEnLaneHex;       //
    QString sEnGBStationId;
    QString sEnGBLaneId;
    QString sLastGantryId;  //上一个门架id etc

public:
    CVehEntryInfo() { Clear(); }
    void Clear()
    {
        nEntryType = Entry_None;
        // wEnNetWorkId = 0;
        dwEnStationID = 0;
        dwExStationID = 0;
        sEnStaionName.clear();
        sExStationName.clear();
        nEnLaneID = 0;
        bEnLaneType = 0;
        dwEnOper = 0;
        bEnShiftId = 0;
        bEnVC = 0;
        bEnVT = 0;
        bEnVLPC = VP_COLOR_NONE;
        memset(szEnVLP, 0, sizeof(szEnVLP));
        nPassTime = 0;

        sEnStationHex.clear();
        sEnLaneHex.clear();
        sEnNetWorkIdHex.clear();
        sEnGBLaneId.clear();
        sEnGBStationId.clear();
        sGantryNumHex.clear();
        sLastGantryId.clear();

        gantryPassTime = 0;
        bVehState = 0xFF;
        dwTotalWeight = 0;
        dwWeightLimit = 0;
        VehicalAxles = 0;
    }

    CVehEntryInfo &operator=(const CVehEntryInfo &b)
    {
        nEntryType = b.nEntryType;
        // wEnNetWorkId = b.wEnNetWorkId;
        dwEnStationID = b.dwEnStationID;
        dwExStationID = b.dwExStationID;
        sEnStaionName = b.sEnStaionName;
        sExStationName = b.sExStationName;
        nEnLaneID = b.nEnLaneID;
        EnTime = b.EnTime;
        bEnLaneType = b.bEnLaneType;
        dwEnOper = b.dwEnOper;
        bEnShiftId = b.bEnShiftId;
        bEnVC = b.bEnVC;
        bEnVT = b.bEnVT;
        bEnVLPC = b.bEnVLPC;
        memcpy(szEnVLP, b.szEnVLP, sizeof(b.szEnVLP));
        nPassTime = b.nPassTime;

        sEnNetWorkIdHex = b.sEnNetWorkIdHex;
        sEnStationHex = b.sEnStationHex;
        sEnLaneHex = b.sEnLaneHex;
        sEnGBLaneId = b.sEnGBLaneId;
        sEnGBStationId = b.sEnGBStationId;

        sGantryNumHex = b.sGantryNumHex;
        sLastGantryId = b.sLastGantryId;
        dwTotalWeight = b.dwTotalWeight;
        dwWeightLimit = b.dwWeightLimit;
        VehicalAxles = b.VehicalAxles;
        bVehState = b.bVehState;
        gantryPassTime = b.gantryPassTime;

        return *this;
    }
};

//免费区间信息
struct CFreeAreaInfo
{
    quint8 bFreeType;
    char FreeArea[80];
};

//标识站信息
struct CFlagStationInfo
{
    quint8 bFlagStationCnt;
    char FlagStations[60];
};

//计算通行费
struct CCalculateMoneyInfo
{
    qint32 nTruckBasicMoney;     //货车基本费额  nTotalTollMoney-nOverWeightMoney
    qint32 nTotalTollMoney;      //出口基础金额(折扣前) 应收+免收,=原nTollMoney
    qint32 nCardCost;            //应收卡成本
    quint32 nOverWeightMoney;    //超限加收通行费
    quint32 nFreeMoneyDiscount;  //通行费免收金额 (折扣后) =原nFreeMoney
    quint32 nTotalDisCountTollMoney;  //出口计算通行费（折扣后）应收+免收 =原nDiscountMoney;
    quint32 nDisCountTollMoneyDue;  //应实收通行费,折扣后应收金额-- Add -
    quint32 nRebateMoney;           //折扣优惠金额只针对实收不含免收
public:
    CCalculateMoneyInfo() { Clear(); }
    void Clear()
    {
        nTruckBasicMoney = 0;
        nTotalTollMoney = 0;
        nCardCost = 0;
        nOverWeightMoney = 0;
        nFreeMoneyDiscount = 0;
        nTotalDisCountTollMoney = 0;
        nDisCountTollMoneyDue = 0;
        nRebateMoney = 0;
    }
    CCalculateMoneyInfo &operator=(const CCalculateMoneyInfo &b)
    {
        nTruckBasicMoney = b.nTruckBasicMoney;
        nTotalTollMoney = b.nTotalTollMoney;
        nCardCost = b.nCardCost;
        nOverWeightMoney = b.nOverWeightMoney;
        nFreeMoneyDiscount = b.nFreeMoneyDiscount;
        nTotalDisCountTollMoney = b.nTotalDisCountTollMoney;
        nDisCountTollMoneyDue = b.nDisCountTollMoneyDue;
        nRebateMoney = b.nRebateMoney;

        return *this;
    }
};

//票据修改信息
struct CInvModifyInfo
{
    char PreInvNo[12];
    quint16 wPreInvCnt;
    char AftInvNo[12];
    quint16 wAftInvCnt;
};

//打印票信息
struct CInvPrintInfo
{
    quint64 nInvNo;
    quint16 bInvCnt;
};

//计费方式
enum CFareCalcType
{
    FC_Normal,              //正常计费
    FC_UType,               // u型车计费
    FC_SameStationNotUType  //主辅站非U行车计费
};
//流水信息,主界面显示
struct CWasteInfo
{
    bool bExit;             //是否出口
    quint8 bType;           //交易结果 1-成功 0-失败, 9:其它错误交易
    QDateTime dtWasteTime;  //业务时间
    QString sCardId;
    CVehClass vehClass;     //车型
    CUnionVehType vehType;  //车种
    quint8 nVLPC;           //车牌颜色
    QString sVehPlate;
    QString sEnStationName;
    quint32 dwToTalWeight;    //总重
    quint32 dwWeightLimit;    //限重
    quint32 dwCashMoney;      //现金通行费
    double dwLastMoney;       //最终金额
    quint32 dwBalance;        //余额
    QString sTransPayType;    //支付方式
    double dwConsumeMoney;    //扣费金额
    QString sDiscountedFree;  //免费金额
    bool bIsPayByStoreCard;   //储值卡
    bool bTallyCard;          //记账卡
    bool bSuccessed;          //是否成功
    QString sDesc;            //说明
public:
    CWasteInfo() { Clear(); }
    void Clear()
    {
        bType = 0;
        dtWasteTime = QDateTime::fromString("2000-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        sCardId.clear();
        vehClass = VC_None;
        vehType = UVT_Normal;
        nVLPC = 0;
        sVehPlate.clear();
        sEnStationName.clear();
        dwCashMoney = 0;
        dwLastMoney = 0;
        dwBalance = 0;
        sDiscountedFree.clear();
        bIsPayByStoreCard = false;
        bTallyCard = false;
        bSuccessed = false;
        dwConsumeMoney = 0;
        sDesc.clear();
        dwToTalWeight = 0;  //总重
        dwWeightLimit = 0;  //限重
        bExit = false;
        sTransPayType.clear();
    }
    CWasteInfo &operator=(const CWasteInfo &b)
    {
        if (this != &b) {
            bExit = b.bExit;
            bType = b.bType;
            sCardId = b.sCardId;
            vehClass = b.vehClass;
            nVLPC = b.nVLPC;
            sVehPlate = b.sVehPlate;
            sEnStationName = b.sEnStationName;
            dwCashMoney = b.dwCashMoney;
            dwLastMoney = b.dwLastMoney;
            dwBalance = b.dwBalance;
            bIsPayByStoreCard = b.bIsPayByStoreCard;
            dtWasteTime = b.dtWasteTime;
            bTallyCard = b.bTallyCard;
            sDiscountedFree = b.sDiscountedFree;
            bSuccessed = b.bSuccessed;
            dwConsumeMoney = b.dwConsumeMoney;
            sDesc = b.sDesc;
            dwToTalWeight = b.dwToTalWeight;  //总重
            dwWeightLimit = b.dwWeightLimit;  //限重
            sTransPayType = b.sTransPayType;
        }
        return *this;
    }
};
Q_DECLARE_METATYPE(CWasteInfo)

struct CRepayMoneyInfo
{
    int nRepayType;        //补费类型
    int nTollMoney;        //补通行费
    int nCardCost;         //补卡成本
    int nOweMoney;         //欠通行费,黑名单对应追缴通行费
    int nOweCardCost;      //欠卡成本,黑名单对应追缴卡成本
    QString sRecordNo;     //欠费记录号
    int nAlreadyMoney;     //已缴通行费
    int nAlreadyCardCost;  //已缴卡成本
    QString sEnStation;
    QString sExStation;
    QString sVehPlate;
    int nVehPlateColor;
    int nEnLaneId;
    int nExLaneId;
    quint16 wVC;  //车型
public:
    CRepayMoneyInfo() { Clear(); }

    bool IsPartialRepay() const
    {
        return (nTollMoney + nCardCost) <
               (nOweMoney + nOweMoney - nAlreadyMoney - nAlreadyCardCost);
    }
    void Clear()
    {
        nRepayType = 0;
        nTollMoney = 0;
        nCardCost = 0;
        nOweMoney = 0;
        nOweCardCost = 0;
        nAlreadyMoney = 0;
        nAlreadyCardCost = 0;
        sEnStation.clear();
        sExStation.clear();
        sRecordNo.clear();
        nEnLaneId = 0;
        nExLaneId = 0;
        sVehPlate.clear();
        nVehPlateColor = 0;
        wVC = 0;
    }
};

//轴组限载
struct CAxisGroupWeightLimit
{
    //轴组类型
    quint32 axisGroup;
    //限载
    quint32 weightLimit;
};

//获取节假日免费名称
inline QString GetHolidayFreeName(qint8 bHolidayFreeType)
{
    switch (bHolidayFreeType) {
        case 1:
            return "元旦";
        case 2:
            return "省厅春节";
        case 3:
            return "法定春节";
        case 4:
            return "清明节";
        case 5:
            return "劳动节";
        case 6:
            return "端午节";
        case 7:
            return "仲秋节";
        case 8:
            return "国庆节";
        case 9:
            return "备用1";
        case 10:
            return "备用2";
        default:
            return "未知";
    }
    return "";
}
inline QString GetOBUManufacturer(quint32 dwOBUID)
{
    quint8 bOBUId[4];
    quint32 tmpOBUId = qToBigEndian(dwOBUID);
    memcpy(bOBUId, &tmpOBUId, 4);

    switch (bOBUId[0]) {
        case 0x01:
            return QString("埃特斯");
        case 0x02:
            return QString("金溢");
        case 0x03:
            return QString("聚利");
        case 0x05:
            return QString("长江智能");
        case 0x06:
            return QString("航天信息");
        case 0x07:
            return QString("千方");
        case 0x08:
            return QString("万集");
        case 0x09:
            return QString("中兴");
        case 0x0A:
            return QString("北京诚达");
        case 0x0B:
            return QString("逸海京通");
        case 0x0C:
            return QString("握奇");
        case 0x0D:
            return QString("搜林");
        default:
            return QString("ID:%1").arg(bOBUId[0], 2, 16, QLatin1Char('0'));
    }
}

inline int GetAxisNumForAxisType(int nAxisType)
{
    switch (nAxisType) {
        case AXT_A1T1:  // 1：单轴单胎；
            return 1;
        case AXT_A1T2:  // 2：单轴双胎；
            return 1;
        case AXT_A2T1:  // 3：双联轴单胎；
            return 2;
        case AXT_A2T12:  // 4：双联轴单双胎；
            return 2;
        case AXT_A2T2:  // 5：双联轴双胎；
            return 2;
        case AXT_A3T1:  // 6：三联轴单胎；
            return 3;
        case AXT_A3T2:  // 7：三联轴双胎；
            return 3;
        case AXT_A3T21:  // 8：三联轴双单胎；
            return 3;
        case AXT_A3T22:  // 9：三联轴双双胎；
            return 3;
        default:
            return 0;
    }
}

inline QString GetAxisNumForName(int nAxisType)
{
    switch (nAxisType) {
        case AXT_A1T1:  // 1：单轴单胎；
            return QObject::tr("单轴单胎");
        case AXT_A1T2:  // 2：单轴双胎；
            return QObject::tr("单轴双胎");
        case AXT_A2T1:  // 3：双联轴单胎；
            return QObject::tr("双轴单胎");
        case AXT_A2T12:  // 4：双联轴单双胎；
            return QObject::tr("双轴单双");
        case AXT_A2T2:  // 5：双联轴双胎；
            return QObject::tr("双轴双胎");
        case AXT_A3T1:  // 6：三联轴单胎；
            return QObject::tr("三轴单胎");
        case AXT_A3T2:  // 7：三联轴双胎；
            return QObject::tr("三轴双胎");
        case AXT_A3T21:  // 8：三联轴双单胎；
            return QObject::tr("单单双");
        case AXT_A3T22:  // 9：三联轴双双胎；
            return QObject::tr("单双双");
        default:
            return QObject::tr("异型轴");
    }
}

/*
功能： 专项作业车返回限载
参数： nAxisGroup 轴组类型
返回值：车辆限载重量，单位千克
*/
inline quint32 GetWeightLimitForSpecial(quint32 nAxisGroup)
{
    const quint32 aryTotalWeightStd[] = {26000, 39000, 52000, 55000, 55000};
    //计算轴数
    qint32 a = nAxisGroup;
    int nAxisNum = 0;
    while (a != 0) {
        int xx = a % 10;
        a = a / 10;
        nAxisNum += GetAxisNumForAxisType(xx);
    }

    if (nAxisNum > 6) {
        return aryTotalWeightStd[4];
    } else if (nAxisNum > 1) {
        return aryTotalWeightStd[nAxisNum - 2];
    } else {
        return aryTotalWeightStd[0];
    }
}

/*
功能： 根据轴数计算限载
参数： nAxisNum 车辆轴组类型
返回值：车辆限载重量，单位千克
*/
inline quint32 GetWeightLimit(quint32 nAxisGroup)
{
    // 分别定义轴型和整车的限载标准
    const CAxisGroupWeightLimit aryAxisWeightStd[] = {
        {11, 4500},   {12, 18000},  {122, 27000},  {14, 27000},    {15, 25000},
        {112, 25000}, {125, 36000}, {114, 36000},  {1222, 36000},  {152, 35000},
        {115, 31000}, {155, 43000}, {1125, 43000}, {1522, 43000},  {11222, 43000},
        {127, 42000}, {157, 49000}, {1255, 49000}, {11522, 49000}, {1127, 46000}};
    int nlength = sizeof(aryAxisWeightStd) / sizeof(CAxisGroupWeightLimit);

    const quint32 aryTotalWeightStd[] = {17000, 25000, 35000, 43000, 49000};

    //先按照轴组类型查询
    for (int i = 0; i < nlength; i++) {
        if (aryAxisWeightStd[i].axisGroup == nAxisGroup) {
            return aryAxisWeightStd[i].weightLimit;
        }
    }
    //计算轴数
    qint32 a = nAxisGroup;
    int sum = 0;
    while (a != 0) {
        int xx = a % 10;
        sum += GetAxisNumForAxisType(xx);
        a = a / 10;
    }
    if (sum <= 6 && sum >= 2) {
        return aryTotalWeightStd[sum - 2];
    } else {
        //超长车, 直接返回49000
        return 49000;
    }
}

#endif  // LANETYPE_H
