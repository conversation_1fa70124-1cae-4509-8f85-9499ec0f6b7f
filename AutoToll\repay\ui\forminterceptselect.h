#ifndef FORMINTERCEPTSELECT_H
#define FORMINTERCEPTSELECT_H

#include <QLabel>
#include <QPushButton>
#include <QPainter>
#include <QKeyEvent>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>

#include "../../baseopwidget.h"
#include "../../MtcKey/MtcKeyDef.h"
#include "../../globalui.h"
#include "../common/repaytypes.h"

/**
 * @brief 拦截方式选择界面
 * 用于省内名单补费时选择拦截方式（入口拦截或出口拦截）
 */
class FormInterceptSelect : public CBaseOpWidget
{
    Q_OBJECT

public:
    explicit FormInterceptSelect(QWidget *parent = 0);
    ~FormInterceptSelect();

    // 初始化界面
    virtual void InitUI(int iFlag = 0);
    
    // 简化的显示接口，符合项目规范
    bool SelectInterceptType(InterceptType defaultType = Intercept_Exit);
    
    // 获取选择结果
    InterceptType GetSelectedType() const { return m_selectedType; }
    
    // 传统接口（保持兼容）
    bool ShowInterceptSelect(InterceptType &selectedType);
    void SetDefaultSelection(InterceptType defaultType);
    void SetPromptMessage(const QString &message);

protected:
    // 重写绘制事件
    void paintEvent(QPaintEvent *event);
    
    // 重写按键事件处理
    virtual int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
    
    // 重写模态显示后的处理
    virtual void OnModalShowed();

private slots:
    // 无

private:
    // 界面初始化
    void InitControls();
    void SetupControlProperties();
    void InitLayout();
    void InitConnections();
    void InitUIConfig();
    
    // 绘制函数
    void DrawBackground(QPainter &painter);
    void DrawTitle(QPainter &painter);
    void DrawOptions(QPainter &painter);
    void DrawHelpMessage(QPainter &painter);
    
    // 选择处理
    void SetCurrentSelection(int index);
    void ConfirmSelection();
    void CancelSelection();
    void UpdateSelectionDisplay();
    void ProcessNumberKey(int number);
    
    // 输入处理
    void ProcessKey1();
    void ProcessKey2();
    void ProcessEnterKey();
    void ProcessEscapeKey();
    void ProcessUpKey();
    void ProcessDownKey();
    
    // 界面状态控制
    void SetUIEnabled(bool enabled);
    void UpdateHelpMessage();
    
    // 错误处理
    void ShowErrorMessage(const QString &message);
    void ShowSuccessMessage(const QString &message);
    void ShowWarningMessage(const QString &message);
    void ClearStatusMessage();
    
    // 数据处理
    InterceptType GetInterceptTypeByIndex(int index) const;
    int GetIndexByInterceptType(InterceptType type) const;
    QString GetInterceptTypeName(InterceptType type) const;
    QString GetInterceptTypeDescription(InterceptType type) const;

private:
    // 不再使用控件，改为自绘方式，与项目其他界面保持一致
    
    // 界面配置
    QFont m_fontTitle;
    QFont m_fontOption;
    QFont m_fontHelp;
    
    // 选择数据
    InterceptType m_selectedType;     // 选择的拦截类型
    int m_nCurrentIndex;              // 当前选择索引（0=入口拦截，1=出口拦截）
    bool m_bSelectionConfirmed;       // 是否已确认选择
    QString m_promptMessage;          // 提示信息
    
    // 界面状态
    bool m_bProcessing;               // 是否正在处理
    // 定时器相关已去除
    
    // 不需要存储颜色配置，直接使用g_GlobalUI
    
    // 布局配置
    static const int TITLE_HEIGHT = 70;           // 标题高度
    static const int PROMPT_HEIGHT = 50;          // 提示区高度
    static const int OPTION_HEIGHT = 80;          // 选项高度
    static const int STATUS_HEIGHT = 30;          // 状态区高度
    static const int HELP_HEIGHT = 60;            // 帮助区高度
    static const int MARGIN = 20;                 // 边距
    static const int OPTION_SPACING = 30;         // 选项间距
    static const int OPTION_WIDTH = 300;          // 选项宽度
    
    // 选择配置
    static const int OPTION_COUNT = 2;                 // 选项数量
    
    // 选项定义
    struct InterceptOption {
        InterceptType type;
        QString name;
        QString description;
        QString shortcut;
    };
    
    static const InterceptOption INTERCEPT_OPTIONS[OPTION_COUNT];
};

#endif // FORMINTERCEPTSELECT_H 