﻿#include "abstractstate.h"

#include <time.h>

#include <QStackedWidget>

#include "devicefactory.h"
#include "dlgmain.h"
#include "etclanectrl.h"
#include "globalutils.h"
#include "ilogmsg.h"
#include "remotecontrolmgr.h"

QMutex CAbstractState::m_Mutex;
CAbstractState *CAbstractState::m_pCurState = NULL;
QStackedWidget *CAbstractState::m_pStateUIs = NULL;

qint32 CAbstractState::m_nAutoPlateColor = -1;
qint32 CAbstractState::m_nInputPlateColor = -1;
CVehClass CAbstractState::m_autoVehClass = VC_None;
CVehClass CAbstractState::m_InputVehClass = VC_None;
CVehClass CAbstractState::m_PInputVehClass = VC_None;
QString CAbstractState::m_sAutoVehPlate = "";
QString CAbstractState::m_sInputVehPlate = "";
QString CAbstractState::m_sInputAxleType = "";
CUnionVehType CAbstractState::m_InputVehType = UVT_Normal;
CVehClass CAbstractState::m_DefVehClass = VC_None;
bool CAbstractState::m_bInputPlate = false;

bool CAbstractState::m_bDataSaveFailed = false;
QString CAbstractState::m_sCertNo = QString();
CAbstractState *CAbstractState::m_pETCState = NULL;
bool CAbstractState::m_bPause = false;

CAbstractState::CAbstractState(qint32 nStateId, QObject *parent)
    : QObject(parent), m_nStateId(nStateId)
{
    m_pStateUI = NULL;
    m_nErrorCode = 0;
    m_sError.clear();
    m_sStateName = GetStateDesc(nStateId);
    setObjectName(m_sStateName);
}

void CAbstractState::Leave()
{
    //
}

void CAbstractState::Enter() { ShowStateUI(m_pStateUI); }

bool CAbstractState::bAllowExitApp()
{
    QString sMsg = QString("%1 状态下不允许关闭系统").arg(m_sStateName);
    DebugLog(sMsg);
    return false;
}

void CAbstractState::SetStateUI(CAbstractStateUI *pStateUI)
{
    if (pStateUI) {
        m_pStateUI = pStateUI;
        m_pStateUI->SetState(this);
        // connect(this,SIGNAL(NotifyLaneStateDisplayEvent(int,QList<QVariant>,int)),m_pStateUI,SLOT(OnDisplayMsgEvent(int,QList<QVariant>,int)));
        //显示界面的提示信息
        connect(this, SIGNAL(NotifyHelpMsg(int, QString, int)), m_pStateUI,
                SLOT(OnDisplayHelpMsg(int, QString, int)));
    }
    return;
}

qint32 CAbstractState::GetCurStateId()
{
    if (m_pCurState)
        return m_pCurState->GetStateId();
    else
        return 0;
}

CAbstractState *CAbstractState::ChangeToNextState(CAbstractState *pNextState)
{
    CAbstractState *pOldState = NULL;
    {
        QMutexLocker Locker(&m_Mutex);
        if (m_pCurState) m_pCurState->Leave();
        pOldState = m_pCurState;

        m_pCurState = pNextState;
    }

    if (m_pCurState) m_pCurState->Enter();
    return pOldState;
}

QString CAbstractState::GetStateDesc(qint32 nStateId)
{
    switch (nStateId) {
        case StateID_None:
            return "";
        case StateID_VehInputEn:
            return QString("入口车辆信息输入");
        case StateID_VehInputEx:
            return QString("车辆信息输入");
        case StateID_WaitPass:
            return QString("等待车辆离开");
        case StateID_Motor:
            return QString("车队");
        case StateID_UnLogin:
            return QString("下班");
        default:
            return QString("");
    }
}

void CAbstractState::ShowStateUI(QWidget *pStateUI)
{
    if (!m_pStateUIs || !pStateUI) return;
    if (-1 == m_pStateUIs->indexOf(pStateUI)) {
        m_pStateUIs->addWidget(pStateUI);
    }
    m_pStateUIs->setCurrentWidget(pStateUI);
}

void CAbstractState::ClearTransInfo_OnFrm() { return; }

void CAbstractState::DealRsuErrorInfo(QString sPlate, QString sError, quint8 IsTradeSuc)
{
    Q_UNUSED(sPlate)
    Q_UNUSED(IsTradeSuc)
    Q_UNUSED(sError)
}

void CAbstractState::UpdateRsuTradeInfo() {}

bool CAbstractState::ProcessRsuEvent_New(int nIndex, quint32 OBUID, int nEvent, int &nErrorCode,
                                         QString &sError)
{
    Q_UNUSED(OBUID)
    Q_UNUSED(nEvent)
    Q_UNUSED(nErrorCode)
    Q_UNUSED(sError)
    return false;
}

bool CAbstractState::ProcessCardMachineEvent(int nEvent, int nPos, int nIndex)
{
    Q_UNUSED(nEvent)
    Q_UNUSED(nPos)
    Q_UNUSED(nIndex)
    return true;
}

bool CAbstractState::OnInputVehInfoByRemote(qint32 vehClass, int nPlateColor, QString sVehPlate,
                                            qint32 vehType, QString axleType)
{
    return true;
}

bool CAbstractState::OnSelectPayTypeByRemote(CKeyValue keyValue) { return true; }

bool CAbstractState::OnBackInputStateByRemote() { return true; }

void CAbstractState::OnRemoteSimulateDownBar()
{
    //交给特定的状态机处理
}

bool CAbstractState::ProcessRsuEvent(quint32 OBUID, qint32 nEvent, int &nErrorCode, QString &sError)
{
    Q_UNUSED(OBUID)
    Q_UNUSED(nEvent)
    Q_UNUSED(nErrorCode)
    Q_UNUSED(sError)
    return false;
}

void CAbstractState::DealRsuVehInfo(QString sPlate, QString sError, quint8 IsTradeSuc)
{
    m_pCurState->DealRsuErrorInfo(sPlate, sError, IsTradeSuc);
}

bool CAbstractState::OnPressCardMgrButtonEvent(int nIcPos, int nBoxIndex, QString &sError)
{
    Q_UNUSED(nIcPos)
    Q_UNUSED(nBoxIndex)
    sError = QString("%1状态下,暂不处理按键取卡").arg(m_sStateName);
    return false;
}

bool CAbstractState::OnCardMgrEvent(int nEventId, int BoxIndex, QString &sError)
{
    Q_UNUSED(BoxIndex)
    sError = QString("%1状态下,接收到卡机事件[%2]").arg(m_sStateName).arg(nEventId);
    return false;
}

int CAbstractState::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    Q_UNUSED(mtcKeyEvent);
    return 0;
}
/*
 * 功能：检测到卡片事件
 * 参数：nReadId-读写器Id nCardType -物理卡片类型 bAlarm -是否启动声光报警 sError-错误信息
 * bContinueReadCard -是否继续检测卡片
 *
 */
bool CAbstractState::OnOpenCardEvent(int nReadId, int nCardType, bool &bAlarm, QString &sError,
                                     bool &bContinueReadCard)
{
    Q_UNUSED(nReadId);
    Q_UNUSED(nCardType);
    Q_UNUSED(bAlarm)
    Q_UNUSED(sError)
    Q_UNUSED(bContinueReadCard)
    return true;
}
/*
 *处理IO输入信号
 *参数：nDI 输入设备id，bStatus true-有信号 false-无信号
 *返回值: 缺省为true表示后续ETCLaneCtrl 可以继续处理
 *   false 表示IO输入信号被拦截后续无法处理
 */
bool CAbstractState::ProcessDIEvent(qint32 nDI, bool bStatus, QString &sError)
{
    DebugLog(QString("%1 状态下接收到DI:%2 状态:%3信号")
                 .arg(GetStateDesc(m_nStateId))
                 .arg(nDI)
                 .arg(bStatus));
    Q_UNUSED(nDI);
    Q_UNUSED(bStatus);
    Q_UNUSED(sError);
    return true;
}

bool CAbstractState::PorcessDOEvent(qint32 nDO, bool bStatus, QString &sError)
{
    Q_UNUSED(nDO);
    Q_UNUSED(bStatus);
    Q_UNUSED(sError)
    return true;
}

void CAbstractState::DoViolateMenu(int nTriggerType, bool bAutoSendMsg) {}

void CAbstractState::OnQrCodeEvent(int nPos, const QString &sQrCode) { return; }

void CAbstractState::OnVPREvent(int nVC, int nColor, const QString &sPlate) { return; }

void CAbstractState::ShowMessage(const QString &sMessage, bool bRefresh)
{
    GetMainDlg()->ShowPromptMsg(sMessage);
}

bool CAbstractState::IfInputVehInfoFinished()
{
    //是否车辆信息输入完成
    return (m_InputVehClass != VC_None && m_bInputPlate);
}

/**
 * @brief 刷新提示信息
 */
void CAbstractState::RefreshMessage()
{
    emit NotifyHelpMsg(CAbstractStateUI::HelpType_Refresh, QString(), 1);
    return;
}

void CAbstractState::ShowErrorMessage(const QString &sMessage)
{
    GetMainDlg()->ShowPromptMsg(sMessage, true);
}

void CAbstractState::OnDataSaveFailedEvent()
{
    m_bDataSaveFailed = true;
    // if (m_pCurState) m_pCurState->ShowErrorMessage(QString("脱机数据保存失败,请联系维护人员"));
}

void CAbstractState::SetETCState(CAbstractState *pETCState) { m_pETCState = pETCState; }

void CAbstractState::ShowAutoVehPlate(const QString &sAutoPlate, int nAutoColor)
{
    if (sAutoPlate.length() > 0) {
        if (m_pStateUI) {
            m_pStateUI->ShowAutoVehPlate(sAutoPlate, nAutoColor);
        }
    }
}

void CAbstractState::ShowAutoVehClass(CVehClass vehClass)
{
    if (m_pStateUI) {
        m_pStateUI->ShowAutoVehClass(vehClass);
    }
}

void CAbstractState::Capture()
{
    static time_t lstCapTime = 0;
    if (1) {
        //缺省为后相机
        CVPRDev *pVpr = CDeviceFactory::GetVPRDev(DevIndex_Second);
        if (pVpr) {
            time_t curTime = time(NULL);
            if (curTime - lstCapTime < 10) return;
            lstCapTime = curTime;
            if (!pVpr->Capture()) {
                ShowErrorMessage(QString("车牌强制抓拍识别失败"));
                return;
            }
        }
    } else {
        ShowErrorMessage("无车牌识别设备");
    }
}

void CAbstractState::SetInputVehClass(CVehClass vehClass)
{
    if (m_InputVehClass != vehClass) {
    }
    m_InputVehClass = vehClass;
    if (VC_None == m_PInputVehClass) {
        m_PInputVehClass = vehClass;
    }
}

/*
    取当前的车辆信息
    如果有人工输入的信息，取人工输入车辆信息
    如果没有人工输入信息，取自动识别
    如果没有自动识别，取缺省车辆信息（前提是允许按缺省车型处理，参数配置）
    缺省车型，也应该在参数中配置，暂时按客1.
    bConfirm =true 必须人工输入
*/
void CAbstractState::GetCurVehInfo(CVehInfo &vehInfo, bool bConfirm)
{
    vehInfo.Clear();
    vehInfo.AutoVehClass = m_autoVehClass;
    if (!m_sAutoVehPlate.isEmpty()) {
        qstrncpy(vehInfo.szAutoVehPlate, UnicodetoGB2312(m_sAutoVehPlate).constData(),
                 sizeof(vehInfo.szAutoVehPlate));
    }
    vehInfo.nAutoVehPlateColor = m_nAutoPlateColor;

    if (VC_None != m_InputVehClass) {
        vehInfo.VehClass = m_InputVehClass;
        vehInfo.PVehClass = m_PInputVehClass;
        vehInfo.nVehClassWay = VehClassway_Input;
    } else if (!bConfirm) {
        if (VC_None != m_autoVehClass) {
            vehInfo.VehClass = m_autoVehClass;
            vehInfo.nVehClassWay = VehClassWay_Auto;
        } else if (VC_None != m_DefVehClass) {
            vehInfo.VehClass = m_DefVehClass;
        }
        vehInfo.PVehClass = vehInfo.VehClass;
    }

    vehInfo.GBVehType = m_InputVehType;
    vehInfo.nVehPlateColor = m_nInputPlateColor;
    if (!bConfirm) {
        if (-1 == m_nInputPlateColor) vehInfo.nVehPlateColor = m_nAutoPlateColor;
    }

    QByteArray baPlate;
    if (m_sInputVehPlate.isEmpty()) {
        if (!bConfirm)
            baPlate = UnicodetoGB2312(m_sAutoVehPlate);
        else
            baPlate = UnicodetoGB2312(m_sInputVehPlate);
    } else
        baPlate = UnicodetoGB2312(m_sInputVehPlate);

    qstrncpy(vehInfo.szVehPlate, baPlate.constData(), sizeof(vehInfo.szVehPlate));
    return;
}

void CAbstractState::SetInputVehType(CUnionVehType vehType) { m_InputVehType = vehType; }

void CAbstractState::SetCertNo(const QString &sCertNo) { m_sCertNo = sCertNo; }

void CAbstractState::SetInputAxleType(const QString &axleType) { m_sInputAxleType = axleType; }

void CAbstractState::SetInputVehPlate(const QString &sVehPlate, int nVLPColor)
{
    m_sInputVehPlate = sVehPlate;
    m_nInputPlateColor = nVLPColor;
    m_bInputPlate = true;
}

void CAbstractState::SetAutoVehPlate(const QString &sAutoVehPlate, int nVlpColor)
{
    m_sAutoVehPlate = sAutoVehPlate;
    m_sInputVehPlate = sAutoVehPlate;
    m_nAutoPlateColor = nVlpColor;
    m_nInputPlateColor = nVlpColor;

    GetCurState()->ShowAutoVehPlate(sAutoVehPlate, nVlpColor);
    return;
}

void CAbstractState::SetAutoVehClass(CVehClass vehClass) { m_autoVehClass = vehClass; }

void CAbstractState::ClearVehInfo(bool bClearUI)
{
    m_autoVehClass = VC_None;
    m_nAutoPlateColor = -1;
    m_sAutoVehPlate.clear();
    m_sCertNo.clear();
    ClearInputVeh(bClearUI);
    //
    Ptr_RemoteCtrl->RefreshVehInput();
}

void CAbstractState::ClearInputVeh(bool bClearUI)
{
    m_InputVehClass = VC_None;
    m_PInputVehClass = VC_None;
    m_sInputAxleType.clear();
    m_sInputVehPlate.clear();
    m_nInputPlateColor = -1;
    m_InputVehType = UVT_Normal;
    m_bInputPlate = false;
    if (bClearUI)
        if (m_pCurState) m_pCurState->ClearTransInfo_OnFrm();
}

bool CAbstractState::bHaveInputVLP() { return m_bInputPlate; }

void CAbstractState::GetAutoVehPlate(QString &sAutoVehPlate, int &nVlpColor)
{
    sAutoVehPlate = m_sAutoVehPlate;
    nVlpColor = m_nAutoPlateColor;
}

void CAbstractState::GetInputVehPlate(QString &sPlate, int &nVlpColor)
{
    sPlate = m_sInputVehPlate;
    nVlpColor = m_nInputPlateColor;
}

QString CAbstractState::GetCertNo() { return m_sCertNo; }

CAbstractStateUI::CAbstractStateUI(QWidget *parent) : CBaseOpWidget(parent)
{
    connect(this, SIGNAL(NotifyLaneStateDisplayEvent(int, QList<QVariant>, int)), this,
            SLOT(OnDisplayMsgEvent(int, QList<QVariant>, int)));
}

void CAbstractStateUI::ShowAutoVehClass(CVehClass vehClass) { Q_UNUSED(vehClass) }

void CAbstractStateUI::ClearAllVehInfo(bool bClearEntryInfo) {}

void CAbstractStateUI::ShowAutoVehPlate(const QString &sPlate, int nVLPColo)
{
    Q_UNUSED(sPlate)
    Q_UNUSED(nVLPColo)
}

void CAbstractStateUI::ShowSpeEventStatus(bool bVisible, const QString &sMsg)
{
    Q_UNUSED(bVisible)
    Q_UNUSED(sMsg)
}

void CAbstractStateUI::OnDisplayMsgEvent(int nType, QList<QVariant> ParamList, int nParam1) {}

void CAbstractStateUI::OnDisplayHelpMsg(int nType, QString sMsg, int nParam1)
{
    switch (nType) {
        case HelpType_Refresh:
            this->RefreshMessage();
            break;
        case HelpType_SetMsg:
            this->SetMessage(sMsg, nParam1);
            break;
        case HelpType_ErrorMsg:
            GetMainDlg()->ShowPromptMsg(sMsg, true);
            break;
        case HelpType_SetTitle:
            this->SetTitle(sMsg);
            break;
        default:
            break;
    }
    return;
}
