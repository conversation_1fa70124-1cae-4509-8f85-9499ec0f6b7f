﻿#ifndef FUNCMENU_H
#define FUNCMENU_H

#include "cfarecalctypes.h"
#include "cmonreqmgr.h"
#include "listdlg.h"

#define UNLOGINMENU_COUNT 3
#define SETITEM_COUNT 5
#define RSUITEM_COUNT 4
#define WEIGHITEM_COUNT 6
#define OTHERXMENU_COUNT 10
#define COLORMENU_COUNT 7
#define CLASSMENU_COUNT 18
#define APPEXIT_CONUT 4

enum FunMenu_Item
{
    FunMenu_None = 0,
    FunMenu_Features,
    FunMenu_Maintenance,
    FunMenu_System,
    FunMenu_End,

    FunMenu_ExportData,
    FunMenu_SetInfo,
    FunMenu_ImporeParam,
    FunMenu_SetRSUPower,
    FunMenu_ParamVersionDetail,
    FunMenu_SetRSUModel,
    FunMenu_WasteDetail,
    FunMenu_BarrierUp,    //手动抬杆
    FunMenu_BarrierDown,  //手动落杆

    FunMenu_ReConnectReader,  //
    FunMenu_ReconnectVLP,
    FunMenu_ParamDownLoad,     //参数下载
    FunMenu_RSU,               //天线
    FunMenu_Setting,           // 1 设置
    FunMenu_ReGranCard,        // 8 补卡
    FunMenu_EditerWeigh,       // 4 编辑称重信息
    FunMenu_USelectStation,    // U型车选站
    FunMenu_UCarReduce,        // 5 U型通行费减免
    FunMenu_ReConnectPrinter,  //
    FunMenu_ChangeBills,
    FunMenu_ChangeCardBox,
    FunMenu_ProcessLongCar,  // 10 超长车处理
    FunMenu_DevTest,         //
    FunMenu_EditFlag,        //编辑标识站
    FunMenu_ReconnectMobilePay

    /*
    FunMenu_ReversCar,          // 2 倒车
    FunMenu_SysWorkParamSele,   // 3 系统工作参数查询
    FunMenu_UCarReduce,         // 5 U型通行费减免
    FunMenu_VehLeadOut,         // 6 车辆引出
    FunMenu_VehRedo,            // 7 车辆重新处理
    FunMenu_ParamVerInfo,       // 9 参数版本信息查询
    FunMenu_ICCardTransInfo,    // 11 鲁通卡交易信息
    FunMenu_MobilePayTran,      //12 移动支付人工放行
    FunMenu_BigTruck,           //13大件货车
    FunMenu_ProvinceAccount,    //14跨省计费
    */
};

class CFuncMenu
{
public:
    enum
    {
        Violate_LoopTrigger,  //线圈触发
        Violate_ViolateKey
    };

public:
    CFuncMenu();
    ~CFuncMenu();

private:
    //生成其他免征菜单项
    void GetOtherXParamMenu(int nLaneType, bool bPayState, QList<CListData> &MenuList);
    //生成车牌颜色菜单项
    void GetVehPlateColorParamMenu(QList<CListData> &MenuList);
    //生成车型菜单项
    void GetVehClassParamMenu(QList<CListData> &MenuList);
    //生成下班功能菜单项
    void GetUnlogFuncMenuItems(QList<CListData> &MenuList);

    void GetMaintenanceMenuItems(QList<CListData> &MenuList);
    void GetFeaturesMenuItems(QList<CListData> &MenuList);
    //生成编辑称重信息菜单项
    void GetEditWeightList(QList<CListData> &MenuList);
    //生成设置菜单项
    void GetSettingItems(const QList<bool> &Statuslist, QList<CListData> &MenuList);
    //生成RSU菜单项
    void GetRSUItems(const QList<bool> &Statuslist, QList<CListData> &MenuList);
    //取设置菜单状态
    QList<bool> GetSettingItemsStatus();
    //取RSU菜单状态
    QList<bool> GetRSUItemsStatus();
    //根据设置菜单返回结果，设置对应标志
    int SetSettingStatus(int nIndex);
    int SetRSUStatus(int nIndex);
    //取当前第一个可用菜单索引值
    int GetListFirstTrue(QList<CListData> *list);

    //初始化功能菜单
    void InitLoginFuncMenu();
    //生成功能菜单菜单项
    void CreateLoginFunMenus(QList<CListData> &MenuItems);
    //根据功能菜单Id获取对应事件Id
    int GetEventByMenuId(int nMenuId, CMonReqID &monReqId);
    int ReConnReader();
    //设置菜单处理
    int DoSettingMenu();
    int DoRSUMenu();
    void UpDateFunMenuStatusByStateId(int nStateId);
    void UpdateLoginFunMenuItemValue(int nIndex, bool bValue);
    bool DoRoleAuth(int nLaneEvent);
    void ShowParamVersionDetail();

public:
    //更新功能菜单的状态
    void UpdateLoginFunMenuItemStatus(int nItemId, bool bEnable);
    //下班状态下的功能菜单
    void ShowUnloginFuncMenu();
    //上班状态下显示功能菜单
    int DoLoginFuncMenu();
    //功能菜单
    void DoFeaturesFuncMenu();
    //维护菜单
    void DoMaintenanceFuncMenu();

    //其他免征车
    //    CVehType ShowOtherX(CVehType VehType, const QString& sMsg,int
    //    nLaneType=LaneType_MtcEntry,bool bPayState=false);
    //显示车牌颜色菜单
    VP_COLOR ShowVehPlateColor();
    //

    static CFuncMenu *GetFunMenu(int nStateId = 0);
    //
    static int DoRepayMenu();
    static int DoViolateMenu(int nViolateType);
    static bool IsViolateMenuShow() { return m_bViolateMenuShow; }
    //打印失败菜单
    static int DoPrintFailedMenu();
    //绿通菜单
    //    static CVehType DoLvTongMenu();
    // U型菜单
    static int DoUSelectMenu(const QString &sTitle);

    CVehClass ShowVehClass();

public:
    //未登录状态下的功能菜单
    void ShowMenu_UnLogin();
    //改轴菜单（计重）
    void ShowMenu_ChangeAxle();
    //显示选择货6的菜单
    CVehClass ShowMenu_SelTruck6();
    static int ShowMenu_OtherFree(CUnionVehType &vehType, bool bExit, bool bOnlyTruck = false);
    static CEntryQryResult *ShowMenu_qryEntryStation(QList<CEntryQryResult> &entryList);
    static int ShowMenu_PrePayBList();
    static int ShowMenu_Reprint();

private:
    QVector<CListData> m_FunMenuItems;
    static bool m_bViolateMenuShow;
    bool IsETCShow;
    bool IsETCClose;
};

//获取登录后功能菜单名称
QString GetLogFunMenuItemName(qint8 nItemId, bool bIsUse);

//获取登录后功能菜单名称
QString GetUnLogItemName(qint8 nItemId);
QString GetEditWeightItems(qint8 nItemId);

QString GetMaintenanceItemName(qint8 nItemId);
QString GetFeaturesItemName(qint8 nItemId);

// 设置菜单子项---bIsUse是是否使用
QString GetSettingMenuItems(qint8 nItemId, bool bIsUse);
// RSU菜单子项---bIsUse是否使用
QString GetRSUMenuItems(qint8 nItemId, bool bIsUse);

// 设置"车牌颜色"子菜单
QString GetVehPlateColorMenuItems(qint8 nItemId);
//设置"车型"子菜单
QString GetVehClassMenuItems(qint8 nItemId);
// 设置"编辑称重信息"子菜单
QString GetWeightMenuItems(qint8 nItemId);
//导出未上传数据
void ExportData();
//导入参数
void ImportParam();
//查询流水情况
void ShowWasteInfo();
void SetRsuPower();
void SetRsuModel();
class ImportParamDlg : public CBaseOpWidget
{
    Q_OBJECT
public:
    int ImportParam();

protected:
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);
private slots:
    void SelectFileInfo();

private:
    QLineEdit *m_pText;
    QLineEdit *m_pTextCode;
    QPushButton *m_pBtn;
};
//设置天线功率
class RSUPowerDlg : public CBaseOpWidget
{
    Q_OBJECT
public:
    int GetRSUPower();

protected:
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);

private:
    QLineEdit *m_pText;
};
//设置天线模式
class RSUModelDlg : public CBaseOpWidget
{
    Q_OBJECT
public:
    int GetRSUModel();

protected:
    int mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent);

private:
    QComboBox *m_pComboBox;
};
#endif  // FUNCMENU_H
