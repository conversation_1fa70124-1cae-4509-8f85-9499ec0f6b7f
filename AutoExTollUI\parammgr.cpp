#include "parammgr.h"

#include <QSettings>

#include "globalutils.h"

ParamMgr g_ParamMgr;

ParamMgr::ParamMgr(QObject *parent) : QObject(parent) {}

/**
 * @brief ParamMgr::Init
 * 加载参数
 */
void ParamMgr::Init() {
    QString IniFileName = GetCurrentPath() + "autoextollui.ini";
    QSettings uiConfig(IniFileName, QSettings::IniFormat);

    //网络端口号
    m_UIServerPort = uiConfig.value("UIServer/port", 18765).toInt();
}
